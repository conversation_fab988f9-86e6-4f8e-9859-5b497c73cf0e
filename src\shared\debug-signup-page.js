// 调试Microsoft注册页面的脚本
// 在浏览器控制台中运行此脚本来检查页面状态

console.log('=== Microsoft注册页面调试工具 ===');

// 1. 检查当前页面URL
console.log('1. 当前页面URL:', window.location.href);

// 2. 检查页面标题
console.log('2. 页面标题:', document.title);

// 3. 检查所有可能的邮箱输入框
console.log('3. 检查邮箱输入框:');
const emailSelectors = [
  'input[type="email"][name="电子邮件"]',
  'input[type="email"]',
  'input[name="MemberName"]',
  'input[name="email"]',
  'input[placeholder*="邮件"]',
  'input[placeholder*="email"]',
  'input[id*="email"]',
  'input[id*="Email"]'
];

emailSelectors.forEach((selector, index) => {
  const element = document.querySelector(selector);
  console.log(`  ${index + 1}. ${selector}:`, element);
  if (element) {
    console.log(`     - value: "${element.value}"`);
    console.log(`     - placeholder: "${element.placeholder}"`);
    console.log(`     - name: "${element.name}"`);
    console.log(`     - id: "${element.id}"`);
  }
});

// 4. 检查所有可能的提交按钮
console.log('4. 检查提交按钮:');
const buttonSelectors = [
  'button[type="submit"][data-testid="primaryButton"]',
  'button[type="submit"]',
  'button[data-testid="primaryButton"]',
  'input[type="submit"]',
  'button:contains("下一个")',
  'button:contains("下一步")',
  'button:contains("Next")',
  'button:contains("继续")'
];

buttonSelectors.forEach((selector, index) => {
  const element = document.querySelector(selector);
  console.log(`  ${index + 1}. ${selector}:`, element);
  if (element) {
    console.log(`     - textContent: "${element.textContent}"`);
    console.log(`     - disabled: ${element.disabled}`);
    console.log(`     - type: "${element.type}"`);
  }
});

// 5. 检查所有按钮（更广泛的搜索）
console.log('5. 所有按钮元素:');
const allButtons = document.querySelectorAll('button');
allButtons.forEach((button, index) => {
  console.log(`  按钮 ${index + 1}:`, {
    text: button.textContent.trim(),
    type: button.type,
    disabled: button.disabled,
    className: button.className,
    id: button.id,
    testId: button.getAttribute('data-testid')
  });
});

// 6. 检查所有输入框
console.log('6. 所有输入框元素:');
const allInputs = document.querySelectorAll('input');
allInputs.forEach((input, index) => {
  console.log(`  输入框 ${index + 1}:`, {
    type: input.type,
    name: input.name,
    id: input.id,
    placeholder: input.placeholder,
    value: input.value,
    className: input.className
  });
});

// 7. 检查页面文本内容
console.log('7. 页面主要文本内容:');
const bodyText = document.body.textContent || document.body.innerText;
console.log('页面文本预览:', bodyText.substring(0, 500));

// 8. 检查是否有错误信息
console.log('8. 检查错误信息:');
const errorSelectors = [
  '.error',
  '.alert',
  '.warning',
  '[role="alert"]',
  '.field-validation-error',
  '.validation-error'
];

errorSelectors.forEach((selector, index) => {
  const elements = document.querySelectorAll(selector);
  if (elements.length > 0) {
    console.log(`  错误类型 ${index + 1} (${selector}):`, elements);
    elements.forEach((el, i) => {
      console.log(`    错误 ${i + 1}: "${el.textContent.trim()}"`);
    });
  }
});

// 9. 检查表单状态
console.log('9. 检查表单:');
const forms = document.querySelectorAll('form');
forms.forEach((form, index) => {
  console.log(`  表单 ${index + 1}:`, {
    action: form.action,
    method: form.method,
    id: form.id,
    className: form.className
  });
});

// 10. 检查验证码相关元素
console.log('10. 检查验证码相关元素:');
const codeSelectors = [
  '[data-testid="codeEntry"]',
  '#codeEntry-0',
  '#codeEntry-1',
  'input[maxlength="1"]',
  '.code-input',
  '.verification-code'
];

codeSelectors.forEach((selector, index) => {
  const element = document.querySelector(selector);
  console.log(`  ${index + 1}. ${selector}:`, element);
});

// 11. 检查页面是否包含验证相关文本
console.log('11. 检查验证相关文本:');
const verificationTexts = [
  '验证你的电子邮件',
  '验证你的电子邮件地址',
  '输入我们发送到你的电子邮件地址的代码',
  'Verify your email',
  'Enter the code we sent to your email',
  '安全代码',
  'security code',
  'verification code'
];

verificationTexts.forEach((text, index) => {
  const found = bodyText.includes(text);
  console.log(`  ${index + 1}. "${text}": ${found ? '✅ 找到' : '❌ 未找到'}`);
});

// 12. 模拟填写邮箱的函数
function testEmailInput(email = '<EMAIL>') {
  console.log('12. 测试邮箱填写:');
  
  // 尝试所有可能的邮箱输入框选择器
  const emailSelectors = [
    'input[type="email"][name="电子邮件"]',
    'input[type="email"]',
    'input[name="MemberName"]',
    'input[name="email"]'
  ];
  
  for (const selector of emailSelectors) {
    const emailInput = document.querySelector(selector);
    if (emailInput) {
      console.log(`  找到邮箱输入框: ${selector}`);
      console.log(`  当前值: "${emailInput.value}"`);
      
      // 尝试填写
      emailInput.value = email;
      emailInput.dispatchEvent(new Event('input', { bubbles: true }));
      emailInput.dispatchEvent(new Event('change', { bubbles: true }));
      
      console.log(`  填写后的值: "${emailInput.value}"`);
      return emailInput;
    }
  }
  
  console.log('  ❌ 未找到邮箱输入框');
  return null;
}

// 13. 模拟点击下一步按钮的函数
function testNextButton() {
  console.log('13. 测试下一步按钮:');
  
  const buttonSelectors = [
    'button[type="submit"][data-testid="primaryButton"]',
    'button[type="submit"]',
    'button[data-testid="primaryButton"]'
  ];
  
  for (const selector of buttonSelectors) {
    const button = document.querySelector(selector);
    if (button) {
      console.log(`  找到按钮: ${selector}`);
      console.log(`  按钮文本: "${button.textContent}"`);
      console.log(`  是否禁用: ${button.disabled}`);
      
      if (button.textContent.includes('下一个') || button.textContent.includes('Next')) {
        console.log(`  ✅ 这是下一步按钮`);
        return button;
      }
    }
  }
  
  console.log('  ❌ 未找到下一步按钮');
  return null;
}

// 提供手动测试函数
window.debugSignup = {
  testEmailInput,
  testNextButton,
  fillEmail: (email = '<EMAIL>') => {
    const input = testEmailInput(email);
    if (input) {
      console.log('邮箱填写成功');
    }
  },
  clickNext: () => {
    const button = testNextButton();
    if (button && !button.disabled) {
      button.click();
      console.log('已点击下一步按钮');
    } else {
      console.log('按钮不可用或被禁用');
    }
  }
};

console.log('=== 调试完成 ===');
console.log('可以使用以下命令进行手动测试:');
console.log('- debugSignup.fillEmail("<EMAIL>") // 填写邮箱');
console.log('- debugSignup.clickNext() // 点击下一步');
