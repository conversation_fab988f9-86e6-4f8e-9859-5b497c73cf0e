/**
 * Microsoft注册系统 v3.0
 * 完全重写的微软账号注册自动化系统
 * 
 * <AUTHOR> Team
 * @version 3.0.0
 * @description 基于状态机的阶段化处理系统，完全替换旧版本
 */

// 导入所有必要的组件
// 注意：在实际使用时，这些文件需要按正确顺序加载

/**
 * 全局注册系统管理器
 * 统一管理整个注册流程的生命周期
 */
class MicrosoftRegistrationSystemV3 {
  constructor() {
    this.controller = null;
    this.isInitialized = false;
    this.version = '3.0.0';
    this.systemStatus = 'idle';
    
    // 绑定消息监听器
    this.setupMessageListeners();
  }

  /**
   * 初始化系统
   */
  async initialize() {
    if (this.isInitialized) {
      console.log('⚠️ 系统已初始化');
      return true;
    }

    try {
      console.log('🚀 初始化Microsoft注册系统 v3.0...');
      
      // 检查依赖
      if (!this.checkDependencies()) {
        throw new Error('系统依赖检查失败');
      }

      // 创建控制器
      this.controller = new MicrosoftRegistrationController();
      
      // 设置系统事件监听
      this.setupSystemEventListeners();
      
      this.isInitialized = true;
      this.systemStatus = 'ready';
      
      console.log('✅ Microsoft注册系统初始化完成');
      
      // 通知系统就绪
      this.notifySystemReady();
      
      return true;

    } catch (error) {
      console.error('❌ 系统初始化失败:', error);
      this.systemStatus = 'error';
      return false;
    }
  }

  /**
   * 检查系统依赖
   */
  checkDependencies() {
    const requiredClasses = [
      'MicrosoftRegistrationStateMachine',
      'MicrosoftRegistrationController',
      'PageDetector',
      'DataPermissionHandler',
      'EmailInputHandler',
      'EmailVerificationHandler',
      'VerificationCodeHandler',
      'PersonalInfoHandler',
      'NameInfoHandler',
      'CaptchaHandler',
      'LoginCompleteHandler',
      'RewardsWelcomeHandler',
      'ErrorHandler'
    ];

    const missing = [];
    
    for (const className of requiredClasses) {
      if (typeof window[className] === 'undefined') {
        missing.push(className);
      }
    }

    if (missing.length > 0) {
      console.error('❌ 缺少必要的类:', missing);
      return false;
    }

    console.log('✅ 系统依赖检查通过');
    return true;
  }

  /**
   * 设置系统事件监听器
   */
  setupSystemEventListeners() {
    // 监听页面卸载事件
    window.addEventListener('beforeunload', () => {
      this.cleanup();
    });

    // 监听页面可见性变化
    document.addEventListener('visibilitychange', () => {
      this.handleVisibilityChange();
    });

    // 监听错误事件
    window.addEventListener('error', (event) => {
      this.handleGlobalError(event);
    });
  }

  /**
   * 设置消息监听器
   */
  setupMessageListeners() {
    // 监听来自popup的消息
    chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
      this.handleMessage(request, sender, sendResponse);
      return true; // 保持消息通道开放
    });
  }

  /**
   * 处理消息
   */
  async handleMessage(request, sender, sendResponse) {
    try {
      switch (request.action) {
        case 'startRegistration':
          const startResult = await this.startRegistration();
          sendResponse({ success: startResult });
          break;

        case 'stopRegistration':
          const stopResult = await this.stopRegistration();
          sendResponse({ success: stopResult });
          break;

        case 'getStatus':
          const status = this.getSystemStatus();
          sendResponse({ success: true, status });
          break;

        case 'resetSystem':
          const resetResult = await this.resetSystem();
          sendResponse({ success: resetResult });
          break;

        default:
          sendResponse({ success: false, error: '未知的操作' });
      }
    } catch (error) {
      console.error('❌ 消息处理失败:', error);
      sendResponse({ success: false, error: error.message });
    }
  }

  /**
   * 启动注册流程
   */
  async startRegistration() {
    if (!this.isInitialized) {
      const initialized = await this.initialize();
      if (!initialized) {
        return false;
      }
    }

    if (this.systemStatus === 'running') {
      console.log('⚠️ 注册流程已在运行');
      return true;
    }

    try {
      console.log('🚀 启动注册流程...');
      this.systemStatus = 'running';
      
      await this.controller.start();
      
      console.log('✅ 注册流程启动成功');
      return true;

    } catch (error) {
      console.error('❌ 启动注册流程失败:', error);
      this.systemStatus = 'error';
      return false;
    }
  }

  /**
   * 停止注册流程
   */
  async stopRegistration() {
    if (!this.controller || this.systemStatus !== 'running') {
      console.log('⚠️ 注册流程未在运行');
      return true;
    }

    try {
      console.log('🛑 停止注册流程...');
      
      await this.controller.stop();
      this.systemStatus = 'ready';
      
      console.log('✅ 注册流程停止成功');
      return true;

    } catch (error) {
      console.error('❌ 停止注册流程失败:', error);
      return false;
    }
  }

  /**
   * 重置系统
   */
  async resetSystem() {
    try {
      console.log('🔄 重置系统...');
      
      // 停止当前流程
      if (this.controller) {
        await this.controller.stop();
      }

      // 清理资源
      this.cleanup();

      // 重新初始化
      this.isInitialized = false;
      this.systemStatus = 'idle';
      
      const initialized = await this.initialize();
      
      console.log('✅ 系统重置完成');
      return initialized;

    } catch (error) {
      console.error('❌ 系统重置失败:', error);
      return false;
    }
  }

  /**
   * 获取系统状态
   */
  getSystemStatus() {
    const baseStatus = {
      version: this.version,
      isInitialized: this.isInitialized,
      systemStatus: this.systemStatus,
      timestamp: new Date().toISOString()
    };

    if (this.controller) {
      const controllerStatus = this.controller.getStatus();
      return {
        ...baseStatus,
        controller: controllerStatus
      };
    }

    return baseStatus;
  }

  /**
   * 处理页面可见性变化
   */
  handleVisibilityChange() {
    if (document.hidden) {
      console.log('📱 页面隐藏，暂停检测');
      // 可以在这里暂停某些操作
    } else {
      console.log('📱 页面显示，恢复检测');
      // 可以在这里恢复操作
    }
  }

  /**
   * 处理全局错误
   */
  handleGlobalError(event) {
    console.error('🚨 全局错误:', event.error);
    
    // 如果是系统相关错误，尝试恢复
    if (this.controller && event.error.message.includes('Microsoft')) {
      this.controller.stateMachine.handleError(event.error);
    }
  }

  /**
   * 通知系统就绪
   */
  notifySystemReady() {
    // 发送消息给background script
    try {
      chrome.runtime.sendMessage({
        action: 'systemReady',
        version: this.version,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      // 忽略消息发送失败
    }

    // 触发自定义事件
    window.dispatchEvent(new CustomEvent('microsoftRegistrationSystemReady', {
      detail: {
        version: this.version,
        system: this
      }
    }));
  }

  /**
   * 清理资源
   */
  cleanup() {
    console.log('🧹 清理系统资源...');
    
    if (this.controller) {
      this.controller.stop();
    }

    // 清理事件监听器
    // 注意：这里只清理我们添加的监听器
  }

  /**
   * 获取系统信息
   */
  getSystemInfo() {
    return {
      name: 'Microsoft Registration System',
      version: this.version,
      author: 'RTBS Team',
      description: '基于状态机的阶段化处理系统',
      features: [
        '严格的阶段控制',
        '智能页面检测',
        '可靠的IMAP服务',
        '自动错误恢复',
        '资源管理',
        '状态持久化'
      ]
    };
  }
}

// 创建全局系统实例
let microsoftRegistrationSystemV3 = null;

/**
 * 初始化系统
 */
function initializeMicrosoftRegistrationV3() {
  if (microsoftRegistrationSystemV3) {
    console.log('⚠️ 系统已存在');
    return microsoftRegistrationSystemV3;
  }

  console.log('🔧 创建Microsoft注册系统 v3.0...');
  microsoftRegistrationSystemV3 = new MicrosoftRegistrationSystemV3();
  
  // 自动初始化
  microsoftRegistrationSystemV3.initialize();
  
  return microsoftRegistrationSystemV3;
}

/**
 * 获取系统实例
 */
function getMicrosoftRegistrationSystemV3() {
  if (!microsoftRegistrationSystemV3) {
    return initializeMicrosoftRegistrationV3();
  }
  return microsoftRegistrationSystemV3;
}

// 自动初始化（如果在Microsoft相关页面）
if (location.href.includes('microsoft.com') || 
    location.href.includes('live.com') || 
    location.href.includes('outlook.com')) {
  
  console.log('🎯 检测到Microsoft页面，自动初始化系统...');
  
  // 等待DOM加载完成
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeMicrosoftRegistrationV3);
  } else {
    initializeMicrosoftRegistrationV3();
  }
}

// 导出到全局
window.MicrosoftRegistrationSystemV3 = MicrosoftRegistrationSystemV3;
window.initializeMicrosoftRegistrationV3 = initializeMicrosoftRegistrationV3;
window.getMicrosoftRegistrationSystemV3 = getMicrosoftRegistrationSystemV3;
