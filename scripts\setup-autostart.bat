@echo off
echo Setting up Microsoft Registration System v3.0 for auto-start...

set STARTUP_FOLDER=%APPDATA%\Microsoft\Windows\Start Menu\Programs\Startup
set CURRENT_DIR=%CD%
set SHORTCUT_NAME=Microsoft Registration v3.0 AutoStart

echo Creating auto-start shortcut in: %STARTUP_FOLDER%

powershell -Command "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%STARTUP_FOLDER%\%SHORTCUT_NAME%.lnk'); $Shortcut.TargetPath = '%CURRENT_DIR%\scripts\smart-start.bat'; $Shortcut.WorkingDirectory = '%CURRENT_DIR%'; $Shortcut.Description = 'Microsoft Registration System v3.0 - Auto Start'; $Shortcut.WindowStyle = 7; $Shortcut.Save()"

if exist "%STARTUP_FOLDER%\%SHORTCUT_NAME%.lnk" (
    echo Auto-start setup completed successfully!
    echo The system will now start automatically when Windows boots.
    echo.
    echo To disable auto-start, delete the shortcut from:
    echo %STARTUP_FOLDER%
) else (
    echo Failed to setup auto-start.
)

echo.
echo WARNING: Auto-start will run the IMAP server on every boot.
echo Make sure this is what you want before enabling.
echo.
pause
