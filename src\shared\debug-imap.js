// 调试IMAP服务器
const Imap = require('imap');
const { simpleParser } = require('mailparser');
const fs = require('fs');

// 加载IMAP配置
const config = JSON.parse(fs.readFileSync('imap-config.json', 'utf8'));

console.log('IMAP配置:', config);

const imap = new Imap(config);

imap.once('ready', () => {
  console.log('IMAP连接成功');
  
  imap.openBox('INBOX', false, (err, box) => {
    if (err) {
      console.error('打开收件箱失败:', err);
      return;
    }
    
    console.log(`收件箱包含 ${box.messages.total} 封邮件`);
    
    // 搜索所有邮件
    const searchCriteria = ['ALL'];

    console.log('搜索所有邮件...');
    
    imap.search(searchCriteria, (err, results) => {
      if (err) {
        console.error('搜索失败:', err);
        return;
      }
      
      console.log(`找到 ${results.length} 封邮件`);
      
      if (results.length === 0) {
        console.log('没有找到最近的邮件');
        imap.end();
        return;
      }
      
      const fetch = imap.fetch(results, { bodies: '' });
      
      fetch.on('message', (msg, seqno) => {
        console.log(`\n=== 处理邮件 ${seqno} ===`);
        
        msg.on('body', (stream, info) => {
          simpleParser(stream, (err, parsed) => {
            if (err) {
              console.error('解析邮件失败:', err);
              return;
            }
            
            console.log('邮件主题:', parsed.subject);
            console.log('发件人:', parsed.from?.text);
            console.log('收件人:', parsed.to?.text);
            console.log('日期:', parsed.date);
            
            // 检查是否包含Microsoft相关内容
            const allContent = [parsed.text, parsed.html].filter(Boolean).join(' ');
            const isMicrosoftEmail = parsed.from && parsed.from.text.toLowerCase().includes('microsoft');
            const hasVerificationKeywords = allContent.toLowerCase().includes('验证') || 
                                           allContent.toLowerCase().includes('verification') ||
                                           allContent.toLowerCase().includes('安全代码') ||
                                           allContent.toLowerCase().includes('security code');
            
            console.log('是否Microsoft邮件:', isMicrosoftEmail);
            console.log('是否包含验证关键词:', hasVerificationKeywords);
            
            if (parsed.text) {
              console.log('纯文本内容预览:', parsed.text.substring(0, 200));
            }
            
            if (parsed.html) {
              console.log('HTML内容预览:', parsed.html.substring(0, 200));
            }
            
            // 尝试提取验证码
            const codePatterns = [
              /安全代码[:\s]*(\d{6})/i,
              /验证码[:\s]*(\d{6})/i,
              /verification code[:\s]*(\d{6})/i,
              /security code[:\s]*(\d{6})/i,
              /<span[^>]*>(\d{6})<\/span>/i,
              /(\d{6})/g
            ];
            
            for (const content of [parsed.text, parsed.html].filter(Boolean)) {
              for (let i = 0; i < codePatterns.length; i++) {
                const pattern = codePatterns[i];
                const match = content.match(pattern);
                if (match && match[1]) {
                  const code = match[1];
                  if (/^\d{6}$/.test(code)) {
                    console.log(`找到6位数字: ${code} (正则 ${i + 1})`);
                    
                    // 检查上下文
                    if (i === codePatterns.length - 1) {
                      const contextBefore = content.substring(Math.max(0, match.index - 100), match.index);
                      const hasColonBefore = contextBefore.includes(':') || contextBefore.includes('：');
                      const isMicrosoftContext = content.toLowerCase().includes('microsoft');
                      
                      if (hasColonBefore && isMicrosoftContext) {
                        console.log(`✅ 可能的验证码: ${code}`);
                      }
                    } else {
                      console.log(`✅ 验证码: ${code}`);
                    }
                  }
                }
              }
            }
          });
        });
      });
      
      fetch.once('end', () => {
        console.log('\n邮件处理完成');
        imap.end();
      });
    });
  });
});

imap.once('error', (err) => {
  console.error('IMAP错误:', err);
});

imap.once('end', () => {
  console.log('IMAP连接已关闭');
  process.exit(0);
});

console.log('正在连接IMAP服务器...');
imap.connect();
