@echo off
echo.
echo ========================================
echo   Microsoft Registration System v3.0
echo            File Checker
echo ========================================
echo.

set MISSING_FILES=0
set TOTAL_FILES=0

echo Checking required files...
echo.

REM Check core files
echo [Core Files]
call :check_file "src\extension\manifest.json"
call :check_file "src\extension\content.js"
call :check_file "src\extension\popup.js"
call :check_file "src\extension\popup.html"
call :check_file "src\extension\background.js"

echo.
echo [v3.0 New Files]
call :check_file "src\extension\microsoft-registration-state-machine.js"
call :check_file "src\extension\microsoft-registration-handlers.js"
call :check_file "src\extension\remaining-stage-handlers.js"
call :check_file "src\extension\page-detector.js"
call :check_file "src\extension\microsoft-registration-controller.js"
call :check_file "src\extension\error-handling-system.js"
call :check_file "src\extension\microsoft-registration-v3.js"

echo.
echo [IMAP Service Files]
call :check_file "src\server\imap-server.js"
call :check_file "src\server\enhanced-imap-service.js"

echo.
echo [Documentation and Test Files]
call :check_file "docs\DEPLOYMENT_GUIDE_V3.md"
call :check_file "tests\test-v3-system.js"

echo.
echo [Optional Files]
if exist ".env" (
    echo [OK] .env ^(optional^)
) else (
    echo [SKIP] .env ^(optional, not configured^)
)
call :check_optional_file "config\imap-config.json"

echo.
echo ========================================
echo Check Results Summary
echo ========================================

if %MISSING_FILES%==0 (
    echo [OK] All required files exist ^(%TOTAL_FILES%/%TOTAL_FILES%^)
    echo System is ready to upgrade to v3.0!
) else (
    echo [ERROR] Missing %MISSING_FILES% required files
    echo Please ensure all files are correctly placed
)

echo.
echo Next Steps:
if %MISSING_FILES%==0 (
    echo 1. Run upgrade-to-v3.bat to complete upgrade
    echo 2. Configure .env file ^(if not configured yet^)
    echo 3. Start IMAP server
    echo 4. Reload browser extension
    echo 5. Run tests to verify functionality
) else (
    echo 1. Download and place missing files
    echo 2. Re-run this check script
    echo 3. Confirm all files before upgrading
)

echo.
pause
goto :eof

:check_file
set /a TOTAL_FILES+=1
if exist "%~1" (
    echo [OK] %~1
) else (
    echo [MISSING] %~1
    set /a MISSING_FILES+=1
)
goto :eof

:check_optional_file
if exist "%~1" (
    echo [OK] %~1 ^(optional^)
) else (
    echo [SKIP] %~1 ^(optional, not configured^)
)
goto :eof
