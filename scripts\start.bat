@echo off
chcp 65001 >nul
title RTBS - Real-Time Browser Service

echo.
echo ========================================
echo   RTBS - Real-Time Browser Service
echo   Version 2.0.0
echo ========================================
echo.

:: Check if Node.js is installed
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] Node.js is not installed or not in PATH
    echo Please install Node.js from https://nodejs.org/
    pause
    exit /b 1
)

:: Check if package.json exists
if not exist "package.json" (
    echo [ERROR] package.json not found
    echo Please run this script from the project root directory
    pause
    exit /b 1
)

:: Install dependencies if node_modules doesn't exist
if not exist "node_modules" (
    echo [INFO] Installing dependencies...
    npm install
    if %errorlevel% neq 0 (
        echo [ERROR] Failed to install dependencies
        pause
        exit /b 1
    )
    echo [SUCCESS] Dependencies installed successfully
    echo.
)

:: Check if .env file exists
if not exist ".env" (
    echo [WARNING] .env file not found
    echo [INFO] Creating .env file from template...
    if exist "config\.env.template" (
        copy "config\.env.template" ".env" >nul
        echo [SUCCESS] .env file created from template
        echo [WARNING] Please edit .env file with your actual configuration
        echo.
    ) else (
        echo [ERROR] .env template not found
        echo Please create .env file manually
        pause
        exit /b 1
    )
)

:: Create logs directory if it doesn't exist
if not exist "logs" (
    mkdir logs
    echo [INFO] Created logs directory
)

:: Start the IMAP server
echo [INFO] Starting RTBS IMAP Server...
echo [INFO] Server will be available at http://localhost:3000
echo [INFO] Press Ctrl+C to stop the server
echo.
echo ========================================
echo   Server Starting...
echo ========================================
echo.

node src/server/imap-server.js

:: If we get here, the server has stopped
echo.
echo ========================================
echo   Server Stopped
echo ========================================
echo.
pause
