{"name": "mailsplit", "version": "5.4.0", "description": "Split email messages into an object stream", "main": "index.js", "directories": {"test": "test"}, "scripts": {"test": "grunt"}, "author": "<PERSON><PERSON>", "license": "(MIT OR EUPL-1.1+)", "dependencies": {"libbase64": "1.2.1", "libmime": "5.2.0", "libqp": "2.0.1"}, "devDependencies": {"eslint": "8.29.0", "eslint-config-nodemailer": "1.2.0", "eslint-config-prettier": "8.5.0", "grunt": "1.5.3", "grunt-cli": "1.4.3", "grunt-contrib-nodeunit": "4.0.0", "grunt-eslint": "24.0.1", "random-message": "1.1.0"}, "files": ["lib", "index.js"], "repository": {"type": "git", "url": "https://github.com/andris9/mailsplit.git"}}