// RTBS - 后台脚本

// 创建文件名
function createFileName(data) {
  const account = data.userAccount || '未检测到账号';
  const progress = data.searchDaysInfo ? data.searchDaysInfo.progress : '未检测到天数';

  const safeProgress = progress.replace(/\//g, '#');
  const safeAccount = account.replace(/[<>:"/\\|?*]/g, '_');
  const filename = `${safeAccount}---${safeProgress}.txt`;

  return filename;
}

// 获取当天文件夹名称
function getTodayFolder() {
  const now = new Date();
  const month = now.getMonth() + 1;
  const day = now.getDate();
  const todayFolder = `${month}月${day}日`;

  return {
    folder: todayFolder,
    actualPath: `Downloads\\RTBS\\${todayFolder}\\`
  };
}

// 创建文件到当天文件夹
async function createFile(filename) {
  try {
    const folderInfo = getTodayFolder();
    const targetPath = `RTBS/${folderInfo.folder}/${filename}`;

    // 检查文件是否已存在
    const existingFiles = await chrome.downloads.search({
      filename: targetPath,
      state: 'complete'
    });

    if (existingFiles.length > 0) {
      return { success: true, message: '文件已存在，跳过创建', skipped: true };
    }

    // 创建空文件
    const dataUrl = 'data:text/plain;charset=utf-8,';

    chrome.downloads.download({
      url: dataUrl,
      filename: targetPath,
      saveAs: false,
      conflictAction: 'uniquify'
    }, (downloadId) => {
      if (chrome.runtime.lastError) {
        // 备用下载方式
        chrome.downloads.download({
          url: dataUrl,
          filename: `RTBS-${folderInfo.folder}-${filename}`,
          saveAs: false
        });
      }
    });

    return { success: true, message: `文件已创建到 ${folderInfo.folder} 文件夹` };
  } catch (error) {
    return { success: false, error: error.message };
  }
}

// 保存到本地存储
async function saveToStorage(data) {
  try {
    const key = `log_${Date.now()}`;
    await chrome.storage.local.set({ [key]: data });
  } catch (error) {
    // 保存失败时静默处理
  }
}

// 监听来自content script的消息
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  if (message.action === 'dataDetected') {
    // 检查是否有有效数据
    const hasAccount = message.userAccount && message.userAccount !== '未检测到账号';
    const hasSearchDays = message.searchDaysInfo && message.searchDaysInfo.progress;

    if (!hasAccount || !hasSearchDays) {
      sendResponse({
        success: true,
        message: '没有有效数据，跳过保存',
        skipped: true,
        shouldCloseTab: false // 没有有效数据时不关闭标签页
      });
      return;
    }

    // 异步处理消息
    (async () => {
      try {
        const filename = createFileName(message);
        const result = await createFile(filename);

        if (result.skipped) {
          sendResponse({
            success: true,
            message: result.message,
            skipped: true,
            shouldCloseTab: false // 跳过时不关闭标签页
          });
          return;
        }

        // 保存到本地存储作为备份
        await saveToStorage({
          filename: filename,
          timestamp: new Date().toISOString(),
          action: message.action,
          url: message.url,
          userAccount: message.userAccount,
          searchDaysInfo: message.searchDaysInfo
        });

        const folderInfo = getTodayFolder();
        const actualPath = `Downloads\\RTBS\\${folderInfo.folder}\\${filename}`;

        sendResponse({
          success: true,
          filename: filename,
          message: `文件已创建到: ${actualPath}`,
          actualPath: actualPath,
          shouldCloseTab: true // 指示应该关闭标签页
        });

        // 10秒后关闭标签页
        setTimeout(() => {
          if (sender.tab && sender.tab.id) {
            chrome.tabs.remove(sender.tab.id);
          }
        }, 10000);

      } catch (error) {
        sendResponse({
          success: false,
          error: error.message
        });
      }
    })();

    return true; // 保持消息通道开放以支持异步响应
  }

  // 处理获取当天文件夹的请求
  if (message.action === 'getTodayFolder') {
    try {
      const result = getTodayFolder();
      sendResponse(result);
    } catch (error) {
      sendResponse({ error: error.message });
    }
    return true;
  }

  // 处理获取统计信息的请求
  if (message.action === 'getStats') {
    (async () => {
      try {
        const result = await chrome.storage.local.get(null);
        const logs = Object.values(result).filter(item => item.filename);

        const stats = {
          totalRecords: logs.length,
          lastRecord: logs.length > 0 ? logs[logs.length - 1] : null,
          accounts: [...new Set(logs.map(log => log.userAccount).filter(Boolean))],
          recentLogs: logs.slice(-5)
        };

        sendResponse({ success: true, stats: stats });
      } catch (error) {
        sendResponse({ success: false, error: error.message });
      }
    })();

    return true;
  }

  // 处理关闭哔哩标签页的请求
  if (message.action === 'closeBiliTab') {
    if (sender.tab && sender.tab.id) {
      chrome.tabs.remove(sender.tab.id);
    }
    return true;
  }

  // 处理Microsoft账号保存请求
  if (message.action === 'saveMicrosoftAccount') {
    (async () => {
      try {
        // 提取账号名（去掉@s4464.cfd部分）
        const accountName = message.account.replace('@s4464.cfd', '');
        const filename = `${accountName}.txt`;

        // 创建账号信息内容
        const accountInfo = `Microsoft账号注册信息
账号: ${message.account}
注册时间: ${message.timestamp}
注册状态: ${message.registrationComplete ? '完成' : '进行中'}
`;

        // 创建到AutoRL文件夹
        const targetPath = `RTBS/AutoRL/${filename}`;

        // 检查文件是否已存在
        const existingFiles = await chrome.downloads.search({
          filename: targetPath,
          state: 'complete'
        });

        if (existingFiles.length > 0) {
          sendResponse({
            success: true,
            message: '账号文件已存在，跳过创建',
            skipped: true,
            filename: filename
          });
          return;
        }

        // 创建包含账号信息的文件
        const dataUrl = 'data:text/plain;charset=utf-8,' + encodeURIComponent(accountInfo);

        chrome.downloads.download({
          url: dataUrl,
          filename: targetPath,
          saveAs: false,
          conflictAction: 'uniquify'
        }, (downloadId) => {
          if (chrome.runtime.lastError) {
            // 备用下载方式
            chrome.downloads.download({
              url: dataUrl,
              filename: `RTBS-AutoRL-${filename}`,
              saveAs: false
            });
          }
        });

        // 保存到本地存储作为备份
        await saveToStorage({
          filename: filename,
          timestamp: message.timestamp,
          action: 'saveMicrosoftAccount',
          account: message.account,
          registrationComplete: message.registrationComplete
        });

        const actualPath = `Downloads\\RTBS\\AutoRL\\${filename}`;

        sendResponse({
          success: true,
          filename: filename,
          message: `Microsoft账号文件已创建到: ${actualPath}`,
          actualPath: actualPath
        });

      } catch (error) {
        console.error('保存Microsoft账号失败:', error);
        sendResponse({
          success: false,
          error: error.message
        });
      }
    })();

    return true;
  }

  return false;
});

// 扩展安装时的初始化
chrome.runtime.onInstalled.addListener((details) => {
  // 静默初始化，无需日志输出
});
