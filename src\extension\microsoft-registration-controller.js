/**
 * Microsoft注册控制器
 * 统一管理整个注册流程
 * 
 * <AUTHOR> Team
 * @version 3.0.0
 * @description 基于状态机的注册流程控制器
 */

/**
 * Microsoft注册控制器类
 * 负责协调状态机和各个阶段处理器
 */
class MicrosoftRegistrationController {
  constructor() {
    this.stateMachine = new MicrosoftRegistrationStateMachine();
    this.pageDetector = new PageDetector();
    this.isRunning = false;
    this.detectionInterval = null;
    this.detectionIntervalTime = 2000; // 2秒检测一次
    
    this.initializeHandlers();
    this.setupEventListeners();
  }

  /**
   * 初始化所有阶段处理器
   */
  initializeHandlers() {
    console.log('🔧 初始化阶段处理器...');
    
    // 注册所有阶段处理器
    this.stateMachine.registerStageHandler(
      REGISTRATION_STAGES.DATA_PERMISSION, 
      new DataPermissionHandler()
    );
    
    this.stateMachine.registerStageHandler(
      REGISTRATION_STAGES.EMAIL_INPUT, 
      new EmailInputHandler()
    );
    
    this.stateMachine.registerStageHandler(
      REGISTRATION_STAGES.EMAIL_VERIFICATION, 
      new EmailVerificationHandler()
    );
    
    this.stateMachine.registerStageHandler(
      REGISTRATION_STAGES.VERIFICATION_CODE, 
      new VerificationCodeHandler()
    );
    
    this.stateMachine.registerStageHandler(
      REGISTRATION_STAGES.PERSONAL_INFO, 
      new PersonalInfoHandler()
    );
    
    this.stateMachine.registerStageHandler(
      REGISTRATION_STAGES.NAME_INFO, 
      new NameInfoHandler()
    );
    
    this.stateMachine.registerStageHandler(
      REGISTRATION_STAGES.CAPTCHA, 
      new CaptchaHandler()
    );

    console.log('✅ 所有阶段处理器初始化完成');
  }

  /**
   * 设置事件监听器
   */
  setupEventListeners() {
    // 监听状态机事件
    this.stateMachine.addListener((event, data) => {
      this.handleStateMachineEvent(event, data);
    });

    // 监听页面变化
    this.setupPageChangeListeners();
  }

  /**
   * 设置页面变化监听器
   */
  setupPageChangeListeners() {
    // 监听URL变化
    let lastUrl = location.href;
    new MutationObserver(() => {
      const url = location.href;
      if (url !== lastUrl) {
        lastUrl = url;
        this.handlePageChange();
      }
    }).observe(document, { subtree: true, childList: true });

    // 监听DOM变化
    new MutationObserver(() => {
      if (this.isRunning) {
        this.handleDOMChange();
      }
    }).observe(document.body, { 
      subtree: true, 
      childList: true, 
      attributes: true 
    });
  }

  /**
   * 启动注册流程
   */
  async start() {
    if (this.isRunning) {
      console.log('⚠️ 注册流程已在运行中');
      return;
    }

    console.log('🚀 启动Microsoft注册流程...');
    this.isRunning = true;

    try {
      // 重置状态机
      await this.stateMachine.reset();
      
      // 设置开始时间
      this.stateMachine.context.startTime = new Date().toISOString();
      
      // 开始页面检测
      this.startPageDetection();
      
      // 检测当前页面并开始相应阶段
      const currentStage = this.pageDetector.detectCurrentStage();
      if (currentStage && currentStage !== REGISTRATION_STAGES.IDLE) {
        console.log(`🎯 检测到当前阶段: ${currentStage}`);
        await this.stateMachine.transitionTo(currentStage);
      } else {
        console.log('🔍 等待检测到有效的注册页面...');
      }

    } catch (error) {
      console.error('❌ 启动注册流程失败:', error);
      await this.stateMachine.handleError(error);
    }
  }

  /**
   * 停止注册流程
   */
  async stop() {
    if (!this.isRunning) {
      console.log('⚠️ 注册流程未在运行');
      return;
    }

    console.log('🛑 停止Microsoft注册流程...');
    this.isRunning = false;

    // 停止页面检测
    this.stopPageDetection();

    // 重置状态机
    await this.stateMachine.reset();

    console.log('✅ 注册流程已停止');
  }

  /**
   * 开始页面检测
   */
  startPageDetection() {
    if (this.detectionInterval) {
      clearInterval(this.detectionInterval);
    }

    this.detectionInterval = setInterval(() => {
      this.performPageDetection();
    }, this.detectionIntervalTime);

    console.log('🔍 页面检测已启动');
  }

  /**
   * 停止页面检测
   */
  stopPageDetection() {
    if (this.detectionInterval) {
      clearInterval(this.detectionInterval);
      this.detectionInterval = null;
      console.log('🔍 页面检测已停止');
    }
  }

  /**
   * 执行页面检测
   */
  async performPageDetection() {
    if (!this.isRunning) return;

    try {
      const detectedStage = this.pageDetector.detectCurrentStage();
      const currentStage = this.stateMachine.getCurrentStage();

      if (detectedStage && detectedStage !== currentStage) {
        console.log(`🔄 页面检测: ${currentStage} -> ${detectedStage}`);
        
        // 检查是否是有效的阶段转换
        if (this.stateMachine.isValidTransition(currentStage, detectedStage)) {
          await this.stateMachine.transitionTo(detectedStage);
        } else {
          console.log(`⚠️ 无效的阶段转换，忽略: ${currentStage} -> ${detectedStage}`);
        }
      }
    } catch (error) {
      console.error('❌ 页面检测失败:', error);
    }
  }

  /**
   * 处理状态机事件
   */
  handleStateMachineEvent(event, data) {
    console.log(`📢 状态机事件: ${event}`, data);

    switch (event) {
      case 'stageChanged':
        this.handleStageChanged(data);
        break;
      case 'error':
        this.handleError(data);
        break;
    }

    // 通知UI更新
    this.notifyUIUpdate();
  }

  /**
   * 处理阶段变化
   */
  handleStageChanged(data) {
    console.log(`🔄 阶段变化: ${data.from} -> ${data.to}`);
    
    // 保存状态到本地存储
    this.saveState();
    
    // 如果到达完成状态，停止流程
    if (data.to === REGISTRATION_STAGES.COMPLETED) {
      this.handleRegistrationComplete();
    }
  }

  /**
   * 处理注册完成
   */
  async handleRegistrationComplete() {
    console.log('🎉 注册流程完成！');
    
    const context = this.stateMachine.getContext();
    
    // 保存账号信息
    if (context.account) {
      await this.saveAccountInfo(context.account);
    }
    
    // 停止流程
    await this.stop();
  }

  /**
   * 保存账号信息
   */
  async saveAccountInfo(account) {
    try {
      // 发送消息给background script保存文件
      chrome.runtime.sendMessage({
        action: 'saveMicrosoftAccount',
        account: account,
        timestamp: new Date().toISOString(),
        registrationComplete: true
      }, (response) => {
        if (response && response.success) {
          console.log('✅ 账号信息保存成功:', response.filename);
        } else {
          console.error('❌ 账号信息保存失败:', response?.error);
        }
      });
    } catch (error) {
      console.error('❌ 保存账号信息失败:', error);
    }
  }

  /**
   * 处理错误
   */
  handleError(data) {
    console.error('❌ 注册流程错误:', data);
    
    // 可以在这里实现错误恢复逻辑
    // 比如重试、回退到之前的阶段等
  }

  /**
   * 处理页面变化
   */
  handlePageChange() {
    console.log('📄 页面URL变化，重新检测...');
    if (this.isRunning) {
      // 立即执行一次检测
      setTimeout(() => this.performPageDetection(), 500);
    }
  }

  /**
   * 处理DOM变化
   */
  handleDOMChange() {
    // 这里可以实现更精细的DOM变化处理
    // 比如检测特定元素的出现或消失
  }

  /**
   * 保存状态到本地存储
   */
  saveState() {
    try {
      const state = {
        currentStage: this.stateMachine.getCurrentStage(),
        context: this.stateMachine.getContext(),
        timestamp: new Date().toISOString()
      };
      
      localStorage.setItem('msRegistrationState', JSON.stringify(state));
    } catch (error) {
      console.error('❌ 保存状态失败:', error);
    }
  }

  /**
   * 从本地存储恢复状态
   */
  loadState() {
    try {
      const savedState = localStorage.getItem('msRegistrationState');
      if (savedState) {
        const state = JSON.parse(savedState);
        console.log('🔄 恢复保存的状态:', state);
        return state;
      }
    } catch (error) {
      console.error('❌ 加载状态失败:', error);
    }
    return null;
  }

  /**
   * 通知UI更新
   */
  notifyUIUpdate() {
    // 发送消息给popup更新显示
    try {
      chrome.runtime.sendMessage({
        action: 'updateRegistrationStatus',
        stage: this.stateMachine.getCurrentStage(),
        context: this.stateMachine.getContext()
      });
    } catch (error) {
      // 忽略消息发送失败（可能popup未打开）
    }
  }

  /**
   * 获取当前状态
   */
  getStatus() {
    return {
      isRunning: this.isRunning,
      currentStage: this.stateMachine.getCurrentStage(),
      context: this.stateMachine.getContext()
    };
  }
}

// 导出控制器
if (typeof module !== 'undefined' && module.exports) {
  module.exports = MicrosoftRegistrationController;
} else {
  // 浏览器环境
  window.MicrosoftRegistrationController = MicrosoftRegistrationController;
}
