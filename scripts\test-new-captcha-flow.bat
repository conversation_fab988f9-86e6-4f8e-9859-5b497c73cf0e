@echo off
chcp 65001 >nul
echo ========================================
echo    New CAPTCHA Flow Test Script
echo ========================================
echo.

echo Starting new captcha flow test...
echo.

echo Opening test page in default browser...
start "" "..\tests\test-new-captcha-flow.html"

echo.
echo Test page opened successfully!
echo.
echo Instructions:
echo 1. The test page will open in your default browser
echo 2. Test the accessibility challenge button detection
echo 3. Test the "press again" button detection  
echo 4. Test the complete verification flow
echo 5. Check the log output for detailed information
echo.
echo Features being tested:
echo - Detection of accessibility challenge button with SVG icon
echo - Detection of "press again" button with specific IDs and classes
echo - Complete two-step verification flow
echo - 4-second wait after "press again" button click
echo - Verification result checking
echo.

pause
