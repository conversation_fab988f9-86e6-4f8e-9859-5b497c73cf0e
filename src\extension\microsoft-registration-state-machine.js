/**
 * Microsoft注册状态机架构
 * 完全重写的阶段化处理系统
 * 
 * <AUTHOR> Team
 * @version 3.0.0
 * @description 基于状态机的严格阶段控制系统
 */

/**
 * 注册阶段定义
 * 每个阶段都有明确的职责和转换条件
 */
const REGISTRATION_STAGES = {
  IDLE: 'idle',                           // 空闲状态
  DATA_PERMISSION: 'data_permission',     // 数据许可页面
  EMAIL_INPUT: 'email_input',             // 邮箱输入页面
  EMAIL_VERIFICATION: 'email_verification', // 邮件验证等待页面
  VERIFICATION_CODE: 'verification_code', // 验证码输入页面
  PERSONAL_INFO: 'personal_info',         // 个人信息页面
  NAME_INFO: 'name_info',                 // 姓名信息页面
  CAPTCHA: 'captcha',                     // 人机验证页面
  LOGIN_COMPLETE: 'login_complete',       // 登录完成页面
  REWARDS_WELCOME: 'rewards_welcome',     // 奖励欢迎页面
  COMPLETED: 'completed',                 // 注册完成
  ERROR: 'error'                          // 错误状态
};

/**
 * 阶段转换规则
 * 定义每个阶段可以转换到的下一个阶段
 */
const STAGE_TRANSITIONS = {
  [REGISTRATION_STAGES.IDLE]: [REGISTRATION_STAGES.DATA_PERMISSION],
  [REGISTRATION_STAGES.DATA_PERMISSION]: [REGISTRATION_STAGES.EMAIL_INPUT],
  [REGISTRATION_STAGES.EMAIL_INPUT]: [REGISTRATION_STAGES.EMAIL_VERIFICATION],
  [REGISTRATION_STAGES.EMAIL_VERIFICATION]: [REGISTRATION_STAGES.VERIFICATION_CODE],
  [REGISTRATION_STAGES.VERIFICATION_CODE]: [REGISTRATION_STAGES.PERSONAL_INFO],
  [REGISTRATION_STAGES.PERSONAL_INFO]: [REGISTRATION_STAGES.NAME_INFO],
  [REGISTRATION_STAGES.NAME_INFO]: [REGISTRATION_STAGES.CAPTCHA],
  [REGISTRATION_STAGES.CAPTCHA]: [REGISTRATION_STAGES.LOGIN_COMPLETE],
  [REGISTRATION_STAGES.LOGIN_COMPLETE]: [REGISTRATION_STAGES.REWARDS_WELCOME],
  [REGISTRATION_STAGES.REWARDS_WELCOME]: [REGISTRATION_STAGES.COMPLETED],
  [REGISTRATION_STAGES.COMPLETED]: [],
  [REGISTRATION_STAGES.ERROR]: [REGISTRATION_STAGES.IDLE] // 错误状态可以重置到空闲
};

/**
 * 阶段处理器接口
 * 每个阶段必须实现这些方法
 */
class StageHandler {
  constructor(stageName) {
    this.stageName = stageName;
    this.isActive = false;
    this.resources = new Set(); // 跟踪需要清理的资源
  }

  /**
   * 进入阶段时的操作
   * @param {Object} context - 上下文信息
   * @returns {Promise<boolean>} - 是否成功进入
   */
  async onEnter(context) {
    console.log(`🚀 进入阶段: ${this.stageName}`);
    this.isActive = true;
    return true;
  }

  /**
   * 阶段执行操作
   * @param {Object} context - 上下文信息
   * @returns {Promise<boolean>} - 是否执行成功
   */
  async execute(context) {
    console.log(`⚡ 执行阶段: ${this.stageName}`);
    return true;
  }

  /**
   * 退出阶段时的清理操作
   * @param {Object} context - 上下文信息
   * @returns {Promise<boolean>} - 是否成功退出
   */
  async onExit(context) {
    console.log(`🏁 退出阶段: ${this.stageName}`);
    await this.cleanup();
    this.isActive = false;
    return true;
  }

  /**
   * 检查是否可以进入下一阶段
   * @param {Object} context - 上下文信息
   * @returns {boolean} - 是否可以进入下一阶段
   */
  canProceed(context) {
    return true;
  }

  /**
   * 清理资源
   */
  async cleanup() {
    for (const resource of this.resources) {
      try {
        if (typeof resource.cleanup === 'function') {
          await resource.cleanup();
        } else if (typeof resource === 'function') {
          resource();
        }
      } catch (error) {
        console.error(`清理资源失败:`, error);
      }
    }
    this.resources.clear();
  }

  /**
   * 添加需要清理的资源
   * @param {*} resource - 资源对象或清理函数
   */
  addResource(resource) {
    this.resources.add(resource);
  }
}

/**
 * Microsoft注册状态机
 * 核心状态管理和阶段控制
 */
class MicrosoftRegistrationStateMachine {
  constructor() {
    this.currentStage = REGISTRATION_STAGES.IDLE;
    this.previousStage = null;
    this.stageHandlers = new Map();
    this.context = {
      account: null,
      attempts: 0,
      maxAttempts: 3,
      startTime: null,
      errors: [],
      metadata: {}
    };
    this.listeners = new Set();
    this.isTransitioning = false;
    
    this.initializeStageHandlers();
  }

  /**
   * 初始化阶段处理器
   */
  initializeStageHandlers() {
    // 这里将在后续实现具体的阶段处理器
    console.log('🔧 初始化阶段处理器...');
  }

  /**
   * 注册阶段处理器
   * @param {string} stage - 阶段名称
   * @param {StageHandler} handler - 处理器实例
   */
  registerStageHandler(stage, handler) {
    this.stageHandlers.set(stage, handler);
    console.log(`📝 注册阶段处理器: ${stage}`);
  }

  /**
   * 转换到指定阶段
   * @param {string} targetStage - 目标阶段
   * @param {Object} additionalContext - 额外的上下文信息
   * @returns {Promise<boolean>} - 是否转换成功
   */
  async transitionTo(targetStage, additionalContext = {}) {
    if (this.isTransitioning) {
      console.warn(`⚠️ 正在转换中，忽略转换请求: ${targetStage}`);
      return false;
    }

    // 验证转换是否合法
    if (!this.isValidTransition(this.currentStage, targetStage)) {
      console.error(`❌ 非法的阶段转换: ${this.currentStage} -> ${targetStage}`);
      return false;
    }

    this.isTransitioning = true;
    
    try {
      // 合并上下文
      this.context = { ...this.context, ...additionalContext };
      
      // 退出当前阶段
      const currentHandler = this.stageHandlers.get(this.currentStage);
      if (currentHandler && currentHandler.isActive) {
        const exitSuccess = await currentHandler.onExit(this.context);
        if (!exitSuccess) {
          console.error(`❌ 退出阶段失败: ${this.currentStage}`);
          return false;
        }
      }

      // 更新阶段状态
      this.previousStage = this.currentStage;
      this.currentStage = targetStage;

      // 进入新阶段
      const newHandler = this.stageHandlers.get(targetStage);
      if (newHandler) {
        const enterSuccess = await newHandler.onEnter(this.context);
        if (!enterSuccess) {
          console.error(`❌ 进入阶段失败: ${targetStage}`);
          // 回滚到之前的阶段
          this.currentStage = this.previousStage;
          return false;
        }

        // 执行阶段操作
        const executeSuccess = await newHandler.execute(this.context);
        if (!executeSuccess) {
          console.error(`❌ 执行阶段失败: ${targetStage}`);
          return false;
        }
      }

      // 通知监听器
      this.notifyListeners('stageChanged', {
        from: this.previousStage,
        to: this.currentStage,
        context: this.context
      });

      console.log(`✅ 阶段转换成功: ${this.previousStage} -> ${this.currentStage}`);
      return true;

    } catch (error) {
      console.error(`❌ 阶段转换异常:`, error);
      this.context.errors.push({
        stage: targetStage,
        error: error.message,
        timestamp: new Date().toISOString()
      });
      return false;
    } finally {
      this.isTransitioning = false;
    }
  }

  /**
   * 验证阶段转换是否合法
   * @param {string} fromStage - 源阶段
   * @param {string} toStage - 目标阶段
   * @returns {boolean} - 是否合法
   */
  isValidTransition(fromStage, toStage) {
    const allowedTransitions = STAGE_TRANSITIONS[fromStage] || [];
    return allowedTransitions.includes(toStage) || toStage === REGISTRATION_STAGES.ERROR;
  }

  /**
   * 获取当前阶段
   * @returns {string} - 当前阶段
   */
  getCurrentStage() {
    return this.currentStage;
  }

  /**
   * 获取上下文信息
   * @returns {Object} - 上下文对象
   */
  getContext() {
    return { ...this.context };
  }

  /**
   * 添加事件监听器
   * @param {Function} listener - 监听器函数
   */
  addListener(listener) {
    this.listeners.add(listener);
  }

  /**
   * 移除事件监听器
   * @param {Function} listener - 监听器函数
   */
  removeListener(listener) {
    this.listeners.delete(listener);
  }

  /**
   * 通知所有监听器
   * @param {string} event - 事件名称
   * @param {*} data - 事件数据
   */
  notifyListeners(event, data) {
    for (const listener of this.listeners) {
      try {
        listener(event, data);
      } catch (error) {
        console.error('监听器执行失败:', error);
      }
    }
  }

  /**
   * 重置状态机
   */
  async reset() {
    console.log('🔄 重置状态机...');
    
    // 清理当前阶段
    const currentHandler = this.stageHandlers.get(this.currentStage);
    if (currentHandler && currentHandler.isActive) {
      await currentHandler.onExit(this.context);
    }

    // 重置状态
    this.currentStage = REGISTRATION_STAGES.IDLE;
    this.previousStage = null;
    this.isTransitioning = false;
    this.context = {
      account: null,
      attempts: 0,
      maxAttempts: 3,
      startTime: null,
      errors: [],
      metadata: {}
    };

    console.log('✅ 状态机重置完成');
  }

  /**
   * 处理错误
   * @param {Error} error - 错误对象
   * @param {string} stage - 发生错误的阶段
   */
  async handleError(error, stage = null) {
    console.error(`❌ 处理错误:`, error);
    
    this.context.errors.push({
      stage: stage || this.currentStage,
      error: error.message,
      timestamp: new Date().toISOString()
    });

    // 转换到错误状态
    await this.transitionTo(REGISTRATION_STAGES.ERROR, {
      lastError: error.message
    });
  }
}

// 导出
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    REGISTRATION_STAGES,
    STAGE_TRANSITIONS,
    StageHandler,
    MicrosoftRegistrationStateMachine
  };
} else {
  // 浏览器环境 - 将类和常量直接暴露到全局作用域
  window.REGISTRATION_STAGES = REGISTRATION_STAGES;
  window.STAGE_TRANSITIONS = STAGE_TRANSITIONS;
  window.StageHandler = StageHandler;
  window.MicrosoftRegistrationStateMachine = MicrosoftRegistrationStateMachine;

  // 同时保留命名空间对象
  window.MicrosoftRegistrationStateMachineModule = {
    REGISTRATION_STAGES,
    STAGE_TRANSITIONS,
    StageHandler,
    MicrosoftRegistrationStateMachine
  };
}
