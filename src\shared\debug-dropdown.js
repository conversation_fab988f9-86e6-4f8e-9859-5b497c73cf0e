// 下拉菜单调试脚本
// 用于测试和调试Microsoft注册页面的月份和日期下拉框选择

console.log('=== 下拉菜单调试脚本启动 ===');

// 调试函数：分析页面上的下拉框结构
function analyzeDropdowns() {
  console.log('\n1. 分析页面上的下拉框结构:');
  
  // 查找月份下拉框
  const monthDropdown = document.querySelector('button[name="BirthMonth"]');
  if (monthDropdown) {
    console.log('✓ 找到月份下拉框:');
    console.log('  - 元素:', monthDropdown);
    console.log('  - 当前值:', monthDropdown.getAttribute('value'));
    console.log('  - aria-label:', monthDropdown.getAttribute('aria-label'));
    console.log('  - 文本内容:', monthDropdown.textContent.trim());
  } else {
    console.log('✗ 未找到月份下拉框');
  }
  
  // 查找日期下拉框
  const dayDropdown = document.querySelector('button[name="BirthDay"]');
  if (dayDropdown) {
    console.log('✓ 找到日期下拉框:');
    console.log('  - 元素:', dayDropdown);
    console.log('  - 当前值:', dayDropdown.getAttribute('value'));
    console.log('  - aria-label:', dayDropdown.getAttribute('aria-label'));
    console.log('  - 文本内容:', dayDropdown.textContent.trim());
  } else {
    console.log('✗ 未找到日期下拉框');
  }
}

// 调试函数：测试月份选择
function testMonthSelection(targetMonth = 6) {
  console.log(`\n2. 测试月份选择 (目标月份: ${targetMonth}):`)
  
  const monthDropdown = document.querySelector('button[name="BirthMonth"]');
  if (!monthDropdown) {
    console.log('✗ 未找到月份下拉框');
    return;
  }
  
  console.log('✓ 点击月份下拉框...');
  monthDropdown.click();
  
  setTimeout(() => {
    console.log('✓ 下拉框已展开，查找选项...');
    
    // 输出所有可见的选项
    const allOptions = document.querySelectorAll('[role="option"]');
    console.log(`找到 ${allOptions.length} 个选项:`);
    allOptions.forEach((option, index) => {
      console.log(`  选项 ${index + 1}: "${option.textContent.trim()}" (data-value: ${option.getAttribute('data-value')})`);
    });
    
    // 尝试多种方式查找目标月份
    let monthOption = null;
    const selectors = [
      `[role="option"][data-value="${targetMonth}"]`,
      `[role="option"]:nth-child(${targetMonth})`,
      `[aria-label*="${targetMonth}月"]`
    ];
    
    for (const selector of selectors) {
      monthOption = document.querySelector(selector);
      if (monthOption) {
        console.log(`✓ 通过选择器找到月份选项: ${selector}`);
        break;
      }
    }
    
    // 通过文本内容查找
    if (!monthOption) {
      for (const option of allOptions) {
        if (option.textContent.includes(`${targetMonth}月`) || 
            option.textContent.trim() === targetMonth.toString()) {
          monthOption = option;
          console.log(`✓ 通过文本内容找到月份选项: "${option.textContent.trim()}"`);
          break;
        }
      }
    }
    
    if (monthOption) {
      console.log('✓ 点击月份选项...');
      monthOption.click();
      
      setTimeout(() => {
        const updatedDropdown = document.querySelector('button[name="BirthMonth"]');
        console.log('✓ 月份选择完成，当前值:', updatedDropdown ? updatedDropdown.textContent.trim() : '未知');
      }, 300);
    } else {
      console.log('✗ 未找到目标月份选项');
    }
  }, 500);
}

// 调试函数：测试日期选择
function testDaySelection(targetDay = 15) {
  console.log(`\n3. 测试日期选择 (目标日期: ${targetDay}):`)
  
  const dayDropdown = document.querySelector('button[name="BirthDay"]');
  if (!dayDropdown) {
    console.log('✗ 未找到日期下拉框');
    return;
  }
  
  console.log('✓ 点击日期下拉框...');
  dayDropdown.click();
  
  setTimeout(() => {
    console.log('✓ 下拉框已展开，查找选项...');
    
    // 输出所有可见的选项
    const allOptions = document.querySelectorAll('[role="option"]');
    console.log(`找到 ${allOptions.length} 个选项:`);
    allOptions.forEach((option, index) => {
      console.log(`  选项 ${index + 1}: "${option.textContent.trim()}" (data-value: ${option.getAttribute('data-value')})`);
    });
    
    // 尝试多种方式查找目标日期
    let dayOption = null;
    const selectors = [
      `[role="option"][data-value="${targetDay}"]`,
      `[role="option"]:nth-child(${targetDay})`,
      `[aria-label*="${targetDay}日"]`
    ];
    
    for (const selector of selectors) {
      dayOption = document.querySelector(selector);
      if (dayOption) {
        console.log(`✓ 通过选择器找到日期选项: ${selector}`);
        break;
      }
    }
    
    // 通过文本内容查找
    if (!dayOption) {
      for (const option of allOptions) {
        if (option.textContent.includes(`${targetDay}日`) || 
            option.textContent.trim() === targetDay.toString()) {
          dayOption = option;
          console.log(`✓ 通过文本内容找到日期选项: "${option.textContent.trim()}"`);
          break;
        }
      }
    }
    
    if (dayOption) {
      console.log('✓ 点击日期选项...');
      dayOption.click();
      
      setTimeout(() => {
        const updatedDropdown = document.querySelector('button[name="BirthDay"]');
        console.log('✓ 日期选择完成，当前值:', updatedDropdown ? updatedDropdown.textContent.trim() : '未知');
      }, 300);
    } else {
      console.log('✗ 未找到目标日期选项');
    }
  }, 500);
}

// 综合测试函数
function runFullTest() {
  console.log('\n=== 开始综合测试 ===');
  
  // 步骤1：分析结构
  analyzeDropdowns();
  
  // 步骤2：测试月份选择（延迟执行）
  setTimeout(() => {
    testMonthSelection(8); // 测试选择8月
  }, 2000);
  
  // 步骤3：测试日期选择（延迟执行）
  setTimeout(() => {
    testDaySelection(20); // 测试选择20日
  }, 5000);
}

// 快速选择函数（用于实际使用）
function quickSelectBirthDate(month, day) {
  console.log(`快速选择生日: ${month}月${day}日`);
  
  // 选择月份
  setTimeout(() => {
    const monthDropdown = document.querySelector('button[name="BirthMonth"]');
    if (monthDropdown) {
      monthDropdown.click();
      
      setTimeout(() => {
        const allOptions = document.querySelectorAll('[role="option"]');
        let monthOption = null;
        
        // 优先通过文本内容查找
        for (const option of allOptions) {
          if (option.textContent.includes(`${month}月`) || 
              option.textContent.trim() === month.toString()) {
            monthOption = option;
            break;
          }
        }
        
        if (monthOption) {
          monthOption.click();
          console.log(`✓ 已选择月份: ${month}`);
        }
      }, 500);
    }
  }, 500);
  
  // 选择日期
  setTimeout(() => {
    const dayDropdown = document.querySelector('button[name="BirthDay"]');
    if (dayDropdown) {
      dayDropdown.click();
      
      setTimeout(() => {
        const allOptions = document.querySelectorAll('[role="option"]');
        let dayOption = null;
        
        // 优先通过文本内容查找
        for (const option of allOptions) {
          if (option.textContent.includes(`${day}日`) || 
              option.textContent.trim() === day.toString()) {
            dayOption = option;
            break;
          }
        }
        
        if (dayOption) {
          dayOption.click();
          console.log(`✓ 已选择日期: ${day}`);
        }
      }, 500);
    }
  }, 2500);
}

// 导出函数供控制台使用
window.debugDropdown = {
  analyze: analyzeDropdowns,
  testMonth: testMonthSelection,
  testDay: testDaySelection,
  runFullTest: runFullTest,
  quickSelect: quickSelectBirthDate
};

console.log('\n=== 调试脚本加载完成 ===');
console.log('可用函数:');
console.log('- debugDropdown.analyze() - 分析下拉框结构');
console.log('- debugDropdown.testMonth(月份) - 测试月份选择');
console.log('- debugDropdown.testDay(日期) - 测试日期选择');
console.log('- debugDropdown.runFullTest() - 运行完整测试');
console.log('- debugDropdown.quickSelect(月份, 日期) - 快速选择生日');
console.log('\n示例用法:');
console.log('debugDropdown.quickSelect(8, 20); // 选择8月20日');
