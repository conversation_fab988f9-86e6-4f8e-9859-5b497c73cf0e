{"name": "tlds", "version": "1.240.0", "description": "A list of TLDs.", "repository": "https://github.com/stephenmathieson/node-tlds.git", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "main": "index.json", "files": ["index.json", "index.d.ts", "bin.js"], "bin": "bin.js", "scripts": {"lint": "eslint .", "test": "ava", "prepublishOnly": "eslint . && ava", "build": "node update.js"}, "keywords": ["data", "tld", "tlds", "top", "level", "domains"], "devDependencies": {"ava": "^3.11.0", "eslint": "^7.6.0", "execa": "^4.0.3", "got": "^11.5.1", "is-git-dirty": "^2.0.1", "is-punycode": "^1.0.1", "load-json-file": "^6.2.0", "prepend-file": "^2.0.0", "punycode": "^2.1.1", "semver": "^7.3.2", "write-json-file": "^4.3.0"}, "eslintConfig": {"extends": "eslint:recommended", "env": {"node": true, "es2020": true}, "rules": {"indent": ["error", 2], "quotes": ["error", "single"], "semi": ["error", "always"]}}}