@echo off
echo ========================================
echo   Microsoft Registration System v3.0
echo         Configuration Setup
echo ========================================
echo.

echo Creating .env configuration file...

(
echo # Microsoft Registration System v3.0 Configuration
echo IMAP_HOST=imap.qq.com
echo IMAP_PORT=993
echo IMAP_USER=<EMAIL>
echo IMAP_PASSWORD=zoqhcyomjbuubace
echo IMAP_TLS=true
echo TLS_REJECT_UNAUTHORIZED=false
echo TLS_MIN_VERSION=TLSv1.2
echo SERVER_PORT=3000
echo DEBUG=false
echo LOG_LEVEL=info
echo MAX_EMAIL_AGE_MINUTES=10
echo VERIFICATION_CODE_TTL_MINUTES=5
echo MAX_CONCURRENT_CONNECTIONS=3
echo MAX_RETRIES=5
echo RETRY_DELAY_MS=3000
echo CONNECTION_TIMEOUT_MS=30000
) > .env

if exist ".env" (
    echo [OK] .env file created successfully
    echo.
    echo Configuration contents:
    type .env
    echo.
    echo Configuration setup completed!
    echo You can now run: start-v3-system.bat
) else (
    echo [ERROR] Failed to create .env file
    echo Please create it manually using notepad
)

echo.
pause
