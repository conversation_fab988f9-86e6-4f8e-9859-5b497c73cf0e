// 测试人机验证页面检测修复
console.log('🧪 开始测试人机验证页面检测修复...');

// 模拟当前页面的DOM结构
function simulateCurrentPage() {
  // 创建模拟的页面元素
  const title = document.createElement('h1');
  title.setAttribute('data-testid', 'title');
  title.textContent = '证明你不是机器人';
  document.body.appendChild(title);

  const description = document.createElement('span');
  description.textContent = '长按该按钮。';
  document.body.appendChild(description);

  const iframe = document.createElement('iframe');
  iframe.setAttribute('data-testid', 'humanCaptchaIframe');
  iframe.setAttribute('title', '验证质询');
  iframe.src = 'https://iframe.hsprotect.net/index.html?app_id=PXzC5j78di&session_id=04a7c5ab27b34de9b18ad72294d6ac06';
  document.body.appendChild(iframe);

  console.log('✅ 模拟页面DOM结构创建完成');
}

// 测试isCaptchaPage函数
function testIsCaptchaPage() {
  console.log('🔍 测试isCaptchaPage函数...');
  
  // 检查标题
  const titleElement = document.querySelector('h1[data-testid="title"]');
  if (titleElement && titleElement.textContent.includes('证明你不是机器人')) {
    console.log('✅ 标题检测通过');
    return true;
  }

  // 检查页面中是否包含验证相关的关键词
  const bodyText = document.body.textContent;
  if (bodyText.includes('证明你不是机器人') || bodyText.includes('Human Challenge') ||
      (bodyText.includes('按住') && bodyText.includes('验证')) ||
      (bodyText.includes('长按') && bodyText.includes('按钮')) ||
      bodyText.includes('可访问性挑战') || bodyText.includes('再次按下')) {
    console.log('✅ 页面文本检测通过');
    return true;
  }

  console.log('❌ 人机验证页面检测失败');
  return false;
}

// 测试按钮查找逻辑
function testButtonDetection() {
  console.log('🔍 测试按钮查找逻辑...');
  
  // 检查是否显示"按住"或"长按"
  let foundHoldText = false;

  // 检查所有span标签
  const allSpans = document.querySelectorAll('span');
  for (let span of allSpans) {
    if (span.textContent.includes('按住') || span.textContent.includes('长按')) {
      foundHoldText = true;
      console.log('✅ 通过span标签找到"按住"或"长按"文本');
      break;
    }
  }

  if (foundHoldText) {
    console.log('✅ 找到长按文本，开始查找验证按钮...');
    
    // 查找iframe中的验证按钮
    const iframes = document.querySelectorAll('iframe[data-testid="humanCaptchaIframe"], iframe[title*="验证"], iframe[src*="hsprotect"]');
    if (iframes.length > 0) {
      console.log('✅ 找到人机验证iframe');
      const iframeContainer = iframes[0].parentElement;
      if (iframeContainer) {
        console.log('✅ 找到iframe容器作为验证按钮');
        return true;
      }
    }
  }

  console.log('❌ 按钮检测失败');
  return false;
}

// 运行测试
function runTests() {
  console.log('🚀 开始运行测试...');
  
  // 清理页面
  document.body.innerHTML = '';
  
  // 模拟当前页面
  simulateCurrentPage();
  
  // 运行测试
  const captchaPageTest = testIsCaptchaPage();
  const buttonDetectionTest = testButtonDetection();
  
  console.log('📊 测试结果:');
  console.log(`  - 人机验证页面检测: ${captchaPageTest ? '✅ 通过' : '❌ 失败'}`);
  console.log(`  - 按钮查找逻辑: ${buttonDetectionTest ? '✅ 通过' : '❌ 失败'}`);
  
  if (captchaPageTest && buttonDetectionTest) {
    console.log('🎉 所有测试通过！修复成功！');
  } else {
    console.log('⚠️ 部分测试失败，需要进一步调试');
  }
}

// 执行测试
runTests();
