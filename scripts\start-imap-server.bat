@echo off
title RTBS IMAP Server

echo.
echo ========================================
echo      RTBS IMAP Mail Server
echo ========================================
echo.

:: Check Node.js
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: Node.js not detected
    echo Please run install-imap-server.bat first to install
    pause
    exit /b 1
)

:: Check configuration file
if not exist "config\imap-config.json" (
    echo Error: Configuration file config\imap-config.json not found
    echo Please ensure the configuration file exists
    pause
    exit /b 1
)

:: Check dependencies
if not exist "node_modules" (
    echo Error: Dependencies not installed
    echo Please run install-imap-server.bat first to install
    pause
    exit /b 1
)

echo Environment check passed
echo.
echo Starting IMAP server...
echo Service address: http://localhost:3000
echo Health check: http://localhost:3000/health
echo.
echo Press Ctrl+C to stop the server
echo.

:: Start server
node src/server/imap-server.js

echo.
echo Server stopped
pause
