{"name": "mailparser", "version": "3.6.5", "description": "Parse e-mails", "main": "index.js", "scripts": {"test": "grunt"}, "author": "<PERSON><PERSON>", "contributors": [{"name": "<PERSON>", "email": "petersalo<PERSON><EMAIL>", "url": "https://github.com/petersalomonsen"}], "license": "MIT", "dependencies": {"encoding-japanese": "2.0.0", "he": "1.2.0", "html-to-text": "9.0.5", "iconv-lite": "0.6.3", "libmime": "5.2.1", "linkify-it": "4.0.1", "mailsplit": "5.4.0", "nodemailer": "6.9.3", "tlds": "1.240.0"}, "devDependencies": {"ajv": "8.12.0", "eslint": "8.44.0", "eslint-config-nodemailer": "1.2.0", "eslint-config-prettier": "8.8.0", "grunt": "1.6.1", "grunt-cli": "1.4.3", "grunt-contrib-nodeunit": "5.0.0", "grunt-eslint": "24.2.0", "iconv": "3.0.1", "random-message": "1.1.0"}, "repository": {"type": "git", "url": "https://github.com/nodemailer/mailparser.git"}, "bugs": {"url": "https://github.com/nodemailer/mailparser/issues"}}