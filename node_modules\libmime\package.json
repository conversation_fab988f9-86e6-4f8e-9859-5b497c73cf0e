{"name": "libmime", "description": "Encode and decode quoted printable and base64 strings", "version": "5.2.1", "main": "lib/libmime", "homepage": "https://github.com/andris9/libmime", "repository": {"type": "git", "url": "git://github.com/andris9/libmime.git"}, "license": "MIT", "keywords": ["MIME", "Base64", "Quoted-Printable"], "author": "<PERSON><PERSON> <<EMAIL>>", "scripts": {"test": "grunt"}, "dependencies": {"encoding-japanese": "2.0.0", "iconv-lite": "0.6.3", "libbase64": "1.2.1", "libqp": "2.0.1"}, "devDependencies": {"chai": "4.3.7", "eslint-config-nodemailer": "1.2.0", "eslint-config-prettier": "8.6.0", "grunt": "1.5.3", "grunt-cli": "1.4.3", "grunt-eslint": "24.0.1", "grunt-mocha-test": "0.13.3", "mocha": "10.2.0"}}