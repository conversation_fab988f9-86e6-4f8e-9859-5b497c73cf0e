// 测试修复效果的脚本

console.log('🧪 开始测试修复效果...');

// 测试邮件验证码匹配修复
function testEmailCodeMatching() {
  console.log('\n📧 测试邮件验证码匹配修复:');
  
  // 模拟测试数据
  const testEmails = [
    {
      to: '<EMAIL>',
      from: '<EMAIL>',
      subject: '安全代码',
      text: '您的安全代码是: 123456',
      date: new Date(Date.now() - 2 * 60 * 1000) // 2分钟前
    },
    {
      to: '<EMAIL>', 
      from: '<EMAIL>',
      subject: '安全代码',
      text: '您的安全代码是: 654321',
      date: new Date(Date.now() - 1 * 60 * 1000) // 1分钟前
    },
    {
      to: '<EMAIL>',
      from: '<EMAIL>', 
      subject: '验证码',
      text: '请使用此安全代码: 789012',
      date: new Date() // 刚刚
    }
  ];
  
  // 测试相关性评分
  if (typeof window !== 'undefined' && window.imapServer) {
    const targetEmail = '<EMAIL>';
    
    testEmails.forEach((email, index) => {
      const score = window.imapServer.getEmailRelevanceScore(email, targetEmail);
      console.log(`邮件 ${index + 1} 相关性评分: ${score}分`);
      console.log(`  - 收件人: ${email.to}`);
      console.log(`  - 时间: ${email.date.toLocaleTimeString()}`);
    });
    
    // 应该选择最新的、相关性最高的邮件（邮件3）
    console.log('\n✅ 预期结果: 应该选择邮件3（789012），因为它最新且发给目标邮箱');
  } else {
    console.log('⚠️ IMAP服务器未初始化，跳过邮件测试');
  }
}

// 测试出生日期选择修复
function testBirthDateSelection() {
  console.log('\n🎂 测试出生日期选择修复:');
  
  // 检查页面上是否有生日选择元素
  const monthDropdown = document.querySelector('button[name="BirthMonth"]');
  const dayDropdown = document.querySelector('button[name="BirthDay"]');
  
  if (monthDropdown && dayDropdown) {
    console.log('✅ 找到生日选择元素');
    console.log(`月份下拉框状态: ${monthDropdown.textContent.trim()}`);
    console.log(`日期下拉框状态: ${dayDropdown.textContent.trim()}`);
    
    // 测试新的选择方法
    const testMonth = 8;
    const testDay = 15;
    
    console.log(`\n🧪 测试选择 ${testMonth}月${testDay}日...`);
    
    if (typeof selectBirthDateSequentially === 'function') {
      selectBirthDateSequentially(testMonth, testDay);
      console.log('✅ 调用新的选择方法成功');
    } else {
      console.log('❌ 新的选择方法未找到');
    }
  } else {
    console.log('⚠️ 当前页面没有生日选择元素');
    console.log('提示: 请在Microsoft账户注册页面运行此测试');
  }
}

// 测试下拉框状态检查
function testDropdownState() {
  console.log('\n🔍 检查下拉框状态:');
  
  const monthDropdown = document.querySelector('button[name="BirthMonth"]');
  const dayDropdown = document.querySelector('button[name="BirthDay"]');
  
  if (monthDropdown) {
    console.log('月份下拉框:');
    console.log(`  - 文本: "${monthDropdown.textContent.trim()}"`);
    console.log(`  - 禁用状态: ${monthDropdown.disabled}`);
    console.log(`  - aria-disabled: ${monthDropdown.getAttribute('aria-disabled')}`);
    console.log(`  - aria-expanded: ${monthDropdown.getAttribute('aria-expanded')}`);
  }
  
  if (dayDropdown) {
    console.log('日期下拉框:');
    console.log(`  - 文本: "${dayDropdown.textContent.trim()}"`);
    console.log(`  - 禁用状态: ${dayDropdown.disabled}`);
    console.log(`  - aria-disabled: ${dayDropdown.getAttribute('aria-disabled')}`);
    console.log(`  - aria-expanded: ${dayDropdown.getAttribute('aria-expanded')}`);
  }
}

// 运行所有测试
function runAllTests() {
  console.log('🚀 运行所有修复测试...\n');
  
  testEmailCodeMatching();
  testBirthDateSelection();
  testDropdownState();
  
  console.log('\n✅ 测试完成！');
  console.log('\n📋 使用说明:');
  console.log('1. 邮件验证码问题: 现在会根据邮箱地址和时间选择最相关的验证码');
  console.log('2. 生日选择问题: 使用了重试机制和备用方案，应该更稳定');
  console.log('3. 如果仍有问题，请查看控制台日志获取详细信息');
}

// 导出测试函数
if (typeof window !== 'undefined') {
  window.testFixes = {
    runAll: runAllTests,
    testEmail: testEmailCodeMatching,
    testBirthDate: testBirthDateSelection,
    testDropdown: testDropdownState
  };
  
  console.log('🔧 测试工具已加载！');
  console.log('使用 testFixes.runAll() 运行所有测试');
  console.log('或使用 testFixes.testEmail() / testFixes.testBirthDate() 单独测试');
}

// 自动运行测试（如果在浏览器环境中）
if (typeof window !== 'undefined') {
  setTimeout(runAllTests, 1000);
}
