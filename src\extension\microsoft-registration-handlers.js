/**
 * Microsoft注册阶段处理器实现
 * 每个注册阶段的具体处理逻辑
 * 
 * <AUTHOR> Team
 * @version 3.0.0
 * @description 实现各个注册阶段的具体操作
 */

/**
 * 数据许可阶段处理器
 */
class DataPermissionHandler extends StageHandler {
  constructor() {
    super(REGISTRATION_STAGES.DATA_PERMISSION);
  }

  async execute(context) {
    console.log('🔐 处理数据许可页面...');
    
    // 检查是否在数据许可页面
    const isDataPermissionPage = this.detectDataPermissionPage();
    if (!isDataPermissionPage) {
      console.log('❌ 不在数据许可页面');
      return false;
    }

    // 点击同意按钮
    const agreeButton = document.querySelector('button[data-testid="accept-button"], button[id*="accept"], button[class*="accept"]');
    if (agreeButton) {
      console.log('✅ 找到同意按钮，点击...');
      agreeButton.click();
      
      // 等待页面跳转
      await this.waitForPageTransition();
      return true;
    }

    console.log('❌ 未找到同意按钮');
    return false;
  }

  detectDataPermissionPage() {
    // 检测数据许可页面的特征
    const indicators = [
      () => document.querySelector('[data-testid="accept-button"]'),
      () => document.querySelector('button[id*="accept"]'),
      () => document.querySelector('button[class*="accept"]'),
      () => document.body.textContent.includes('数据使用') || document.body.textContent.includes('privacy'),
    ];

    return indicators.some(indicator => indicator());
  }

  async waitForPageTransition() {
    return new Promise(resolve => {
      const timeout = setTimeout(resolve, 3000);
      this.addResource(() => clearTimeout(timeout));
    });
  }
}

/**
 * 邮箱输入阶段处理器
 */
class EmailInputHandler extends StageHandler {
  constructor() {
    super(REGISTRATION_STAGES.EMAIL_INPUT);
  }

  async onEnter(context) {
    await super.onEnter(context);
    
    // 生成新的邮箱账号
    if (!context.account) {
      context.account = this.generateEmailAccount();
      console.log(`📧 生成邮箱账号: ${context.account}`);
    }
    
    return true;
  }

  async execute(context) {
    console.log('📧 处理邮箱输入页面...');
    
    // 检查是否在邮箱输入页面
    const emailInput = this.findEmailInput();
    if (!emailInput) {
      console.log('❌ 未找到邮箱输入框');
      return false;
    }

    // 填写邮箱
    emailInput.value = context.account;
    emailInput.dispatchEvent(new Event('input', { bubbles: true }));
    emailInput.dispatchEvent(new Event('change', { bubbles: true }));
    
    console.log(`✅ 邮箱填写完成: ${context.account}`);

    // 点击下一步按钮
    await this.clickNextButton();
    
    return true;
  }

  generateEmailAccount() {
    const chars = 'abcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for (let i = 0; i < 12; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return `${result}@s4464.cfd`;
  }

  findEmailInput() {
    const selectors = [
      'input[type="email"]',
      'input[name*="email"]',
      'input[id*="email"]',
      'input[placeholder*="email"]',
      'input[data-testid*="email"]'
    ];

    for (const selector of selectors) {
      const input = document.querySelector(selector);
      if (input && input.offsetParent !== null) { // 确保元素可见
        return input;
      }
    }
    return null;
  }

  async clickNextButton() {
    const nextButton = this.findNextButton();
    if (nextButton) {
      console.log('✅ 点击下一步按钮');
      nextButton.click();
      await this.waitForPageTransition();
    } else {
      console.log('❌ 未找到下一步按钮');
    }
  }

  findNextButton() {
    const selectors = [
      'button[type="submit"]',
      'button[data-testid*="next"]',
      'button[id*="next"]',
      'input[type="submit"]'
    ];

    for (const selector of selectors) {
      const button = document.querySelector(selector);
      if (button && button.offsetParent !== null) {
        return button;
      }
    }
    return null;
  }

  async waitForPageTransition() {
    return new Promise(resolve => {
      const timeout = setTimeout(resolve, 2000);
      this.addResource(() => clearTimeout(timeout));
    });
  }
}

/**
 * 邮件验证等待阶段处理器
 */
class EmailVerificationHandler extends StageHandler {
  constructor() {
    super(REGISTRATION_STAGES.EMAIL_VERIFICATION);
  }

  async execute(context) {
    console.log('📬 等待邮件验证页面...');
    
    // 检查是否在邮件验证等待页面
    const isVerificationPage = this.detectVerificationWaitPage();
    if (!isVerificationPage) {
      console.log('❌ 不在邮件验证等待页面');
      return false;
    }

    console.log('✅ 在邮件验证等待页面，等待验证码页面出现...');
    
    // 等待验证码输入页面出现
    const codePageAppeared = await this.waitForCodePage();
    return codePageAppeared;
  }

  detectVerificationWaitPage() {
    const indicators = [
      () => document.body.textContent.includes('验证') || document.body.textContent.includes('verify'),
      () => document.body.textContent.includes('邮件') || document.body.textContent.includes('email'),
      () => document.body.textContent.includes('发送') || document.body.textContent.includes('sent'),
      () => document.querySelector('[data-testid*="verification"]'),
      () => document.querySelector('[id*="verification"]')
    ];

    return indicators.some(indicator => indicator());
  }

  async waitForCodePage() {
    return new Promise((resolve) => {
      let attempts = 0;
      const maxAttempts = 30; // 最多等待30秒
      
      const checkInterval = setInterval(() => {
        attempts++;
        
        // 检查是否出现验证码输入框
        const codeInputs = document.querySelectorAll('input[type="text"], input[type="number"]');
        const hasCodeInputs = Array.from(codeInputs).some(input => 
          input.maxLength === 1 || input.pattern === '[0-9]' || 
          input.getAttribute('data-testid')?.includes('code')
        );

        if (hasCodeInputs) {
          clearInterval(checkInterval);
          console.log('✅ 验证码页面已出现');
          resolve(true);
        } else if (attempts >= maxAttempts) {
          clearInterval(checkInterval);
          console.log('⏰ 等待验证码页面超时');
          resolve(false);
        }
      }, 1000);

      this.addResource(() => clearInterval(checkInterval));
    });
  }
}

/**
 * 验证码输入阶段处理器
 */
class VerificationCodeHandler extends StageHandler {
  constructor() {
    super(REGISTRATION_STAGES.VERIFICATION_CODE);
    this.imapService = null;
  }

  async onEnter(context) {
    await super.onEnter(context);
    
    // 初始化IMAP服务
    this.imapService = new ImapServiceManager(context.account);
    this.addResource(this.imapService);
    
    return true;
  }

  async execute(context) {
    console.log('🔢 处理验证码输入页面...');
    
    // 检查验证码输入框
    const codeInputs = this.findCodeInputs();
    if (codeInputs.length === 0) {
      console.log('❌ 未找到验证码输入框');
      return false;
    }

    console.log(`✅ 找到 ${codeInputs.length} 个验证码输入框`);

    // 启动IMAP邮件检查
    const verificationCode = await this.imapService.getVerificationCode();
    if (!verificationCode) {
      console.log('❌ 未获取到验证码');
      return false;
    }

    // 填写验证码
    const fillSuccess = this.fillVerificationCode(codeInputs, verificationCode);
    if (!fillSuccess) {
      console.log('❌ 验证码填写失败');
      return false;
    }

    // 提交验证码
    await this.submitVerificationCode();
    
    return true;
  }

  findCodeInputs() {
    // 查找验证码输入框
    const inputs = document.querySelectorAll('input[type="text"], input[type="number"]');
    return Array.from(inputs).filter(input => 
      input.maxLength === 1 || 
      input.pattern === '[0-9]' || 
      input.getAttribute('data-testid')?.includes('code') ||
      input.getAttribute('aria-label')?.includes('code')
    );
  }

  fillVerificationCode(inputs, code) {
    if (code.length !== 6 || inputs.length < 6) {
      console.log(`❌ 验证码长度或输入框数量不匹配: ${code.length}, ${inputs.length}`);
      return false;
    }

    for (let i = 0; i < 6; i++) {
      if (inputs[i]) {
        inputs[i].value = code[i];
        inputs[i].dispatchEvent(new Event('input', { bubbles: true }));
        inputs[i].dispatchEvent(new Event('change', { bubbles: true }));
      }
    }

    console.log('✅ 验证码填写完成');
    return true;
  }

  async submitVerificationCode() {
    // 查找提交按钮
    const submitButton = document.querySelector('button[type="submit"], button[data-testid*="submit"], button[id*="submit"]');
    if (submitButton) {
      console.log('✅ 点击提交按钮');
      submitButton.click();
      await this.waitForSubmission();
    } else {
      console.log('⚠️ 未找到提交按钮，验证码可能自动提交');
    }
  }

  async waitForSubmission() {
    return new Promise(resolve => {
      const timeout = setTimeout(resolve, 3000);
      this.addResource(() => clearTimeout(timeout));
    });
  }
}

/**
 * IMAP服务管理器
 * 负责邮件检查和验证码获取
 */
class ImapServiceManager {
  constructor(email) {
    this.email = email;
    this.serverUrl = 'http://localhost:3000';
    this.maxRetries = 5;
    this.checkInterval = 3000;
  }

  async getVerificationCode() {
    console.log(`🚀 开始获取验证码: ${this.email}`);
    
    // 首先触发邮件检查
    await this.triggerEmailCheck();
    
    // 轮询获取验证码
    for (let attempt = 0; attempt < this.maxRetries; attempt++) {
      console.log(`🔍 尝试获取验证码 (${attempt + 1}/${this.maxRetries})`);
      
      const code = await this.fetchVerificationCode();
      if (code) {
        console.log(`✅ 获取到验证码: ${code}`);
        return code;
      }
      
      if (attempt < this.maxRetries - 1) {
        await this.wait(this.checkInterval);
      }
    }
    
    console.log('❌ 获取验证码失败');
    return null;
  }

  async triggerEmailCheck() {
    try {
      const response = await fetch(`${this.serverUrl}/check-email/${encodeURIComponent(this.email)}`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' }
      });
      
      const result = await response.json();
      console.log('📧 邮件检查触发结果:', result);
    } catch (error) {
      console.error('❌ 触发邮件检查失败:', error);
    }
  }

  async fetchVerificationCode() {
    try {
      const response = await fetch(`${this.serverUrl}/verification-code/${encodeURIComponent(this.email)}`);
      const result = await response.json();
      
      if (result.success && result.code) {
        return result.code;
      }
    } catch (error) {
      console.error('❌ 获取验证码失败:', error);
    }
    
    return null;
  }

  async wait(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  async cleanup() {
    console.log('🧹 清理IMAP服务资源');
    // 这里可以添加清理逻辑，比如删除验证码邮件
  }
}

/**
 * 个人信息阶段处理器
 */
class PersonalInfoHandler extends StageHandler {
  constructor() {
    super(REGISTRATION_STAGES.PERSONAL_INFO);
  }

  async execute(context) {
    console.log('👤 处理个人信息页面...');

    // 检查是否在个人信息页面
    const isPersonalInfoPage = this.detectPersonalInfoPage();
    if (!isPersonalInfoPage) {
      console.log('❌ 不在个人信息页面');
      return false;
    }

    // 填写生日信息
    const birthdayFilled = await this.fillBirthdayInfo();
    if (!birthdayFilled) {
      console.log('❌ 生日信息填写失败');
      return false;
    }

    // 点击下一步
    await this.clickNextButton();

    return true;
  }

  detectPersonalInfoPage() {
    const indicators = [
      () => document.querySelector('select[name*="birth"], select[id*="birth"]'),
      () => document.querySelector('select[name*="year"], select[id*="year"]'),
      () => document.querySelector('select[name*="month"], select[id*="month"]'),
      () => document.body.textContent.includes('生日') || document.body.textContent.includes('birthday'),
    ];

    return indicators.some(indicator => indicator());
  }

  async fillBirthdayInfo() {
    // 生成随机生日
    const year = Math.floor(Math.random() * 11) + 1995; // 1995-2005
    const month = Math.floor(Math.random() * 12) + 1;   // 1-12
    const day = Math.floor(Math.random() * 26) + 1;     // 1-26

    console.log(`🎂 生成生日: ${year}-${month}-${day}`);

    // 填写年份
    const yearSelect = document.querySelector('select[name*="year"], select[id*="year"]');
    if (yearSelect) {
      yearSelect.value = year.toString();
      yearSelect.dispatchEvent(new Event('change', { bubbles: true }));
    }

    // 填写月份
    const monthSelect = document.querySelector('select[name*="month"], select[id*="month"]');
    if (monthSelect) {
      monthSelect.value = month.toString();
      monthSelect.dispatchEvent(new Event('change', { bubbles: true }));
    }

    // 填写日期
    const daySelect = document.querySelector('select[name*="day"], select[id*="day"]');
    if (daySelect) {
      daySelect.value = day.toString();
      daySelect.dispatchEvent(new Event('change', { bubbles: true }));
    }

    return yearSelect && monthSelect && daySelect;
  }

  async clickNextButton() {
    const nextButton = document.querySelector('button[type="submit"], button[data-testid*="next"], input[type="submit"]');
    if (nextButton) {
      console.log('✅ 点击下一步按钮');
      nextButton.click();
      await this.waitForPageTransition();
    }
  }

  async waitForPageTransition() {
    return new Promise(resolve => {
      const timeout = setTimeout(resolve, 2000);
      this.addResource(() => clearTimeout(timeout));
    });
  }
}

/**
 * 姓名信息阶段处理器
 */
class NameInfoHandler extends StageHandler {
  constructor() {
    super(REGISTRATION_STAGES.NAME_INFO);
  }

  async execute(context) {
    console.log('📝 处理姓名信息页面...');

    // 检查是否在姓名信息页面
    const isNameInfoPage = this.detectNameInfoPage();
    if (!isNameInfoPage) {
      console.log('❌ 不在姓名信息页面');
      return false;
    }

    // 填写姓名信息
    const nameFilled = await this.fillNameInfo();
    if (!nameFilled) {
      console.log('❌ 姓名信息填写失败');
      return false;
    }

    // 点击下一步
    await this.clickNextButton();

    return true;
  }

  detectNameInfoPage() {
    const indicators = [
      () => document.querySelector('input[name*="first"], input[id*="first"]'),
      () => document.querySelector('input[name*="last"], input[id*="last"]'),
      () => document.querySelector('input[name*="name"], input[id*="name"]'),
      () => document.body.textContent.includes('姓名') || document.body.textContent.includes('name'),
    ];

    return indicators.some(indicator => indicator());
  }

  async fillNameInfo() {
    // 生成随机姓名
    const firstName = this.generateRandomName(2, 4, true);  // 2-4位，包含数字
    const lastName = this.generateRandomName(1, 4, false);  // 1-4位，纯字母

    console.log(`👤 生成姓名: ${firstName} ${lastName}`);

    // 填写名字
    const firstNameInput = document.querySelector('input[name*="first"], input[id*="first"]');
    if (firstNameInput) {
      firstNameInput.value = firstName;
      firstNameInput.dispatchEvent(new Event('input', { bubbles: true }));
      firstNameInput.dispatchEvent(new Event('change', { bubbles: true }));
    }

    // 填写姓氏
    const lastNameInput = document.querySelector('input[name*="last"], input[id*="last"]');
    if (lastNameInput) {
      lastNameInput.value = lastName;
      lastNameInput.dispatchEvent(new Event('input', { bubbles: true }));
      lastNameInput.dispatchEvent(new Event('change', { bubbles: true }));
    }

    return firstNameInput && lastNameInput;
  }

  generateRandomName(minLength, maxLength, includeNumbers) {
    const letters = 'abcdefghijklmnopqrstuvwxyz';
    const numbers = '0123456789';
    const chars = includeNumbers ? letters + numbers : letters;

    const length = Math.floor(Math.random() * (maxLength - minLength + 1)) + minLength;
    let result = '';

    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }

    return result;
  }

  async clickNextButton() {
    const nextButton = document.querySelector('button[type="submit"], button[data-testid*="next"], input[type="submit"]');
    if (nextButton) {
      console.log('✅ 点击下一步按钮');
      nextButton.click();
      await this.waitForPageTransition();
    }
  }

  async waitForPageTransition() {
    return new Promise(resolve => {
      const timeout = setTimeout(resolve, 2000);
      this.addResource(() => clearTimeout(timeout));
    });
  }
}

/**
 * 人机验证阶段处理器
 */
class CaptchaHandler extends StageHandler {
  constructor() {
    super(REGISTRATION_STAGES.CAPTCHA);
  }

  async execute(context) {
    console.log('🤖 处理人机验证页面...');

    // 检查是否在人机验证页面
    const isCaptchaPage = this.detectCaptchaPage();
    if (!isCaptchaPage) {
      console.log('❌ 不在人机验证页面');
      return false;
    }

    // 处理人机验证
    const captchaSuccess = await this.handleCaptcha();
    if (!captchaSuccess) {
      console.log('❌ 人机验证失败');
      return false;
    }

    return true;
  }

  detectCaptchaPage() {
    const indicators = [
      () => document.body.textContent.includes('按住') || document.body.textContent.includes('hold'),
      () => document.body.textContent.includes('验证') || document.body.textContent.includes('verify'),
      () => document.querySelector('[data-testid*="captcha"]'),
      () => document.querySelector('[id*="captcha"]'),
      () => document.querySelector('button[aria-label*="hold"]')
    ];

    return indicators.some(indicator => indicator());
  }

  async handleCaptcha() {
    const maxAttempts = 3;

    for (let attempt = 0; attempt < maxAttempts; attempt++) {
      console.log(`🔄 人机验证尝试 ${attempt + 1}/${maxAttempts}`);

      const captchaButton = this.findCaptchaButton();
      if (!captchaButton) {
        console.log('❌ 未找到人机验证按钮');
        return false;
      }

      // 模拟长按操作
      const holdSuccess = await this.performHoldAction(captchaButton);
      if (holdSuccess) {
        console.log('✅ 人机验证成功');
        return true;
      }

      // 等待一段时间再重试
      if (attempt < maxAttempts - 1) {
        await this.wait(2000);
      }
    }

    console.log('❌ 人机验证失败，已达到最大尝试次数');
    return false;
  }

  findCaptchaButton() {
    const selectors = [
      'button[aria-label*="hold"]',
      'button[data-testid*="captcha"]',
      'button[id*="captcha"]',
      'div[role="button"][aria-label*="hold"]'
    ];

    for (const selector of selectors) {
      const button = document.querySelector(selector);
      if (button && button.offsetParent !== null) {
        return button;
      }
    }
    return null;
  }

  async performHoldAction(button) {
    return new Promise((resolve) => {
      console.log('🖱️ 开始长按操作...');

      // 触发鼠标按下事件
      button.dispatchEvent(new MouseEvent('mousedown', { bubbles: true }));

      // 持续3秒
      const holdTimeout = setTimeout(() => {
        // 触发鼠标释放事件
        button.dispatchEvent(new MouseEvent('mouseup', { bubbles: true }));

        // 等待验证结果
        setTimeout(() => {
          // 检查是否验证成功（页面是否跳转）
          const stillOnCaptchaPage = this.detectCaptchaPage();
          resolve(!stillOnCaptchaPage);
        }, 1000);
      }, 3000);

      this.addResource(() => clearTimeout(holdTimeout));
    });
  }

  async wait(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// 导出处理器类
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    DataPermissionHandler,
    EmailInputHandler,
    EmailVerificationHandler,
    VerificationCodeHandler,
    PersonalInfoHandler,
    NameInfoHandler,
    CaptchaHandler,
    ImapServiceManager
  };
} else {
  // 浏览器环境 - 将类直接暴露到全局作用域
  window.DataPermissionHandler = DataPermissionHandler;
  window.EmailInputHandler = EmailInputHandler;
  window.EmailVerificationHandler = EmailVerificationHandler;
  window.VerificationCodeHandler = VerificationCodeHandler;
  window.PersonalInfoHandler = PersonalInfoHandler;
  window.NameInfoHandler = NameInfoHandler;
  window.CaptchaHandler = CaptchaHandler;
  window.ImapServiceManager = ImapServiceManager;

  // 同时保留命名空间对象
  window.MicrosoftRegistrationHandlers = {
    DataPermissionHandler,
    EmailInputHandler,
    EmailVerificationHandler,
    VerificationCodeHandler,
    PersonalInfoHandler,
    NameInfoHandler,
    CaptchaHandler,
    ImapServiceManager
  };
}
