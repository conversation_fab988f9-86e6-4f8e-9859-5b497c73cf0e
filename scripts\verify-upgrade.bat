@echo off
echo.
echo ========================================
echo   Microsoft Registration System v3.0
echo         Upgrade Verification
echo ========================================
echo.

echo Verifying upgrade to Microsoft Registration System v3.0...
echo.

REM Check if we're in the right directory
if not exist "src\extension\manifest.json" (
    echo ERROR: Please run this script in RTBS project root directory
    echo        Current directory should contain src\extension\manifest.json file
    pause
    exit /b 1
)

echo [1/5] Checking core files...
set CORE_FILES_OK=1

if exist "src\extension\manifest.json" (echo [OK] src\extension\manifest.json) else (echo [MISSING] src\extension\manifest.json & set CORE_FILES_OK=0)
if exist "src\extension\content.js" (echo [OK] src\extension\content.js) else (echo [MISSING] src\extension\content.js & set CORE_FILES_OK=0)
if exist "src\extension\popup.js" (echo [OK] src\extension\popup.js) else (echo [MISSING] src\extension\popup.js & set CORE_FILES_OK=0)
if exist "src\extension\popup.html" (echo [OK] src\extension\popup.html) else (echo [MISSING] src\extension\popup.html & set CORE_FILES_OK=0)
if exist "src\extension\background.js" (echo [OK] src\extension\background.js) else (echo [MISSING] src\extension\background.js & set CORE_FILES_OK=0)

echo.
echo [2/5] Checking v3.0 system files...
set V3_FILES_OK=1

if exist "src\extension\microsoft-registration-state-machine.js" (echo [OK] src\extension\microsoft-registration-state-machine.js) else (echo [MISSING] src\extension\microsoft-registration-state-machine.js & set V3_FILES_OK=0)
if exist "src\extension\microsoft-registration-handlers.js" (echo [OK] src\extension\microsoft-registration-handlers.js) else (echo [MISSING] src\extension\microsoft-registration-handlers.js & set V3_FILES_OK=0)
if exist "src\extension\remaining-stage-handlers.js" (echo [OK] src\extension\remaining-stage-handlers.js) else (echo [MISSING] src\extension\remaining-stage-handlers.js & set V3_FILES_OK=0)
if exist "src\extension\page-detector.js" (echo [OK] src\extension\page-detector.js) else (echo [MISSING] src\extension\page-detector.js & set V3_FILES_OK=0)
if exist "src\extension\microsoft-registration-controller.js" (echo [OK] src\extension\microsoft-registration-controller.js) else (echo [MISSING] src\extension\microsoft-registration-controller.js & set V3_FILES_OK=0)
if exist "src\extension\error-handling-system.js" (echo [OK] src\extension\error-handling-system.js) else (echo [MISSING] src\extension\error-handling-system.js & set V3_FILES_OK=0)
if exist "src\extension\microsoft-registration-v3.js" (echo [OK] src\extension\microsoft-registration-v3.js) else (echo [MISSING] src\extension\microsoft-registration-v3.js & set V3_FILES_OK=0)
if exist "src\server\enhanced-imap-service.js" (echo [OK] src\server\enhanced-imap-service.js) else (echo [MISSING] src\server\enhanced-imap-service.js & set V3_FILES_OK=0)

echo.
echo [3/5] Checking IMAP service files...
set IMAP_FILES_OK=1

if exist "src\server\imap-server.js" (echo [OK] src\server\imap-server.js) else (echo [MISSING] src\server\imap-server.js & set IMAP_FILES_OK=0)

echo.
echo [4/5] Checking documentation and tools...
set DOC_FILES_OK=1

if exist "docs\DEPLOYMENT_GUIDE_V3.md" (echo [OK] docs\DEPLOYMENT_GUIDE_V3.md) else (echo [MISSING] docs\DEPLOYMENT_GUIDE_V3.md & set DOC_FILES_OK=0)
if exist "tests\test-v3-system.js" (echo [OK] tests\test-v3-system.js) else (echo [MISSING] tests\test-v3-system.js & set DOC_FILES_OK=0)
if exist "docs\README_V3_UPGRADE.md" (echo [OK] docs\README_V3_UPGRADE.md) else (echo [MISSING] docs\README_V3_UPGRADE.md & set DOC_FILES_OK=0)

echo.
echo [5/5] Checking configuration...
if exist ".env" (
    echo [OK] .env configuration file exists
    set CONFIG_OK=1
) else (
    if exist ".env.example" (
        echo [WARNING] .env not found, but .env.example exists
        echo [INFO] You can copy .env.example to .env and configure it
        set CONFIG_OK=1
    ) else (
        echo [MISSING] .env.example configuration template
        set CONFIG_OK=0
    )
)

echo.
echo ========================================
echo Verification Results
echo ========================================

set OVERALL_OK=1

if %CORE_FILES_OK%==1 (
    echo [PASS] Core files check
) else (
    echo [FAIL] Core files check
    set OVERALL_OK=0
)

if %V3_FILES_OK%==1 (
    echo [PASS] v3.0 system files check
) else (
    echo [FAIL] v3.0 system files check
    set OVERALL_OK=0
)

if %IMAP_FILES_OK%==1 (
    echo [PASS] IMAP service files check
) else (
    echo [FAIL] IMAP service files check
    set OVERALL_OK=0
)

if %DOC_FILES_OK%==1 (
    echo [PASS] Documentation and tools check
) else (
    echo [FAIL] Documentation and tools check
    set OVERALL_OK=0
)

if %CONFIG_OK%==1 (
    echo [PASS] Configuration check
) else (
    echo [FAIL] Configuration check
    set OVERALL_OK=0
)

echo.
if %OVERALL_OK%==1 (
    echo ========================================
    echo         UPGRADE SUCCESSFUL!
    echo ========================================
    echo.
    echo Microsoft Registration System v3.0 is ready to use.
    echo.
    echo Next steps:
    echo 1. Configure .env file if not done yet
    echo 2. Start IMAP server: start-v3-system.bat
    echo 3. Reload browser extension
    echo 4. Test the system functionality
    echo.
    echo Quick commands:
    echo   start-v3-system.bat    - Start IMAP server
    echo   check-v3-files.bat     - Detailed file check
    echo.
) else (
    echo ========================================
    echo         UPGRADE INCOMPLETE
    echo ========================================
    echo.
    echo Some files are missing or configuration is incomplete.
    echo Please check the missing items above and complete the upgrade.
    echo.
    echo Helpful commands:
    echo   check-v3-files.bat     - Detailed file check
    echo   one-click-upgrade.bat  - Complete upgrade process
    echo.
)

echo For detailed documentation, see:
echo   README_V3_UPGRADE.md
echo   DEPLOYMENT_GUIDE_V3.md
echo   UPGRADE_SUMMARY.md
echo.

pause
