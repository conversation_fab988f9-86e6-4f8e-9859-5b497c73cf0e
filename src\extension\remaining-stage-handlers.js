/**
 * 剩余阶段处理器实现
 * 完成登录和奖励页面的处理
 * 
 * <AUTHOR> Team
 * @version 3.0.0
 * @description 实现登录完成和奖励欢迎阶段的处理器
 */

/**
 * 登录完成阶段处理器
 */
class LoginCompleteHandler extends StageHandler {
  constructor() {
    super(REGISTRATION_STAGES.LOGIN_COMPLETE);
  }

  async execute(context) {
    console.log('🎉 处理登录完成页面...');
    
    // 检查是否在登录完成页面
    const isLoginCompletePage = this.detectLoginCompletePage();
    if (!isLoginCompletePage) {
      console.log('❌ 不在登录完成页面');
      return false;
    }

    // 处理各种可能的弹窗和设置页面
    const handled = await this.handleLoginCompleteActions();
    if (!handled) {
      console.log('❌ 登录完成页面处理失败');
      return false;
    }

    return true;
  }

  detectLoginCompletePage() {
    const indicators = [
      () => document.querySelector('button[data-testid*="skip"], button[id*="skip"]'),
      () => document.body.textContent.includes('欢迎') || document.body.textContent.includes('welcome'),
      () => document.body.textContent.includes('设置') || document.body.textContent.includes('setup'),
      () => document.body.textContent.includes('完成') || document.body.textContent.includes('complete'),
      () => location.href.includes('complete') || location.href.includes('finish'),
      () => document.querySelector('button[aria-label*="skip"], a[href*="skip"]')
    ];

    return indicators.some(indicator => indicator());
  }

  async handleLoginCompleteActions() {
    const actions = [
      () => this.clickSkipButton(),
      () => this.handleWelcomeDialog(),
      () => this.handleSetupDialog(),
      () => this.navigateToRewards()
    ];

    for (const action of actions) {
      try {
        const result = await action();
        if (result) {
          console.log('✅ 登录完成页面操作成功');
          return true;
        }
      } catch (error) {
        console.error('❌ 登录完成页面操作失败:', error);
      }
    }

    return false;
  }

  async clickSkipButton() {
    const skipSelectors = [
      'button[data-testid*="skip"]',
      'button[id*="skip"]',
      'button[aria-label*="skip"]',
      'a[href*="skip"]',
      'button:contains("跳过")',
      'button:contains("Skip")',
      'button[class*="skip"]'
    ];

    for (const selector of skipSelectors) {
      const button = document.querySelector(selector);
      if (button && button.offsetParent !== null) {
        console.log(`✅ 找到跳过按钮: ${selector}`);
        button.click();
        await this.waitForPageTransition();
        return true;
      }
    }

    // 尝试文本匹配
    const buttons = document.querySelectorAll('button, a');
    for (const button of buttons) {
      const text = button.textContent.toLowerCase();
      if (text.includes('跳过') || text.includes('skip') || text.includes('暂时') || text.includes('later')) {
        console.log(`✅ 找到跳过按钮(文本匹配): ${text}`);
        button.click();
        await this.waitForPageTransition();
        return true;
      }
    }

    return false;
  }

  async handleWelcomeDialog() {
    // 查找欢迎对话框的关闭或继续按钮
    const dialogSelectors = [
      'button[aria-label*="close"]',
      'button[data-testid*="close"]',
      'button[class*="close"]',
      '.modal button',
      '.dialog button',
      'button[aria-label*="continue"]',
      'button[data-testid*="continue"]'
    ];

    for (const selector of dialogSelectors) {
      const button = document.querySelector(selector);
      if (button && button.offsetParent !== null) {
        console.log(`✅ 处理欢迎对话框: ${selector}`);
        button.click();
        await this.waitForPageTransition();
        return true;
      }
    }

    return false;
  }

  async handleSetupDialog() {
    // 查找设置对话框的跳过或稍后按钮
    const setupSelectors = [
      'button[data-testid*="later"]',
      'button[id*="later"]',
      'button[aria-label*="later"]',
      'button[data-testid*="not-now"]',
      'button[id*="not-now"]'
    ];

    for (const selector of setupSelectors) {
      const button = document.querySelector(selector);
      if (button && button.offsetParent !== null) {
        console.log(`✅ 处理设置对话框: ${selector}`);
        button.click();
        await this.waitForPageTransition();
        return true;
      }
    }

    return false;
  }

  async navigateToRewards() {
    // 尝试导航到Microsoft Rewards页面
    const rewardsUrl = 'https://rewards.microsoft.com/';
    
    // 检查是否已经在rewards页面
    if (location.href.includes('rewards.microsoft.com')) {
      console.log('✅ 已在Microsoft Rewards页面');
      return true;
    }

    // 查找rewards链接
    const rewardsLink = document.querySelector('a[href*="rewards"]');
    if (rewardsLink) {
      console.log('✅ 找到Rewards链接，点击...');
      rewardsLink.click();
      await this.waitForPageTransition();
      return true;
    }

    // 直接导航
    console.log('🔗 直接导航到Microsoft Rewards页面...');
    window.location.href = rewardsUrl;
    await this.waitForPageTransition();
    return true;
  }

  async waitForPageTransition() {
    return new Promise(resolve => {
      const timeout = setTimeout(resolve, 3000);
      this.addResource(() => clearTimeout(timeout));
    });
  }
}

/**
 * 奖励欢迎阶段处理器
 */
class RewardsWelcomeHandler extends StageHandler {
  constructor() {
    super(REGISTRATION_STAGES.REWARDS_WELCOME);
  }

  async execute(context) {
    console.log('🎁 处理奖励欢迎页面...');
    
    // 检查是否在奖励页面
    const isRewardsPage = this.detectRewardsPage();
    if (!isRewardsPage) {
      console.log('❌ 不在奖励页面');
      return false;
    }

    // 处理奖励页面操作
    const handled = await this.handleRewardsActions();
    if (!handled) {
      console.log('❌ 奖励页面处理失败');
      return false;
    }

    return true;
  }

  detectRewardsPage() {
    const indicators = [
      () => location.href.includes('rewards.microsoft.com'),
      () => location.href.includes('microsoft.com') && 
           (document.body.textContent.includes('奖励') || document.body.textContent.includes('rewards')),
      () => document.body.textContent.includes('积分') || document.body.textContent.includes('points'),
      () => document.querySelector('[data-testid*="rewards"]'),
      () => document.querySelector('[id*="rewards"]'),
      () => document.title.toLowerCase().includes('rewards')
    ];

    return indicators.some(indicator => indicator());
  }

  async handleRewardsActions() {
    const actions = [
      () => this.handleWelcomeModal(),
      () => this.clickRewardsItem(),
      () => this.handleTutorial(),
      () => this.completeRegistration()
    ];

    for (const action of actions) {
      try {
        const result = await action();
        if (result) {
          console.log('✅ 奖励页面操作成功');
          return true;
        }
      } catch (error) {
        console.error('❌ 奖励页面操作失败:', error);
      }
    }

    return false;
  }

  async handleWelcomeModal() {
    // 处理欢迎模态框
    const modalSelectors = [
      '.modal button[aria-label*="close"]',
      '.dialog button[data-testid*="close"]',
      'button[aria-label*="get started"]',
      'button[data-testid*="get-started"]',
      'button[class*="welcome"] button'
    ];

    for (const selector of modalSelectors) {
      const button = document.querySelector(selector);
      if (button && button.offsetParent !== null) {
        console.log(`✅ 处理欢迎模态框: ${selector}`);
        button.click();
        await this.waitForAction();
        return true;
      }
    }

    return false;
  }

  async clickRewardsItem() {
    // 点击任意奖励项目
    const itemSelectors = [
      '[data-testid*="reward-item"]',
      '[class*="reward-item"]',
      '[data-testid*="activity"]',
      '[class*="activity-item"]',
      '.reward-card',
      '.activity-card'
    ];

    for (const selector of itemSelectors) {
      const items = document.querySelectorAll(selector);
      if (items.length > 0) {
        const item = items[0]; // 点击第一个项目
        if (item.offsetParent !== null) {
          console.log(`✅ 点击奖励项目: ${selector}`);
          item.click();
          await this.waitForAction();
          return true;
        }
      }
    }

    // 尝试点击任何可点击的奖励相关元素
    const clickableElements = document.querySelectorAll('button, a, [role="button"]');
    for (const element of clickableElements) {
      const text = element.textContent.toLowerCase();
      if (text.includes('奖励') || text.includes('reward') || 
          text.includes('积分') || text.includes('point')) {
        console.log(`✅ 点击奖励相关元素: ${text}`);
        element.click();
        await this.waitForAction();
        return true;
      }
    }

    return false;
  }

  async handleTutorial() {
    // 处理新手教程
    const tutorialSelectors = [
      'button[data-testid*="tutorial-skip"]',
      'button[aria-label*="skip tutorial"]',
      'button[class*="tutorial"] button[class*="skip"]',
      'button:contains("跳过教程")',
      'button:contains("Skip tutorial")'
    ];

    for (const selector of tutorialSelectors) {
      const button = document.querySelector(selector);
      if (button && button.offsetParent !== null) {
        console.log(`✅ 跳过教程: ${selector}`);
        button.click();
        await this.waitForAction();
        return true;
      }
    }

    return false;
  }

  async completeRegistration() {
    // 标记注册完成
    console.log('🎉 Microsoft账号注册流程完成！');
    
    // 等待一段时间确保页面稳定
    await this.waitForAction();
    
    return true;
  }

  async waitForAction() {
    return new Promise(resolve => {
      const timeout = setTimeout(resolve, 2000);
      this.addResource(() => clearTimeout(timeout));
    });
  }
}

/**
 * 错误状态处理器
 */
class ErrorHandler extends StageHandler {
  constructor() {
    super(REGISTRATION_STAGES.ERROR);
  }

  async onEnter(context) {
    await super.onEnter(context);
    console.log('❌ 进入错误状态');
    
    // 记录错误信息
    this.logErrorDetails(context);
    
    return true;
  }

  async execute(context) {
    console.log('🔧 处理错误状态...');
    
    // 分析错误类型并尝试恢复
    const recoveryAction = this.determineRecoveryAction(context);
    
    if (recoveryAction) {
      console.log(`🔄 尝试错误恢复: ${recoveryAction.type}`);
      const recovered = await this.executeRecoveryAction(recoveryAction, context);
      
      if (recovered) {
        console.log('✅ 错误恢复成功');
        return true;
      }
    }

    console.log('❌ 无法自动恢复，需要手动干预');
    return false;
  }

  logErrorDetails(context) {
    console.log('📋 错误详情:');
    console.log('- 当前URL:', location.href);
    console.log('- 错误数量:', context.errors?.length || 0);
    console.log('- 尝试次数:', context.attempts || 0);
    console.log('- 最后错误:', context.lastError || '未知');
    
    if (context.errors && context.errors.length > 0) {
      console.log('- 错误历史:', context.errors);
    }
  }

  determineRecoveryAction(context) {
    // 根据错误类型确定恢复策略
    const lastError = context.lastError || '';
    
    if (lastError.includes('超时') || lastError.includes('timeout')) {
      return { type: 'retry', delay: 5000 };
    }
    
    if (lastError.includes('连接') || lastError.includes('connection')) {
      return { type: 'reconnect', delay: 3000 };
    }
    
    if (lastError.includes('验证码') || lastError.includes('code')) {
      return { type: 'retry_verification', delay: 2000 };
    }
    
    if (context.attempts < context.maxAttempts) {
      return { type: 'retry', delay: 2000 };
    }
    
    return null; // 无法恢复
  }

  async executeRecoveryAction(action, context) {
    try {
      switch (action.type) {
        case 'retry':
          await this.wait(action.delay);
          return true;
          
        case 'reconnect':
          // 重新连接IMAP服务
          await this.wait(action.delay);
          return true;
          
        case 'retry_verification':
          // 重试验证码获取
          await this.wait(action.delay);
          return true;
          
        default:
          return false;
      }
    } catch (error) {
      console.error('❌ 恢复操作失败:', error);
      return false;
    }
  }

  async wait(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// 导出处理器
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    LoginCompleteHandler,
    RewardsWelcomeHandler,
    ErrorHandler
  };
} else {
  // 浏览器环境 - 将类直接暴露到全局作用域
  window.LoginCompleteHandler = LoginCompleteHandler;
  window.RewardsWelcomeHandler = RewardsWelcomeHandler;
  window.ErrorHandler = ErrorHandler;

  // 同时保留命名空间对象
  window.RemainingStageHandlers = {
    LoginCompleteHandler,
    RewardsWelcomeHandler,
    ErrorHandler
  };
}
