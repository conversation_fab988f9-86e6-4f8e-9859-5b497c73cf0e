// RTBS - 内容脚本
console.log('🚀 RTBS内容脚本已加载 - 版本: 验证码时间戳修复版');

let hasDetectedData = false;
let detectionAttempts = 0;
const MAX_ATTEMPTS = 10;

// 检查是否是目标页面
function isTargetPage() {
  const url = window.location.href;
  return url.includes('rewards.bing.com') ||
         url.includes('bing.com/rewards') ||
         url.includes('www.bing.com/rewards');
}

// 检查是否是哔哩搜索页面
function isBiliSearchPage() {
  const url = window.location.href;
  return url.includes('cn.bing.com/search?q=%E5%93%94%E5%93%A9%E5%93%94%E5%93%A9&qs=n&form=BILREW') ||
         url.includes('cn.bing.com/search?q=哔哩哔哩') ||
         url.includes('cn.bing.com/search?q=%E5%93%94%E5%93%A9%E5%93%94%E5%93%A9');
}

// 检查是否是哔哩搜索结果页面（搜索完成后的页面）
function isBiliSearchResultPage() {
  const url = window.location.href;
  // 检查是否是cn.bing.com的搜索页面，且包含哔哩相关的搜索词
  if (!url.includes('cn.bing.com/search')) {
    return false;
  }

  // 检查URL中是否包含哔哩相关的搜索词（编码或未编码）
  const hasOriginalBili = url.includes('%E5%93%94%E5%93%A9%E5%93%94%E5%93%A9'); // 哔哩哔哩的编码
  const hasModifiedBili = /q=.*%E5%93%94.*%E5%93%A9.*%E5%93%94.*%E5%93%A9/.test(url); // 修改后的格式
  const hasDecodedBili = url.includes('哔') && url.includes('哩'); // 未编码的情况

  return hasOriginalBili || hasModifiedBili || hasDecodedBili;
}

// 检测用户账号
function detectUserAccount() {
  try {
    const accountElement = document.querySelector('#mectrl_currentAccount_secondary');
    return accountElement ? accountElement.textContent.trim() : null;
  } catch (error) {
    return null;
  }
}

// 检测搜索天数
function detectSearchDays() {
  try {
    const selector = '#more-activities > div > mee-card:nth-child(1) > div > card-content > mee-rewards-more-activities-card-item > div > a > div.contentContainer > p';
    const element = document.querySelector(selector);

    if (!element) return null;

    const text = element.textContent.trim();
    const dayMatch = text.match(/(\d+)\/(\d+)天/);

    if (dayMatch) {
      return {
        fullText: text,
        currentDays: parseInt(dayMatch[1]),
        totalDays: parseInt(dayMatch[2]),
        progress: `${dayMatch[1]}/${dayMatch[2]}`
      };
    }

    return null;
  } catch (error) {
    return null;
  }
}

// 执行检测并保存数据
function performDetection() {
  if (hasDetectedData || detectionAttempts >= MAX_ATTEMPTS) {
    return;
  }

  detectionAttempts++;

  const userAccount = detectUserAccount();
  const searchDays = detectSearchDays();

  if (userAccount && searchDays) {
    hasDetectedData = true;

    // 保存到本地存储
    const data = {
      userAccount: userAccount,
      searchDaysInfo: searchDays,
      timestamp: new Date().toISOString(),
      url: window.location.href
    };

    // 存储数据
    chrome.storage.local.set({
      'rewardTracker_lastData': data,
      'rewardTracker_timestamp': Date.now()
    });

    // 只在目标页面才发送保存文件的消息
    if (isTargetPage()) {
      chrome.runtime.sendMessage({
        action: 'dataDetected',
        ...data
      });
    }
  }
}

// 主检测函数
function startDetection() {
  // 立即检测一次
  performDetection();

  // 如果还没检测到数据，继续尝试
  if (!hasDetectedData && detectionAttempts < MAX_ATTEMPTS) {
    setTimeout(() => {
      startDetection();
    }, 2000);
  }
}

// 生成随机字符（小写字母或数字）
function getRandomChar() {
  const chars = 'abcdefghijklmnopqrstuvwxyz0123456789';
  return chars.charAt(Math.floor(Math.random() * chars.length));
}

// ==================== Microsoft账号注册功能 ====================

// 生成12位随机账号名（字母+数字）
function generateRandomAccount() {
  const chars = 'abcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  for (let i = 0; i < 12; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result + '@s4464.cfd';
}

// 生成随机年份（1995-2005）
function getRandomYear() {
  return Math.floor(Math.random() * 11) + 1995; // 1995-2005
}

// 生成随机月份（1-12）
function getRandomMonth() {
  return Math.floor(Math.random() * 12) + 1; // 1-12
}

// 生成随机日期（1-26）
function getRandomDay() {
  return Math.floor(Math.random() * 26) + 1; // 1-26
}

// 通用下拉框选择函数
function selectDropdownOption(dropdownSelector, targetValue, optionType = 'month') {
  return new Promise((resolve, reject) => {
    const dropdown = document.querySelector(dropdownSelector);
    if (!dropdown) {
      console.log(`未找到下拉框: ${dropdownSelector}`);
      reject(new Error(`Dropdown not found: ${dropdownSelector}`));
      return;
    }

    console.log(`找到${optionType}下拉框，准备点击`);
    dropdown.click();

    setTimeout(() => {
      // 尝试多种选择器查找选项
      let targetOption = null;
      const selectors = [
        `[role="option"][data-value="${targetValue}"]`,
        `[role="option"]:nth-child(${targetValue})`,
        `[role="listbox"] [role="option"]:nth-child(${targetValue})`,
        `[data-testid="${optionType}-${targetValue}"]`,
        `[aria-label*="${targetValue}${optionType === 'month' ? '月' : '日'}"]`,
        `[title*="${targetValue}${optionType === 'month' ? '月' : '日'}"]`
      ];

      for (const selector of selectors) {
        targetOption = document.querySelector(selector);
        if (targetOption) {
          console.log(`找到${optionType}选项，选择器: ${selector}`);
          break;
        }
      }

      // 如果还是找不到，尝试通过文本内容查找
      if (!targetOption) {
        const allOptions = document.querySelectorAll('[role="option"]');
        for (const option of allOptions) {
          const text = option.textContent.trim();
          const suffix = optionType === 'month' ? '月' : '日';
          if (text.includes(`${targetValue}${suffix}`) ||
              text === targetValue.toString() ||
              text === `${targetValue}${suffix}`) {
            targetOption = option;
            console.log(`通过文本内容找到${optionType}选项: "${text}"`);
            break;
          }
        }
      }

      if (targetOption) {
        targetOption.click();
        console.log(`已选择${optionType}: ${targetValue}`);
        resolve(targetValue);
      } else {
        console.log(`未找到${optionType}选项，输出调试信息:`);
        const allOptions = document.querySelectorAll('[role="option"]');
        console.log('所有可用选项:', Array.from(allOptions).map(opt => ({
          text: opt.textContent.trim(),
          value: opt.getAttribute('data-value'),
          ariaLabel: opt.getAttribute('aria-label')
        })));
        reject(new Error(`Option not found for ${optionType}: ${targetValue}`));
      }
    }, 800);
  });
}

// 改进的顺序选择生日函数（解决日期选择卡住问题）
function selectBirthDateSequentially(month, day) {
  console.log(`🎂 开始智能选择生日: ${month}月${day}日`);

  // 添加全局状态检查，防止重复执行
  if (window.birthDateSelectionInProgress) {
    console.log('⚠️ 生日选择已在进行中，跳过重复执行');
    return;
  }

  window.birthDateSelectionInProgress = true;

  // 使用Promise链确保顺序执行
  selectMonthWithRetry(month)
    .then(() => {
      console.log('✅ 月份选择完成，等待状态稳定后开始选择日期');
      // 增加额外等待时间确保月份选择完全稳定
      return new Promise(resolve => setTimeout(resolve, 2000));
    })
    .then(() => {
      console.log('🔍 开始选择日期');
      return selectDayWithRetry(day);
    })
    .then(() => {
      console.log('🎉 生日选择完成！');
      window.birthDateSelectionInProgress = false;
    })
    .catch(error => {
      console.error('❌ 生日选择失败:', error);
      window.birthDateSelectionInProgress = false;

      // 尝试备用方法
      console.log('🔄 尝试备用选择方法...');
      setTimeout(() => {
        selectBirthDateFallback(month, day);
      }, 2000); // 增加等待时间
    });
}

// 带重试机制的月份选择 - 全新改进版
function selectMonthWithRetry(month, maxRetries = 5) {
  return new Promise((resolve, reject) => {
    let retryCount = 0;

    function attemptMonthSelection() {
      retryCount++;
      console.log(`📅 尝试选择月份 (第${retryCount}/${maxRetries}次): ${month}月`);

      const monthDropdown = document.querySelector('button[name="BirthMonth"]');
      if (!monthDropdown) {
        console.log('❌ 未找到月份下拉框');
        if (retryCount < maxRetries) {
          setTimeout(attemptMonthSelection, 1500);
          return;
        }
        reject(new Error('未找到月份下拉框'));
        return;
      }

      // 检查是否已经选择了正确的月份
      const currentText = monthDropdown.textContent.trim();
      if (currentText.includes(`${month}月`)) {
        console.log(`✅ 月份已正确选择: ${currentText}`);
        setTimeout(() => resolve(), 500);
        return;
      }

      // 强制关闭所有可能打开的下拉框
      const allDropdowns = document.querySelectorAll('button[name="BirthMonth"], button[name="BirthDay"]');
      allDropdowns.forEach(dropdown => {
        if (dropdown.getAttribute('aria-expanded') === 'true') {
          console.log('🔄 关闭其他已打开的下拉框');
          dropdown.click();
        }
      });

      // 等待关闭完成后再打开月份下拉框
      setTimeout(() => {
        console.log('🖱️ 点击月份下拉框');
        monthDropdown.click();

        // 等待下拉框展开并查找选项
        setTimeout(() => {
          findAndSelectMonthOption();
        }, 800);
      }, 300);

      // 查找并选择月份选项
      function findAndSelectMonthOption() {
        console.log('🔍 开始查找月份选项...');

        // 多次尝试查找选项，因为选项可能需要时间加载
        let findAttempts = 0;
        const maxFindAttempts = 3;

        function tryFindOptions() {
          findAttempts++;
          console.log(`🔍 查找选项尝试 ${findAttempts}/${maxFindAttempts}`);

          const allOptions = document.querySelectorAll('[role="option"]');
          console.log(`🔍 找到 ${allOptions.length} 个选项`);

          if (allOptions.length === 0) {
            if (findAttempts < maxFindAttempts) {
              console.log('⏳ 选项未加载，等待后重试...');
              setTimeout(tryFindOptions, 1000);
              return;
            } else {
              console.log('❌ 多次尝试后仍未找到选项');
              if (retryCount < maxRetries) {
                setTimeout(attemptMonthSelection, 2000);
                return;
              }
              reject(new Error('月份选项加载失败'));
              return;
            }
          }

          // 查找匹配的月份选项
          let monthOption = null;
          for (const option of allOptions) {
            const text = option.textContent.trim();
            const dataValue = option.getAttribute('data-value');

            // 多种匹配方式
            if (text.includes(`${month}月`) ||
                text === month.toString() ||
                dataValue === month.toString()) {
              monthOption = option;
              console.log(`✅ 找到月份选项: "${text}" (data-value: ${dataValue})`);
              break;
            }
          }

          if (monthOption) {
            // 点击选项
            console.log(`🖱️ 点击月份选项: ${month}`);
            monthOption.click();

            // 验证选择结果
            setTimeout(() => {
              const updatedDropdown = document.querySelector('button[name="BirthMonth"]');
              const updatedText = updatedDropdown ? updatedDropdown.textContent.trim() : '';

              if (updatedText.includes(`${month}月`)) {
                console.log(`✅ 月份选择成功: ${updatedText}`);
                setTimeout(() => resolve(), 1000);
              } else {
                console.log(`⚠️ 月份选择验证失败，当前显示: ${updatedText}`);
                if (retryCount < maxRetries) {
                  setTimeout(attemptMonthSelection, 2000);
                } else {
                  reject(new Error(`月份选择失败，重试${maxRetries}次后仍未成功`));
                }
              }
            }, 1000);
          } else {
            console.log('❌ 未找到匹配的月份选项');
            console.log('📋 可用选项:', Array.from(allOptions).map(opt => ({
              text: opt.textContent.trim(),
              value: opt.getAttribute('data-value')
            })));

            if (retryCount < maxRetries) {
              setTimeout(attemptMonthSelection, 2000);
            } else {
              reject(new Error('未找到匹配的月份选项'));
            }
          }
        }

        tryFindOptions();
      }
    }

    attemptMonthSelection();
  });
}


// 带重试机制的日期选择 - 全新改进版
function selectDayWithRetry(day, maxRetries = 5) {
  return new Promise((resolve, reject) => {
    let retryCount = 0;

    function attemptDaySelection() {
      retryCount++;
      console.log(`📅 尝试选择日期 (第${retryCount}/${maxRetries}次): ${day}日`);

      // 首次尝试时额外等待月份选择稳定
      if (retryCount === 1) {
        console.log('⏰ 首次尝试日期选择，等待月份选择稳定...');
        setTimeout(() => continueAttempt(), 2000);
        return;
      }

      continueAttempt();

      function continueAttempt() {
        const dayDropdown = document.querySelector('button[name="BirthDay"]');
        if (!dayDropdown) {
          console.log('❌ 未找到日期下拉框');
          if (retryCount < maxRetries) {
            setTimeout(attemptDaySelection, 1500);
            return;
          }
          reject(new Error('未找到日期下拉框'));
          return;
        }

        // 检查是否已经选择了正确的日期
        const currentText = dayDropdown.textContent.trim();
        if (currentText.includes(`${day}日`)) {
          console.log(`✅ 日期已正确选择: ${currentText}`);
          setTimeout(() => resolve(), 500);
          return;
        }

        // 检查下拉框是否可用
        if (dayDropdown.disabled || dayDropdown.getAttribute('aria-disabled') === 'true') {
          console.log('⚠️ 日期下拉框被禁用，等待启用...');
          if (retryCount < maxRetries) {
            setTimeout(attemptDaySelection, 2000);
            return;
          }
          reject(new Error('日期下拉框被禁用'));
          return;
        }

        // 强制关闭所有可能打开的下拉框
        const allDropdowns = document.querySelectorAll('button[name="BirthMonth"], button[name="BirthDay"]');
        allDropdowns.forEach(dropdown => {
          if (dropdown.getAttribute('aria-expanded') === 'true') {
            console.log('🔄 关闭其他已打开的下拉框');
            dropdown.click();
          }
        });

        // 等待关闭完成后再打开日期下拉框
        setTimeout(() => {
          console.log('🖱️ 点击日期下拉框');
          dayDropdown.click();

          // 等待下拉框展开并查找选项
          setTimeout(() => {
            findAndSelectDayOption();
          }, 800);
        }, 300);

        // 查找并选择日期选项
        function findAndSelectDayOption() {
          console.log('🔍 开始查找日期选项...');

          // 多次尝试查找选项
          let findAttempts = 0;
          const maxFindAttempts = 3;

          function tryFindDayOptions() {
            findAttempts++;
            console.log(`🔍 查找日期选项尝试 ${findAttempts}/${maxFindAttempts}`);

            const allOptions = document.querySelectorAll('[role="option"]');
            console.log(`🔍 找到 ${allOptions.length} 个日期选项`);

            if (allOptions.length === 0) {
              if (findAttempts < maxFindAttempts) {
                console.log('⏳ 日期选项未加载，等待后重试...');
                setTimeout(tryFindDayOptions, 1000);
                return;
              } else {
                console.log('❌ 多次尝试后仍未找到日期选项');
                if (retryCount < maxRetries) {
                  setTimeout(attemptDaySelection, 2000);
                  return;
                }
                reject(new Error('日期选项加载失败'));
                return;
              }
            }

            // 查找匹配的日期选项
            let dayOption = null;
            for (const option of allOptions) {
              const text = option.textContent.trim();
              const dataValue = option.getAttribute('data-value');

              if (text.includes(`${day}日`) ||
                  text === day.toString() ||
                  dataValue === day.toString()) {
                dayOption = option;
                console.log(`✅ 找到日期选项: "${text}" (data-value: ${dataValue})`);
                break;
              }
            }

            if (dayOption) {
              console.log(`🖱️ 点击日期选项: ${day}`);
              dayOption.click();

              // 验证选择结果
              setTimeout(() => {
                const updatedDropdown = document.querySelector('button[name="BirthDay"]');
                const updatedText = updatedDropdown ? updatedDropdown.textContent.trim() : '';

                if (updatedText.includes(`${day}日`)) {
                  console.log(`✅ 日期选择成功: ${updatedText}`);
                  setTimeout(() => resolve(), 1000);
                } else {
                  console.log(`⚠️ 日期选择验证失败，当前显示: ${updatedText}`);
                  if (retryCount < maxRetries) {
                    setTimeout(attemptDaySelection, 2000);
                  } else {
                    reject(new Error(`日期选择失败，重试${maxRetries}次后仍未成功`));
                  }
                }
              }, 1000);
            } else {
              console.log('❌ 未找到匹配的日期选项');
              console.log('📋 可用日期选项:', Array.from(allOptions).map(opt => ({
                text: opt.textContent.trim(),
                value: opt.getAttribute('data-value')
              })));

              if (retryCount < maxRetries) {
                setTimeout(attemptDaySelection, 2000);
              } else {
                reject(new Error('未找到匹配的日期选项'));
              }
            }
          }

          tryFindDayOptions();
        }
      }
    }

    attemptDaySelection();
  });
}

// 备用生日选择方法（当主要方法失败时使用）
function selectBirthDateFallback(month, day) {
  console.log(`🔄 使用备用方法选择生日: ${month}月${day}日`);

  // 强制重置所有下拉框状态
  const allDropdowns = document.querySelectorAll('button[name="BirthMonth"], button[name="BirthDay"]');
  allDropdowns.forEach(dropdown => {
    dropdown.setAttribute('aria-expanded', 'false');
    dropdown.blur();
  });

  // 清除可能存在的选项面板
  const optionPanels = document.querySelectorAll('[role="listbox"]');
  optionPanels.forEach(panel => {
    if (panel.style) {
      panel.style.display = 'none';
    }
  });

  setTimeout(() => {
    // 尝试直接设置值（如果支持）
    const monthDropdown = document.querySelector('button[name="BirthMonth"]');
    const dayDropdown = document.querySelector('button[name="BirthDay"]');

    if (monthDropdown && dayDropdown) {
      console.log('🔧 尝试直接设置下拉框值');

      // 尝试设置月份
      try {
        monthDropdown.setAttribute('value', month);
        monthDropdown.textContent = `${month}月`;
        monthDropdown.dispatchEvent(new Event('change', { bubbles: true }));
        console.log(`✅ 直接设置月份: ${month}月`);
      } catch (error) {
        console.log('⚠️ 直接设置月份失败，使用点击方法');
        selectDropdownByForce(monthDropdown, month, '月');
      }

      setTimeout(() => {
        // 尝试设置日期
        try {
          dayDropdown.setAttribute('value', day);
          dayDropdown.textContent = `${day}日`;
          dayDropdown.dispatchEvent(new Event('change', { bubbles: true }));
          console.log(`✅ 直接设置日期: ${day}日`);
        } catch (error) {
          console.log('⚠️ 直接设置日期失败，使用点击方法');
          selectDropdownByForce(dayDropdown, day, '日');
        }
      }, 1000);
    }
  }, 500);
}

// 强制选择下拉框选项
function selectDropdownByForce(dropdown, targetValue, suffix) {
  console.log(`💪 强制选择${suffix}选项: ${targetValue}${suffix}`);

  dropdown.click();

  setTimeout(() => {
    const options = document.querySelectorAll('[role="option"]');
    console.log(`找到 ${options.length} 个选项`);

    for (const option of options) {
      const text = option.textContent.trim();
      if (text.includes(`${targetValue}${suffix}`) || text === targetValue.toString()) {
        console.log(`✅ 强制点击选项: ${text}`);
        option.click();
        return;
      }
    }

    // 如果还是找不到，选择第一个可用选项
    if (options.length > 0) {
      const firstOption = options[Math.min(targetValue - 1, options.length - 1)];
      if (firstOption) {
        console.log(`⚠️ 选择备用选项: ${firstOption.textContent.trim()}`);
        firstOption.click();
      }
    }
  }, 800);
}

// 保留原有的函数以确保向后兼容性
function selectDayAfterMonth(day) {
  console.log(`📅 调用旧版日期选择方法: ${day}日`);
  selectDayWithRetry(day).catch(error => {
    console.error('旧版日期选择失败:', error);
  });
}

// 全局错误恢复机制
function initializeErrorRecovery() {
  try {
    console.log('🔧 初始化错误恢复机制...');

    // 监听未捕获的错误
    window.addEventListener('error', function(event) {
      console.error('🚨 全局错误捕获:', event.error);
      try {
        handleGlobalError(event.error, 'javascript_error');
      } catch (e) {
        console.error('🚨 错误处理器本身出错:', e);
      }
    });

    // 监听未处理的Promise拒绝
    window.addEventListener('unhandledrejection', function(event) {
      console.error('🚨 未处理的Promise拒绝:', event.reason);
      try {
        handleGlobalError(event.reason, 'promise_rejection');
      } catch (e) {
        console.error('🚨 Promise拒绝处理器出错:', e);
      }
    });

    // 定期检查系统状态
    setInterval(checkSystemHealth, 30000); // 每30秒检查一次

    console.log('✅ 错误恢复机制初始化完成');
  } catch (error) {
    console.error('🚨 错误恢复机制初始化失败:', error);
  }
}

// 处理全局错误
function handleGlobalError(error, type) {
  console.log(`🔧 处理全局错误 (${type}):`, error);

  // 重置可能卡住的状态
  if (window.birthDateSelectionInProgress) {
    console.log('🔄 重置生日选择状态');
    window.birthDateSelectionInProgress = false;
  }

  if (window.longPressInProgress) {
    console.log('🔄 重置长按状态');
    window.longPressInProgress = false;
  }

  // 如果是选择器相关错误，尝试重新检测页面
  if (error.message && error.message.includes('querySelector')) {
    console.log('🔄 检测到选择器错误，尝试重新检测页面');
    setTimeout(() => {
      try {
        // 重新检测当前页面类型
        const currentState = detectCurrentPageState();
        if (currentState && currentState.type) {
          console.log('🔄 重新处理页面:', currentState.type);
          // 根据页面类型重新处理
          switch (currentState.type) {
            case 'personal_info':
              setTimeout(() => handlePersonalInfoPage(), 2000);
              break;
            case 'captcha':
              setTimeout(() => handleCaptchaPage(), 2000);
              break;
          }
        }
      } catch (retryError) {
        console.error('🚨 重试处理失败:', retryError);
      }
    }, 3000);
  }
}

// 系统健康检查
function checkSystemHealth() {
  console.log('🏥 执行系统健康检查...');

  // 检查是否有卡住的状态
  const stuckStates = [];

  if (window.birthDateSelectionInProgress) {
    stuckStates.push('生日选择');
  }

  if (window.longPressInProgress) {
    stuckStates.push('长按操作');
  }

  if (stuckStates.length > 0) {
    console.log('⚠️ 检测到可能卡住的状态:', stuckStates);

    // 重置状态
    window.birthDateSelectionInProgress = false;
    window.longPressInProgress = false;

    // 尝试重新检测和处理页面
    setTimeout(() => {
      try {
        const currentState = detectCurrentPageState();
        if (currentState && currentState.type) {
          console.log('🔄 健康检查触发页面重新处理:', currentState.type);
          // 重新处理当前页面
          handleMicrosoftRegistration();
        }
      } catch (error) {
        console.error('🚨 健康检查重试失败:', error);
      }
    }, 2000);
  } else {
    console.log('✅ 系统状态正常');
  }
}

// 生成随机姓氏（1-4位小写字母）
function generateRandomLastName() {
  const chars = 'abcdefghijklmnopqrstuvwxyz';
  const length = Math.floor(Math.random() * 4) + 1; // 1-4位
  let result = '';
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}

// 生成随机名字（2-4位小写字母+数字）
function generateRandomFirstName() {
  const chars = 'abcdefghijklmnopqrstuvwxyz0123456789';
  const length = Math.floor(Math.random() * 3) + 2; // 2-4位
  let result = '';
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}

// 检测是否为Microsoft注册页面
function isMicrosoftSignupPage() {
  const isSignupPage = window.location.href.includes('signup.live.com/signup');
  if (isSignupPage) {
    console.log('检测到Microsoft注册页面URL:', window.location.href);
    // 输出页面基本信息
    const bodyText = document.body ? document.body.textContent.substring(0, 200) : '';
    console.log('页面文本预览:', bodyText);
  }
  return isSignupPage;
}

// 检测是否为邮箱验证登录场景（而非新注册）
function isEmailVerificationLogin() {
  const currentUrl = window.location.href;
  const bodyText = document.body ? document.body.textContent || document.body.innerText : '';

  // 检查是否包含登录相关的关键词
  const hasLoginKeywords = bodyText.includes('登录') ||
                          bodyText.includes('Sign in') ||
                          bodyText.includes('验证你的电子邮件') ||
                          bodyText.includes('Verify your email');

  // 检查URL是否包含登录相关参数
  const hasLoginUrl = currentUrl.includes('login.live.com') ||
                     currentUrl.includes('oauth20_authorize');

  const isLoginScenario = hasLoginKeywords && hasLoginUrl;

  if (isLoginScenario) {
    console.log('🔍 检测到邮箱验证登录场景，不清除现有账号');
  }

  return isLoginScenario;
}

// 检测是否为全新的注册流程开始页面
function isNewRegistrationStart() {
  const currentUrl = window.location.href;
  const bodyText = document.body ? document.body.textContent || document.body.innerText : '';

  // 检查是否为注册页面URL
  const isSignupUrl = currentUrl.includes('signup.live.com/signup');

  // 检查页面是否包含"创建账户"或类似的新注册关键词
  const hasNewAccountKeywords = bodyText.includes('创建账户') ||
                               bodyText.includes('Create account') ||
                               bodyText.includes('获取新的电子邮件地址') ||
                               bodyText.includes('Get a new email address') ||
                               bodyText.includes('电子邮件') && bodyText.includes('下一个');

  // 检查是否有邮箱输入框（新注册的第一步）
  const hasEmailInput = document.querySelector('input[type="email"]') !== null ||
                       document.querySelector('input[name="MemberName"]') !== null;

  const isNewRegistration = isSignupUrl && hasNewAccountKeywords && hasEmailInput;

  if (isNewRegistration) {
    console.log('🆕 检测到全新的注册流程开始页面');
  }

  return isNewRegistration;
}

// 检测是否为数据许可页面
function isDataPermissionPage() {
  const titleElement = document.querySelector('h1[data-testid="title"]');
  if (titleElement && titleElement.textContent.includes('个人数据导出许可')) {
    console.log('检测到数据许可页面');
    return true;
  }
  return false;
}

// 检测是否为验证码页面
function isVerificationCodePage() {
  const currentStage = getCurrentStage();

  // 严格的阶段控制：只在特定阶段允许验证码检测
  if (currentStage !== 'verification_code' && currentStage !== 'none' && currentStage !== 'personal_info') {
    console.log(`🔒 当前阶段 ${currentStage}，跳过验证码检测`);
    return false;
  }

  // 检查多种可能的验证码输入框
  const codeEntryElement = document.querySelector('[data-testid="codeEntry"]');
  const codeEntry0 = document.querySelector('#codeEntry-0');
  const codeEntry1 = document.querySelector('#codeEntry-1');
  const codeInputs = document.querySelectorAll('input[id^="codeEntry-"]');

  const isCodePage = codeEntryElement !== null || codeEntry0 !== null || codeInputs.length > 0;

  if (isCodePage) {
    console.log('🔍 检测到验证码页面:', {
      currentStage: currentStage,
      codeEntryElement: !!codeEntryElement,
      codeEntry0: !!codeEntry0,
      codeEntry1: !!codeEntry1,
      codeInputsCount: codeInputs.length,
      url: window.location.href
    });
  }

  return isCodePage;
}

// 检测是否为邮件验证页面（包含"验证你的电子邮件"等文本）
function isEmailVerificationPage() {
  const currentStage = getCurrentStage();

  // 严格的阶段控制：只在特定阶段允许邮件验证检测
  if (currentStage !== 'email_verification' && currentStage !== 'verification_code' && currentStage !== 'none') {
    console.log(`🔒 当前阶段 ${currentStage}，跳过邮件验证检测`);
    return false;
  }

  const bodyText = document.body ? document.body.textContent || document.body.innerText : '';

  // 检查各种可能的邮件验证文本
  const verifyEmailTexts = [
    '验证你的电子邮件',
    '验证你的电子邮件地址',
    '输入我们发送到你的电子邮件地址的代码',
    '输入我们发送到你的电子邮件的代码',
    'Verify your email',
    'Enter the code we sent to your email',
    '重新发送代码'
  ];

  const matchedTexts = verifyEmailTexts.filter(text => bodyText.includes(text));
  const hasVerifyEmailText = matchedTexts.length > 0;

  if (hasVerifyEmailText) {
    console.log('🔍 检测到邮件验证页面:', {
      currentStage: currentStage,
      hasVerifyEmailText: hasVerifyEmailText,
      matchedTexts: matchedTexts,
      bodyTextPreview: bodyText.substring(0, 300),
      url: window.location.href
    });
  }

  return hasVerifyEmailText;
}

// 检测是否为个人信息页面
function isPersonalInfoPage() {
  const titleElement = document.querySelector('h1[data-testid="title"]');
  return titleElement && titleElement.textContent.includes('添加一些详细信息');
}

// 检测是否为姓名页面
function isNamePage() {
  const titleElement = document.querySelector('h1[data-testid="title"]');
  return titleElement && titleElement.textContent.includes('添加姓名');
}

// 检测是否为人机验证页面
function isCaptchaPage() {
  const currentStage = getCurrentStage();

  // 严格的阶段控制：只在特定阶段允许人机验证检测
  // 人机验证可能在verification_code、email_verification或captcha阶段出现
  if (currentStage !== 'captcha' && currentStage !== 'verification_code' &&
      currentStage !== 'email_verification' && currentStage !== 'none') {
    console.log(`🔒 当前阶段 ${currentStage}，跳过人机验证检测`);
    return false;
  }

  // 检查标题
  const titleElement = document.querySelector('h1[data-testid="title"]');
  if (titleElement && titleElement.textContent.includes('证明你不是机器人')) {
    console.log('🔍 通过标题检测到人机验证页面');
    return true;
  }

  // 检查是否有"按住"或"长按"文本
  const holdText = document.querySelector('#GlAInHXoXszrxIm, p');
  if (holdText && (holdText.textContent.includes('按住') || holdText.textContent.includes('长按'))) {
    return true;
  }

  // 检查是否有Human Challenge相关文本
  const challengeText = document.querySelector('[class*="KuFnlXkinpAhhMm"], span');
  if (challengeText && challengeText.textContent.includes('Human Challenge')) {
    return true;
  }

  // 检查是否有验证按钮
  const captchaButton = document.querySelector('#mWLJzzCQbwTPipB, [role="button"][aria-label*="按住"], [role="button"][aria-label*="长按"], [role="button"][aria-label*="Human Challenge"]');
  if (captchaButton) {
    return true;
  }

  // 检查新的可访问性挑战按钮
  const accessibilityButton = document.querySelector('[role="button"][aria-label*="可访问性挑战"], #gnnyqDwwEqnYTkM');
  if (accessibilityButton) {
    return true;
  }

  // 检查"再次按下"按钮
  const pressAgainButton = document.querySelector('[role="button"][aria-label*="再次按下"], #iJONTsjCTbadsDp, .CCovUDZBqqyRyvP');
  if (pressAgainButton) {
    return true;
  }

  // 检查是否有包含SVG的可访问性按钮
  const svgButton = document.querySelector('a[role="button"] svg, [tabindex="0"] svg');
  if (svgButton && svgButton.closest('[aria-label*="可访问性"]')) {
    return true;
  }

  // 检查页面中是否包含验证相关的关键词
  const bodyText = document.body.textContent;
  if (bodyText.includes('证明你不是机器人') || bodyText.includes('Human Challenge') ||
      (bodyText.includes('按住') && bodyText.includes('验证')) ||
      (bodyText.includes('长按') && bodyText.includes('按钮')) ||
      bodyText.includes('可访问性挑战') || bodyText.includes('再次按下')) {
    return true;
  }

  return false;
}

// 检测是否为登录完成页面
function isLoginCompletePage() {
  const titleElement = document.querySelector('h1[data-testid="title"]');
  return titleElement && titleElement.textContent.includes('使用人脸、指纹或 PIN 更快地登录');
}

// 检测是否为Microsoft Rewards欢迎页面
function isRewardsWelcomePage() {
  return document.querySelector('h2.c-subheading-4.welcome-tour-title') !== null;
}

/**
 * Microsoft注册系统 v3.0 - Content Script
 * 完全重写的内容脚本，基于新的状态机架构
 *
 * <AUTHOR> Team
 * @version 3.0.0
 * @description 替换旧版本的content.js，使用新的阶段化处理系统
 */

// 检查是否已加载新系统
if (typeof window.MicrosoftRegistrationSystemV3 === 'undefined') {
  console.error('❌ Microsoft注册系统 v3.0 未加载，请确保相关文件已正确引入');
} else {
  console.log('✅ Microsoft注册系统 v3.0 已加载');
}

// 兼容性：保留旧版本的状态结构以供popup使用
let msRegistrationState = {
  currentAccount: null,
  currentStep: 'none',
  attempts: 0,
  maxAttempts: 3,
  verificationStatus: '等待中',
  registrationComplete: false,
  lastProcessedUrl: null,
  lastProcessedStep: null,
  detectionInterval: null,
  isProcessing: false,
  // 标记为v3系统
  version: '3.0.0',
  isV3System: true,
  currentStageIndex: -1,
  stageCompleted: {},
  // 阶段序列定义
  stageSequence: [
    'data_permission',
    'signup',
    'email_verification',
    'verification_code',
    'personal_info',
    'name',
    'captcha',
    'login_complete',
    'rewards_welcome'
  ]
};

// 新系统实例
let registrationSystemV3 = null;

/**
 * 初始化v3系统
 */
function initializeV3System() {
  if (registrationSystemV3) {
    return registrationSystemV3;
  }

  try {
    if (typeof getMicrosoftRegistrationSystemV3 === 'function') {
      registrationSystemV3 = getMicrosoftRegistrationSystemV3();

      // 设置状态同步
      setupV3StateSync();

      console.log('✅ v3系统初始化完成');
      return registrationSystemV3;
    } else {
      console.warn('⚠️ v3系统未加载，使用兼容模式');
      return null;
    }

  } catch (error) {
    console.error('❌ v3系统初始化失败:', error);
    return null;
  }
}

/**
 * 设置v3系统状态同步
 */
function setupV3StateSync() {
  if (!registrationSystemV3) return;

  // 监听v3系统状态变化
  window.addEventListener('microsoftRegistrationSystemReady', (event) => {
    console.log('🎉 v3系统就绪');
    syncStateFromV3();
  });

  // 定期同步状态
  setInterval(() => {
    if (registrationSystemV3 && registrationSystemV3.isInitialized) {
      syncStateFromV3();
    }
  }, 1000);
}

/**
 * 从v3系统同步状态
 */
function syncStateFromV3() {
  if (!registrationSystemV3 || !registrationSystemV3.controller) {
    return;
  }

  try {
    const v3Status = registrationSystemV3.getSystemStatus();
    const controllerStatus = v3Status.controller;

    if (controllerStatus) {
      msRegistrationState.isProcessing = controllerStatus.isRunning;
      msRegistrationState.currentStep = mapV3StageToOldStep(controllerStatus.currentStage);
      msRegistrationState.currentAccount = controllerStatus.context?.account || null;
      msRegistrationState.attempts = controllerStatus.context?.attempts || 0;
      msRegistrationState.registrationComplete = controllerStatus.currentStage === 'completed';

      // 保存状态
      saveMicrosoftRegistrationState();
    }
  } catch (error) {
    console.error('❌ 状态同步失败:', error);
  }
}

/**
 * 映射v3阶段到旧版本步骤
 */
function mapV3StageToOldStep(v3Stage) {
  const mapping = {
    'idle': 'none',
    'data_permission': 'data_permission',
    'email_input': 'signup',
    'email_verification': 'email_verification',
    'verification_code': 'verification_code',
    'personal_info': 'personal_info',
    'name_info': 'name',
    'captcha': 'captcha',
    'login_complete': 'login_complete',
    'rewards_welcome': 'rewards_welcome',
    'completed': 'completed',
    'error': 'error'
  };

  return mapping[v3Stage] || v3Stage;
}

// 页面加载完成后自动初始化
document.addEventListener('DOMContentLoaded', () => {
  console.log('📄 DOM加载完成，初始化Microsoft注册系统...');

  // 检查是否在Microsoft相关页面
  if (location.href.includes('microsoft.com') ||
      location.href.includes('live.com') ||
      location.href.includes('outlook.com')) {

    console.log('🎯 检测到Microsoft页面，初始化v3系统...');

    // 延迟初始化，确保所有脚本都已加载
    setTimeout(() => {
      initializeV3System();
    }, 1000);
  }
});

// 如果DOM已经加载完成，立即初始化
if (document.readyState === 'complete' || document.readyState === 'interactive') {
  setTimeout(() => {
    if (location.href.includes('microsoft.com') ||
        location.href.includes('live.com') ||
        location.href.includes('outlook.com')) {
      console.log('🎯 页面已加载，初始化v3系统...');
      initializeV3System();
    }
  }, 1000);
}

/**
 * 启动Microsoft注册流程 - v3版本
 */
async function startMicrosoftRegistrationV3() {
  console.log('🚀 启动Microsoft注册流程 v3.0...');

  // 初始化v3系统
  const system = initializeV3System();
  if (!system) {
    console.warn('⚠️ v3系统不可用，回退到兼容模式');
    return startMicrosoftRegistrationCompatMode();
  }

  try {
    // 启动v3注册流程
    const result = await system.startRegistration();
    if (result) {
      console.log('✅ v3注册流程启动成功');
      msRegistrationState.isProcessing = true;
      msRegistrationState.currentStep = 'initializing';
      saveMicrosoftRegistrationState();
      return true;
    } else {
      console.error('❌ v3注册流程启动失败');
      return false;
    }
  } catch (error) {
    console.error('❌ v3注册流程启动异常:', error);
    return false;
  }
}

/**
 * 停止Microsoft注册流程 - v3版本
 */
async function stopMicrosoftRegistrationV3() {
  console.log('🛑 停止Microsoft注册流程 v3.0...');

  if (registrationSystemV3) {
    try {
      const result = await registrationSystemV3.stopRegistration();
      if (result) {
        console.log('✅ v3注册流程停止成功');
        msRegistrationState.isProcessing = false;
        msRegistrationState.currentStep = 'none';
        saveMicrosoftRegistrationState();
        return true;
      }
    } catch (error) {
      console.error('❌ v3注册流程停止异常:', error);
    }
  }

  // 回退到旧版本停止逻辑
  return stopMicrosoftRegistrationCompatMode();
}

/**
 * 兼容模式：启动注册流程
 */
function startMicrosoftRegistrationCompatMode() {
  console.log('🔄 使用兼容模式启动注册流程...');

  // 这里保留旧版本的启动逻辑作为后备
  msRegistrationState.isProcessing = true;
  msRegistrationState.currentStep = 'detecting';
  saveMicrosoftRegistrationState();

  // 启动旧版本的检测逻辑
  startMicrosoftRegistrationDetection();

  return true;
}

/**
 * 兼容模式：停止注册流程
 */
function stopMicrosoftRegistrationCompatMode() {
  console.log('🔄 使用兼容模式停止注册流程...');

  // 停止旧版本的检测
  if (msRegistrationState.detectionInterval) {
    clearInterval(msRegistrationState.detectionInterval);
    msRegistrationState.detectionInterval = null;
  }

  msRegistrationState.isProcessing = false;
  msRegistrationState.currentStep = 'none';
  saveMicrosoftRegistrationState();

  return true;
}

/**
 * 检查是否应该使用v3系统
 */
function shouldUseV3System() {
  // 检查v3系统是否可用
  return typeof getMicrosoftRegistrationSystemV3 === 'function' &&
         typeof window.MicrosoftRegistrationSystemV3 !== 'undefined';
}

/**
 * 统一的注册流程启动函数
 */
async function startMicrosoftRegistration() {
  if (shouldUseV3System()) {
    return await startMicrosoftRegistrationV3();
  } else {
    return startMicrosoftRegistrationCompatMode();
  }
}

/**
 * 统一的注册流程停止函数
 */
async function stopMicrosoftRegistration() {
  if (shouldUseV3System() && registrationSystemV3) {
    return await stopMicrosoftRegistrationV3();
  } else {
    return stopMicrosoftRegistrationCompatMode();
  }
}

/**
 * 获取注册状态
 */
function getMicrosoftRegistrationStatus() {
  if (registrationSystemV3 && registrationSystemV3.isInitialized) {
    // 返回v3系统状态
    const v3Status = registrationSystemV3.getSystemStatus();
    return {
      ...msRegistrationState,
      v3Status,
      systemVersion: '3.0.0'
    };
  } else {
    // 返回兼容模式状态
    return {
      ...msRegistrationState,
      systemVersion: '2.x-compat'
    };
  }
}

/**
 * 重置注册流程
 */
async function resetMicrosoftRegistration() {
  console.log('🔄 重置Microsoft注册流程...');

  try {
    // 先停止当前流程
    await stopMicrosoftRegistration();

    // 如果有v3系统，重置它
    if (registrationSystemV3) {
      await registrationSystemV3.resetSystem();
    }

    // 重置状态
    msRegistrationState = {
      currentAccount: null,
      currentStep: 'none',
      attempts: 0,
      maxAttempts: 3,
      verificationStatus: '等待中',
      registrationComplete: false,
      lastProcessedUrl: null,
      lastProcessedStep: null,
      detectionInterval: null,
      isProcessing: false,
      version: '3.0.0',
      isV3System: true,
      currentStageIndex: -1,
      stageCompleted: {},
      // 阶段序列定义
      stageSequence: [
        'data_permission',
        'signup',
        'email_verification',
        'verification_code',
        'personal_info',
        'name',
        'captcha',
        'login_complete',
        'rewards_welcome'
      ]
    };

    // 清除本地存储
    clearMicrosoftRegistrationData();

    console.log('✅ 注册流程重置完成');
    return true;

  } catch (error) {
    console.error('❌ 重置注册流程失败:', error);
    return false;
  }
}

// 状态迁移函数 - 处理从旧状态到新阶段化状态机的迁移
function migrateOldStateToNewStage() {
  const currentStep = msRegistrationState.currentStep;

  // 如果是旧的waiting_verification状态，需要迁移到新的阶段化状态机
  if (currentStep === 'waiting_verification') {
    console.log('🔄 检测到旧状态 waiting_verification，迁移到新的阶段化状态机');

    // 检查当前页面类型来确定正确的阶段
    if (isVerificationCodePage()) {
      setCurrentStage('verification_code');
      console.log('✅ 迁移到 verification_code 阶段');
    } else if (isEmailVerificationPage()) {
      setCurrentStage('email_verification');
      console.log('✅ 迁移到 email_verification 阶段');
    } else {
      // 默认迁移到邮件验证阶段
      setCurrentStage('email_verification');
      console.log('✅ 默认迁移到 email_verification 阶段');
    }
    return true;
  }

  return false;
}

// 统一的状态设置函数 - 确保 currentStep 和 currentStageIndex 始终同步
function setCurrentStage(stage, skipValidation = false) {
  // 验证阶段是否有效
  if (!skipValidation && !msRegistrationState.stageSequence.includes(stage) && stage !== 'none' && stage !== 'completed') {
    console.error(`❌ 无效的阶段: ${stage}`);
    return false;
  }

  const oldStage = msRegistrationState.currentStep;
  const oldIndex = msRegistrationState.currentStageIndex;

  // 设置新阶段
  msRegistrationState.currentStep = stage;

  // 设置对应的阶段索引
  if (stage === 'none') {
    msRegistrationState.currentStageIndex = -1;
  } else if (stage === 'completed') {
    msRegistrationState.currentStageIndex = msRegistrationState.stageSequence.length;
  } else {
    msRegistrationState.currentStageIndex = msRegistrationState.stageSequence.indexOf(stage);
  }

  console.log(`🔄 状态变更: ${oldStage}(${oldIndex}) -> ${stage}(${msRegistrationState.currentStageIndex})`);

  // 记录详细状态信息
  logDetailedState(`状态设置: ${stage}`);

  // 保存状态
  saveMicrosoftRegistrationState();
  return true;
}

// 阶段管理函数
function getCurrentStage() {
  // 首先尝试迁移旧状态
  migrateOldStateToNewStage();

  return msRegistrationState.currentStep;
}

function getExpectedStage() {
  // 首先确保状态已迁移
  const currentStage = getCurrentStage();

  // 如果当前阶段已完成，返回下一个阶段
  if (msRegistrationState.stageCompleted[currentStage]) {
    const currentIndex = msRegistrationState.stageSequence.indexOf(currentStage);
    if (currentIndex >= 0 && currentIndex < msRegistrationState.stageSequence.length - 1) {
      return msRegistrationState.stageSequence[currentIndex + 1];
    }
    return 'completed';
  }

  // 如果当前阶段未完成，返回当前阶段
  if (msRegistrationState.currentStageIndex >= 0 &&
      msRegistrationState.currentStageIndex < msRegistrationState.stageSequence.length) {
    return msRegistrationState.stageSequence[msRegistrationState.currentStageIndex];
  }

  // 如果没有开始任何阶段，返回第一个阶段
  if (msRegistrationState.currentStageIndex === -1) {
    return msRegistrationState.stageSequence[0]; // 'data_permission'
  }

  return 'none';
}

function isValidStageTransition(fromStage, toStage) {
  const allowedNext = msRegistrationState.allowedTransitions[fromStage] || [];
  return allowedNext.includes(toStage);
}

function advanceToNextStage(completedStage) {
  console.log(`🎯 完成阶段: ${completedStage}`);

  // 记录推进前的状态
  logDetailedState(`推进前: ${completedStage}`);

  // 标记当前阶段为已完成
  msRegistrationState.stageCompleted[completedStage] = true;

  // 找到下一个阶段
  const currentIndex = msRegistrationState.stageSequence.indexOf(completedStage);
  if (currentIndex >= 0 && currentIndex < msRegistrationState.stageSequence.length - 1) {
    const nextStage = msRegistrationState.stageSequence[currentIndex + 1];
    setCurrentStage(nextStage);
    console.log(`➡️ 进入下一阶段: ${nextStage}`);
  } else if (currentIndex === msRegistrationState.stageSequence.length - 1) {
    // 最后一个阶段完成
    setCurrentStage('completed');
    msRegistrationState.registrationComplete = true;
    console.log('🎉 所有阶段完成！');
  }

  // 记录推进后的状态
  logDetailedState(`推进后: ${completedStage}`);
}

function shouldProcessStage(detectedStage) {
  const currentStage = getCurrentStage();
  const expectedStage = getExpectedStage();

  console.log(`🔍 阶段检查: 当前=${currentStage}, 期望=${expectedStage}, 检测到=${detectedStage}`);

  // 特殊处理：人机验证页面总是允许处理，因为它可能在任何阶段出现
  if (detectedStage === 'captcha') {
    console.log(`🔒 检测到人机验证页面，总是允许处理`);
    return true;
  }

  // 如果检测到的阶段就是我们期望的阶段，允许处理
  if (detectedStage === expectedStage) {
    console.log(`✅ 检测到期望的阶段: ${detectedStage}`);
    return true;
  }

  // 如果检测到的阶段是当前阶段，且还未完成，允许继续处理
  if (detectedStage === currentStage && !msRegistrationState.stageCompleted[detectedStage]) {
    console.log(`🔄 继续处理当前阶段: ${detectedStage}`);
    return true;
  }

  // 如果是有效的阶段转换，允许处理
  if (isValidStageTransition(currentStage, detectedStage)) {
    console.log(`➡️ 有效的阶段转换: ${currentStage} -> ${detectedStage}`);
    return true;
  }

  // 特殊情况：如果当前阶段是none或未初始化，且检测到的是第一个阶段，允许处理
  if ((currentStage === 'none' || msRegistrationState.currentStageIndex === -1) &&
      detectedStage === msRegistrationState.stageSequence[0]) {
    console.log(`🚀 开始第一个阶段: ${detectedStage}`);
    return true;
  }

  console.log(`⏭️ 跳过阶段处理: ${detectedStage} (当前阶段: ${currentStage})`);
  return false;
}

// 统一的状态重置函数
function resetMicrosoftRegistrationState() {
  console.log('🔄 重置Microsoft注册状态');

  // 停止所有正在进行的服务
  if (imapService.isWaitingForCode) {
    console.log('🛑 停止IMAP邮件检查服务');
    stopImapEmailCheck();
  }

  if (msRegistrationState.detectionInterval) {
    console.log('🛑 停止页面检测定时器');
    clearInterval(msRegistrationState.detectionInterval);
    msRegistrationState.detectionInterval = null;
  }

  // 重置所有状态字段
  msRegistrationState.currentAccount = null;
  setCurrentStage('none');
  msRegistrationState.attempts = 0;
  msRegistrationState.verificationStatus = '等待中';
  msRegistrationState.registrationComplete = false;
  msRegistrationState.lastProcessedUrl = null;
  msRegistrationState.lastProcessedStep = null;
  msRegistrationState.isProcessing = false;
  msRegistrationState.stageCompleted = {};

  // 重置IMAP服务状态
  imapService.isWaitingForCode = false;
  imapService.currentEmail = null;
  imapService.checkInterval = null;
  imapService.retryCount = 0;
  imapService.lastCheckTime = null;
  imapService.consecutiveFailures = 0;

  console.log('✅ Microsoft注册状态重置完成');
}

// 状态一致性验证函数
function validateStateConsistency() {
  const issues = [];

  // 检查 currentStep 和 currentStageIndex 是否一致
  if (msRegistrationState.currentStep !== 'none' && msRegistrationState.currentStep !== 'completed') {
    const expectedIndex = msRegistrationState.stageSequence.indexOf(msRegistrationState.currentStep);
    if (expectedIndex !== msRegistrationState.currentStageIndex) {
      issues.push(`状态不一致: currentStep=${msRegistrationState.currentStep}, currentStageIndex=${msRegistrationState.currentStageIndex}, 期望索引=${expectedIndex}`);
    }
  }

  // 检查已完成阶段的逻辑性
  const completedStages = Object.keys(msRegistrationState.stageCompleted).filter(stage => msRegistrationState.stageCompleted[stage]);
  for (const stage of completedStages) {
    const stageIndex = msRegistrationState.stageSequence.indexOf(stage);
    if (stageIndex > msRegistrationState.currentStageIndex) {
      issues.push(`逻辑错误: 阶段 ${stage}(索引${stageIndex}) 已完成，但当前阶段索引为 ${msRegistrationState.currentStageIndex}`);
    }
  }

  // 检查必要字段
  if (msRegistrationState.currentStep && !msRegistrationState.verificationStatus) {
    issues.push('缺少验证状态信息');
  }

  if (issues.length > 0) {
    console.warn('🚨 状态一致性检查发现问题:');
    issues.forEach(issue => console.warn(`  - ${issue}`));
    return false;
  }

  console.log('✅ 状态一致性检查通过');
  return true;
}

// 详细的状态调试信息
function logDetailedState(context = '') {
  console.group(`📊 详细状态信息 ${context ? `(${context})` : ''}`);
  console.log('当前阶段:', msRegistrationState.currentStep);
  console.log('阶段索引:', msRegistrationState.currentStageIndex);
  console.log('已完成阶段:', msRegistrationState.stageCompleted);
  console.log('当前账号:', msRegistrationState.currentAccount);
  console.log('验证状态:', msRegistrationState.verificationStatus);
  console.log('注册完成:', msRegistrationState.registrationComplete);
  console.log('正在处理:', msRegistrationState.isProcessing);
  console.log('IMAP状态:', {
    isWaiting: imapService.isWaitingForCode,
    currentEmail: imapService.currentEmail,
    retryCount: imapService.retryCount
  });
  console.groupEnd();

  // 执行状态一致性检查
  validateStateConsistency();
}

// 保存Microsoft注册状态到storage
function saveMicrosoftRegistrationState() {
  // 在保存前进行状态验证
  validateStateConsistency();

  chrome.storage.local.set({
    'microsoft_registration_status': {
      currentAccount: msRegistrationState.currentAccount,
      currentStep: msRegistrationState.currentStep,
      currentStageIndex: msRegistrationState.currentStageIndex,
      stageCompleted: msRegistrationState.stageCompleted,
      attempts: msRegistrationState.attempts,
      verificationStatus: msRegistrationState.verificationStatus,
      registrationComplete: msRegistrationState.registrationComplete,
      timestamp: new Date().toISOString()
    }
  });
}

// 处理数据许可页面
function handleDataPermissionPage() {
  console.log('检测到数据许可页面');

  // 使用统一的状态管理函数设置当前阶段
  setCurrentStage('data_permission');

  // 查找"同意并继续"按钮
  const agreeButton = document.querySelector('button[data-testid="primaryButton"]#nextButton');
  if (agreeButton && agreeButton.textContent.includes('同意并继续')) {
    console.log('找到"同意并继续"按钮，准备点击');

    // 等待一下再点击，确保页面完全加载
    setTimeout(() => {
      agreeButton.click();
      console.log('已点击"同意并继续"按钮');
      // 不再设置临时状态，让页面跳转后自然检测到signup阶段
    }, 1000);
  } else {
    console.log('未找到"同意并继续"按钮');
  }
}

// 处理Microsoft注册页面
function handleMicrosoftSignupPage() {
  console.log('检测到Microsoft注册页面');

  // 检查是否为全新的注册流程开始
  const isNewRegistrationStartPage = isNewRegistrationStart();
  const hasExistingAccount = msRegistrationState.currentAccount !== null;
  const isLoginScenario = isEmailVerificationLogin();

  // 如果是全新的注册流程开始且已有账号信息，但不是邮箱验证登录场景，说明是新的注册流程，需要清除之前的状态
  if (isNewRegistrationStartPage && hasExistingAccount && !isLoginScenario) {
    console.log('🔄 检测到全新的注册流程开始，清除之前的账号状态');
    console.log('🗑️ 清除之前的账号:', msRegistrationState.currentAccount);
    console.log('🗑️ 清除之前的IMAP邮箱:', imapService.currentEmail);

    // 使用统一的重置函数
    resetMicrosoftRegistrationState();

    // 清除本地存储中的旧状态
    chrome.storage.local.remove(['microsoft_registration_status'], () => {
      console.log('✅ 已清除本地存储中的旧注册状态');
    });

    console.log('✅ 完成新注册流程的状态清除');
  } else if (isLoginScenario && hasExistingAccount) {
    console.log('📧 检测到邮箱验证登录场景，保留现有账号:', msRegistrationState.currentAccount);
  } else if (hasExistingAccount) {
    console.log('📋 继续使用现有账号进行注册流程:', msRegistrationState.currentAccount);
  }

  // 生成新账号（只有在没有账号时才生成）
  if (!msRegistrationState.currentAccount) {
    msRegistrationState.currentAccount = generateRandomAccount();
    setCurrentStage('signup');
    console.log('🆕 生成新账号:', msRegistrationState.currentAccount);

    // 确保IMAP服务使用新的邮箱
    imapService.currentEmail = msRegistrationState.currentAccount;
    console.log('📧 更新IMAP服务邮箱:', imapService.currentEmail);

    saveMicrosoftRegistrationState();
  } else {
    console.log('📋 使用现有账号:', msRegistrationState.currentAccount);

    // 确保IMAP服务邮箱与当前账号一致
    if (imapService.currentEmail !== msRegistrationState.currentAccount) {
      console.log('🔄 同步IMAP服务邮箱:', msRegistrationState.currentAccount);
      imapService.currentEmail = msRegistrationState.currentAccount;
    }
  }

  // 查找邮箱输入框 - 尝试多种选择器
  const emailSelectors = [
    'input[type="email"][name="电子邮件"]',
    'input[type="email"]',
    'input[name="MemberName"]',
    'input[name="email"]',
    'input[placeholder*="邮件"]',
    'input[placeholder*="email"]'
  ];

  let emailInput = null;
  for (const selector of emailSelectors) {
    emailInput = document.querySelector(selector);
    if (emailInput) {
      console.log('找到邮箱输入框，选择器:', selector);
      break;
    }
  }

  if (emailInput && emailInput.value === '') {
    emailInput.value = msRegistrationState.currentAccount;
    emailInput.dispatchEvent(new Event('input', { bubbles: true }));
    emailInput.dispatchEvent(new Event('change', { bubbles: true }));
    emailInput.dispatchEvent(new Event('blur', { bubbles: true }));

    console.log('已填写邮箱:', msRegistrationState.currentAccount);

    // 点击下一步按钮 - 尝试多种选择器
    setTimeout(() => {
      const buttonSelectors = [
        'button[type="submit"][data-testid="primaryButton"]',
        'button[type="submit"]',
        'button[data-testid="primaryButton"]',
        'input[type="submit"]'
      ];

      let nextButton = null;
      for (const selector of buttonSelectors) {
        nextButton = document.querySelector(selector);
        if (nextButton && (nextButton.textContent.includes('下一个') || nextButton.textContent.includes('Next') || nextButton.textContent.includes('继续'))) {
          console.log('找到下一步按钮，选择器:', selector);
          break;
        }
      }

      if (nextButton && !nextButton.disabled) {
        nextButton.click();
        console.log('已点击下一步按钮');
        // 使用新的阶段化状态机，进入邮件验证阶段
        advanceToNextStage('signup');
        saveMicrosoftRegistrationState();

        // 邮箱提交成功后清除随机账号生成数据
        setTimeout(() => {
          clearRandomAccountData();
        }, 3000);

      } else {
        console.log('未找到可用的下一步按钮');
        // 输出调试信息
        const allButtons = document.querySelectorAll('button');
        console.log('页面上所有按钮:', Array.from(allButtons).map(btn => ({
          text: btn.textContent.trim(),
          disabled: btn.disabled,
          type: btn.type,
          testId: btn.getAttribute('data-testid')
        })));
      }
    }, 2000); // 增加等待时间
  } else {
    console.log('未找到邮箱输入框或已填写');
    // 输出调试信息
    const allInputs = document.querySelectorAll('input');
    console.log('页面上所有输入框:', Array.from(allInputs).map(input => ({
      type: input.type,
      name: input.name,
      placeholder: input.placeholder,
      value: input.value
    })));
  }
}

// 真实IMAP邮件服务（增强版本）
let imapService = {
  isWaitingForCode: false,
  currentEmail: null,
  checkInterval: null,
  maxWaitTime: 90000, // 最大等待90秒
  startTime: null,
  serverUrl: 'http://localhost:3000', // IMAP服务器地址
  retryCount: 0,
  maxRetries: 5,
  checkIntervalTime: 3000, // 检查间隔3秒
  lastCheckTime: null,
  consecutiveFailures: 0,
  maxConsecutiveFailures: 3
};

// 启动IMAP邮件检查（增强版本）
function startImapEmailCheck(email) {
  console.log('🚀 启动真实IMAP邮件检查:', email);
  console.log('🌐 服务器URL:', imapService.serverUrl);

  // 重置状态
  imapService.isWaitingForCode = true;
  imapService.currentEmail = email;
  imapService.startTime = Date.now();
  imapService.retryCount = 0;
  imapService.consecutiveFailures = 0;
  imapService.lastCheckTime = null;

  // 异步执行清理和初始化
  (async () => {
    try {
      // 首先强制清理旧的验证码缓存
      console.log('🧹 强制清理旧验证码缓存...');
      await clearOldVerificationCodes(email);

      // 等待一下确保缓存清理完成
      await new Promise(resolve => setTimeout(resolve, 1000));

      // 然后触发服务器检查邮件
      console.log('📧 触发服务器检查邮件...');
      triggerEmailCheck(email);
    } catch (error) {
      console.error('❌ 初始化IMAP检查失败:', error);
    }
  })();

  // 定期检查验证码（使用动态间隔）
  console.log(`⏰ 设置定期检查验证码，初始间隔: ${imapService.checkIntervalTime}ms`);
  imapService.checkInterval = setInterval(() => {
    const elapsedTime = Date.now() - imapService.startTime;
    console.log(`🔍 执行定期验证码检查... (已等待: ${Math.floor(elapsedTime / 1000)}秒)`);
    checkForVerificationCode();
  }, imapService.checkIntervalTime);

  // 设置超时
  console.log(`⏱️ 设置超时时间: ${imapService.maxWaitTime}ms`);
  setTimeout(() => {
    if (imapService.isWaitingForCode) {
      console.log('⏰ IMAP邮件检查超时');
      stopImapEmailCheck();
      msRegistrationState.verificationStatus = `邮件检查超时 (重试${imapService.retryCount}次)`;
      saveMicrosoftRegistrationState();
    }
  }, imapService.maxWaitTime);
}

// 停止IMAP邮件检查（增强版本）
function stopImapEmailCheck() {
  console.log('🛑 停止IMAP邮件检查');

  if (imapService.checkInterval) {
    clearInterval(imapService.checkInterval);
    imapService.checkInterval = null;
    console.log('✅ IMAP检查定时器已清除');
  }

  // 记录统计信息
  if (imapService.startTime) {
    const totalTime = Date.now() - imapService.startTime;
    console.log(`📊 IMAP检查统计: 总时间${Math.floor(totalTime / 1000)}秒, 重试${imapService.retryCount}次, 连续失败${imapService.consecutiveFailures}次`);
  }

  // 重置状态
  imapService.isWaitingForCode = false;
  imapService.currentEmail = null;
  imapService.retryCount = 0;
  imapService.consecutiveFailures = 0;
  imapService.lastCheckTime = null;
}

// 清理旧的验证码缓存
async function clearOldVerificationCodes(email) {
  try {
    console.log('🧹 请求清理旧验证码缓存:', email);
    const response = await fetch(`${imapService.serverUrl}/clear-old-codes`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        email: email,
        timestamp: Date.now()
      })
    });

    if (response.ok) {
      const result = await response.json();
      console.log('✅ 旧验证码清理结果:', result);
    } else {
      console.log('⚠️ 旧验证码清理失败:', response.status, response.statusText);
    }
  } catch (error) {
    console.error('❌ 清理旧验证码时出错:', error);
  }
}

// 删除验证码邮件
async function deleteVerificationEmail(email) {
  if (!email || !imapService.serverUrl) {
    console.log('⚠️ 无法删除验证码邮件：缺少邮箱地址或服务器URL');
    return;
  }

  try {
    console.log('🗑️ 开始删除验证码邮件:', email);
    const response = await fetch(`${imapService.serverUrl}/verification-email/${encodeURIComponent(email)}`, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json'
      }
    });

    if (response.ok) {
      const result = await response.json();
      console.log('✅ 验证码邮件删除成功:', result);
      console.log(`🗑️ 已删除 ${result.deleted} 封验证码邮件`);
    } else {
      const errorResult = await response.json().catch(() => ({ error: '解析响应失败' }));
      console.log('⚠️ 验证码邮件删除失败:', errorResult.error || response.statusText);
    }
  } catch (error) {
    console.error('❌ 删除验证码邮件时出错:', error);
  }
}

// 重置人机验证状态（调试用）
function resetCaptchaState() {
  console.log('🔄 手动重置人机验证状态');
  window.captchaInProgress = false;
  window.captchaStep = 1;
  window.captchaStartTime = null;
  window.captchaPageActive = false;
  window.captchaRetryCount = 0;
  console.log('✅ 人机验证状态已重置');
}

// 将重置函数暴露到全局，方便调试
window.resetCaptchaState = resetCaptchaState;

// 全局错误处理器
window.addEventListener('error', (event) => {
  if (event.error && event.error.message && event.error.message.includes('Extension context invalidated')) {
    console.warn('🔄 捕获到扩展上下文失效错误，停止所有定时器');

    // 停止所有定时器
    if (msRegistrationState.detectionInterval) {
      clearInterval(msRegistrationState.detectionInterval);
      msRegistrationState.detectionInterval = null;
    }

    // 重置状态
    msRegistrationState.isProcessing = false;
    window.captchaInProgress = false;
    window.longPressInProgress = false;

    console.log('✅ 已清理所有状态，等待扩展重新加载');
    return true; // 阻止错误冒泡
  }
});

// 监听扩展上下文变化
if (typeof chrome !== 'undefined' && chrome.runtime) {
  chrome.runtime.onConnect.addListener(() => {
    console.log('🔄 扩展上下文重新连接');
  });
}

// 触发服务器检查邮件
async function triggerEmailCheck(email) {
  try {
    console.log('触发服务器邮件检查:', email);
    const response = await fetch(`${imapService.serverUrl}/check-email/${encodeURIComponent(email)}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      }
    });

    const result = await response.json();
    console.log('服务器邮件检查结果:', result);

    if (!result.success) {
      console.error('服务器邮件检查失败:', result.error);
      msRegistrationState.verificationStatus = '邮件检查失败';
      saveMicrosoftRegistrationState();
    }
  } catch (error) {
    console.error('触发邮件检查失败:', error);
    msRegistrationState.verificationStatus = '服务器连接失败';
    saveMicrosoftRegistrationState();
  }
}

// 从服务器获取验证码（增强版本）
async function checkForVerificationCode() {
  if (!imapService.currentEmail) {
    console.log('❌ 没有当前邮箱，跳过验证码检查');
    return;
  }

  // 检查是否超过最大重试次数
  if (imapService.retryCount >= imapService.maxRetries) {
    console.log(`❌ 已达到最大重试次数 (${imapService.maxRetries})，停止检查`);
    stopImapEmailCheck();
    msRegistrationState.verificationStatus = `验证码获取失败 (已重试${imapService.maxRetries}次)`;
    saveMicrosoftRegistrationState();
    return;
  }

  // 检查连续失败次数
  if (imapService.consecutiveFailures >= imapService.maxConsecutiveFailures) {
    console.log(`⚠️ 连续失败次数过多 (${imapService.consecutiveFailures})，延长检查间隔`);
    // 动态调整检查间隔
    imapService.checkIntervalTime = Math.min(imapService.checkIntervalTime * 1.5, 10000);
    console.log(`📈 调整检查间隔为: ${imapService.checkIntervalTime}ms`);
  }

  imapService.lastCheckTime = Date.now();
  const elapsedTime = imapService.lastCheckTime - imapService.startTime;

  try {
    console.log(`🔍 从服务器获取验证码 (第${imapService.retryCount + 1}次尝试):`, imapService.currentEmail);

    // 构建带时间戳的API URL，确保只获取最新邮件
    const apiUrl = new URL(`${imapService.serverUrl}/verification-code/${encodeURIComponent(imapService.currentEmail)}`);

    // 添加开始时间参数，服务器将只返回此时间之后的邮件中的验证码
    if (imapService.startTime) {
      apiUrl.searchParams.set('since', imapService.startTime.toString());
      console.log(`⏰ 设置邮件过滤时间: ${new Date(imapService.startTime).toISOString()}`);
    }

    // 添加当前时间戳，用于服务器端缓存控制
    apiUrl.searchParams.set('timestamp', Date.now().toString());

    console.log(`📡 请求URL: ${apiUrl.toString()}`);
    console.log(`⏱️ 已等待时间: ${Math.floor(elapsedTime / 1000)}秒`);

    // 设置请求超时
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 10000); // 10秒超时

    const response = await fetch(apiUrl.toString(), {
      signal: controller.signal,
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache'
      }
    });

    clearTimeout(timeoutId);
    console.log('📡 响应状态:', response.status);

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const result = await response.json();
    console.log('📧 API响应:', result);

    if (result.success && result.code) {
      // 验证验证码的时间戳，确保是最新的
      let isValidTimestamp = true;
      if (result.timestamp) {
        const codeTimestamp = new Date(result.timestamp).getTime();
        const timeDiff = codeTimestamp - imapService.startTime;

        console.log('⏰ 验证码时间戳验证:');
        console.log('📅 验证码时间:', new Date(result.timestamp).toISOString());
        console.log('📅 检查开始时间:', new Date(imapService.startTime).toISOString());
        console.log('⏱️ 时间差:', Math.floor(timeDiff / 1000), '秒');

        if (timeDiff < 0) {
          console.log('❌ 验证码时间早于检查开始时间，拒绝旧邮件');
          isValidTimestamp = false;
        } else if (timeDiff < 10000) { // 10秒内的验证码认为可能是旧的，更严格的过滤
          console.log('❌ 验证码时间过于接近开始时间，拒绝可能的旧邮件');
          isValidTimestamp = false;
        } else {
          console.log('✅ 验证码时间戳验证通过，确认为最新验证码');
        }
      } else {
        console.log('⚠️ 服务器未返回验证码时间戳，无法验证是否为最新');
      }

      if (isValidTimestamp) {
        console.log('🎉 ===== 成功获取到验证码 =====');
        console.log('📋 验证码内容:', result.code);
        console.log('📋 验证码长度:', result.code.length);
        console.log('📋 验证码类型:', typeof result.code);
        console.log(`📊 获取统计: 重试${imapService.retryCount}次, 耗时${Math.floor(elapsedTime / 1000)}秒`);
        if (result.timestamp) {
          console.log('📅 验证码邮件时间:', new Date(result.timestamp).toISOString());
        }
        console.log('🎉 ===========================');
      } else {
        console.log('❌ 验证码时间戳验证失败，跳过此验证码，继续等待最新验证码');
        // 增加重试计数但不停止检查
        imapService.retryCount++;
        imapService.consecutiveFailures++;
        return;
      }

      stopImapEmailCheck();
      msRegistrationState.verificationStatus = '验证码已收到: ' + result.code;
      saveMicrosoftRegistrationState();

      // 立即尝试填入验证码
      console.log('🔄 开始填入验证码...');
      const fillResult = fillVerificationCode(result.code);
      if (fillResult) {
        console.log('✅ 验证码填入成功');
        // 重置失败计数
        imapService.consecutiveFailures = 0;

        // 验证码填入成功后，等待一段时间再删除邮件（确保验证通过）
        setTimeout(() => {
          deleteVerificationEmail(msRegistrationState.currentAccount);
        }, 5000); // 等待5秒确保验证码被处理
      } else {
        console.log('❌ 验证码填入失败，请检查页面状态');
      }
    } else {
      console.log(`❌ 暂未收到验证码 (第${imapService.retryCount + 1}次):`, result.message || '无消息');
      msRegistrationState.verificationStatus = `等待验证码... (${Math.floor(elapsedTime / 1000)}秒)`;
      saveMicrosoftRegistrationState();

      // 增加重试计数
      imapService.retryCount++;

      // 如果是服务器返回的"未找到"，不算作连续失败
      if (result.message && !result.message.includes('未找到')) {
        imapService.consecutiveFailures++;
      }
    }
  } catch (error) {
    console.error(`❌ 获取验证码失败 (第${imapService.retryCount + 1}次):`, error);
    console.error('错误详情:', error.message);

    // 增加失败计数
    imapService.retryCount++;
    imapService.consecutiveFailures++;

    // 根据错误类型设置不同的状态消息
    let errorMessage = '获取验证码失败';
    if (error.name === 'AbortError') {
      errorMessage = '请求超时';
    } else if (error.message.includes('fetch')) {
      errorMessage = '网络连接失败';
    } else {
      errorMessage = error.message;
    }

    msRegistrationState.verificationStatus = `${errorMessage} (重试${imapService.retryCount}/${imapService.maxRetries})`;
    saveMicrosoftRegistrationState();

    // 如果连续失败次数过多，考虑重新触发邮件检查
    if (imapService.consecutiveFailures >= imapService.maxConsecutiveFailures && imapService.retryCount < imapService.maxRetries) {
      console.log('🔄 连续失败次数过多，重新触发服务器邮件检查...');
      triggerEmailCheck(imapService.currentEmail);
    }
  }
}

// 处理验证码页面
function handleVerificationCodePage() {
  console.log('🔍 检测到验证码页面');
  console.log('📧 当前账号:', msRegistrationState.currentAccount);
  console.log('📧 IMAP当前邮箱:', imapService.currentEmail);
  console.log('⏰ 是否正在等待验证码:', imapService.isWaitingForCode);

  // 检查页面上的验证码输入框
  console.log('🔍 检查验证码输入框状态...');
  let filledInputs = 0;
  let currentCode = '';

  for (let i = 0; i < 6; i++) {
    const input = document.querySelector(`#codeEntry-${i}`);
    console.log(`🔍 输入框 #codeEntry-${i}:`, input ? '存在' : '不存在');
    if (input) {
      const value = input.value.trim();
      console.log(`📋 输入框 ${i} 当前值:`, value);
      if (value) {
        filledInputs++;
        currentCode += value;
      }
    }
  }

  console.log(`📊 验证码填写状态: ${filledInputs}/6 个输入框已填写`);
  if (filledInputs === 6) {
    console.log(`✅ 验证码已完整填写: ${currentCode}`);
    console.log('🔍 检查是否需要提交...');

    // 检查提交按钮状态
    const submitButton = document.querySelector('button[type="submit"][data-testid="primaryButton"], button[data-testid="primaryButton"]');
    if (submitButton && !submitButton.disabled) {
      console.log('🎯 验证码已填写且提交按钮可用，尝试自动提交');
      setTimeout(() => {
        submitButton.click();
        console.log('✅ 已自动提交验证码');
        advanceToNextStage('verification_code');
        saveMicrosoftRegistrationState();
      }, 1000);
      return; // 重要：如果验证码已填写，直接返回，不再获取新验证码
    } else {
      console.log('⚠️ 验证码已填写但提交按钮不可用，可能需要手动提交');
      return; // 重要：如果验证码已填写，直接返回
    }
  }

  // 使用统一的状态管理函数设置验证码阶段
  if (msRegistrationState.currentStep !== 'verification_code') {
    setCurrentStage('verification_code');
  }
  msRegistrationState.verificationStatus = '等待邮件';
  saveMicrosoftRegistrationState();

  // 确保IMAP服务使用正确的邮箱
  if (msRegistrationState.currentAccount && imapService.currentEmail !== msRegistrationState.currentAccount) {
    console.log('🔄 IMAP邮箱不匹配，更新为当前账号');
    console.log('🔄 旧邮箱:', imapService.currentEmail);
    console.log('🔄 新邮箱:', msRegistrationState.currentAccount);

    // 停止旧的IMAP检查
    if (imapService.isWaitingForCode) {
      console.log('🛑 停止旧邮箱的IMAP检查');
      stopImapEmailCheck();
    }

    // 更新邮箱
    imapService.currentEmail = msRegistrationState.currentAccount;
  }

  // 启动IMAP邮件检查
  if (msRegistrationState.currentAccount && !imapService.isWaitingForCode) {
    console.log('🚀 启动IMAP邮件检查，邮箱:', msRegistrationState.currentAccount);
    startImapEmailCheck(msRegistrationState.currentAccount);
  } else {
    console.log('⏸️ 跳过IMAP邮件检查:', {
      hasAccount: !!msRegistrationState.currentAccount,
      isWaiting: imapService.isWaitingForCode,
      emailMatch: imapService.currentEmail === msRegistrationState.currentAccount
    });

    // 如果已经在等待验证码，但邮箱匹配，继续检查
    if (imapService.isWaitingForCode && imapService.currentEmail === msRegistrationState.currentAccount) {
      console.log('🔄 继续检查验证码...');
    }
  }
}

// 处理邮件验证页面（包含"验证你的电子邮件"等文本的页面）
function handleEmailVerificationPage() {
  console.log('🔍 检测到邮件验证页面');
  console.log('📧 当前账号:', msRegistrationState.currentAccount);
  console.log('📧 IMAP当前邮箱:', imapService.currentEmail);
  console.log('⏰ 是否正在等待验证码:', imapService.isWaitingForCode);

  // 使用统一的状态管理函数设置邮件验证阶段
  if (msRegistrationState.currentStep !== 'email_verification') {
    setCurrentStage('email_verification');
  }
  msRegistrationState.verificationStatus = '等待邮件验证';
  saveMicrosoftRegistrationState();

  // 确保IMAP服务使用正确的邮箱
  if (msRegistrationState.currentAccount && imapService.currentEmail !== msRegistrationState.currentAccount) {
    console.log('🔄 IMAP邮箱不匹配，更新为当前账号');
    console.log('🔄 旧邮箱:', imapService.currentEmail);
    console.log('🔄 新邮箱:', msRegistrationState.currentAccount);

    // 停止旧的IMAP检查
    if (imapService.isWaitingForCode) {
      console.log('🛑 停止旧邮箱的IMAP检查');
      stopImapEmailCheck();
    }

    // 更新邮箱
    imapService.currentEmail = msRegistrationState.currentAccount;
  }

  // 启动IMAP邮件检查
  if (msRegistrationState.currentAccount && !imapService.isWaitingForCode) {
    console.log('🚀 启动IMAP邮件检查，邮箱:', msRegistrationState.currentAccount);
    startImapEmailCheck(msRegistrationState.currentAccount);

    // 邮件验证阶段启动IMAP检查后，等待一段时间再推进阶段
    // 这样可以确保IMAP服务正常启动，然后等待验证码页面出现
    setTimeout(() => {
      if (msRegistrationState.currentStep === 'email_verification' &&
          !msRegistrationState.stageCompleted['email_verification']) {
        console.log('✅ 邮件验证阶段IMAP服务启动完成');
        advanceToNextStage('email_verification');
      }
    }, 3000);
  } else {
    console.log('⏸️ 跳过IMAP邮件检查:', {
      hasAccount: !!msRegistrationState.currentAccount,
      isWaiting: imapService.isWaitingForCode,
      emailMatch: imapService.currentEmail === msRegistrationState.currentAccount
    });

    // 如果已经在等待验证码，但邮箱匹配，继续检查
    if (imapService.isWaitingForCode && imapService.currentEmail === msRegistrationState.currentAccount) {
      console.log('🔄 继续检查验证码...');
    }

    // 如果IMAP已经在运行，也推进阶段
    if (imapService.isWaitingForCode && msRegistrationState.currentStep === 'email_verification' &&
        !msRegistrationState.stageCompleted['email_verification']) {
      console.log('✅ IMAP服务已在运行，推进邮件验证阶段');
      advanceToNextStage('email_verification');
    }
  }
}

// 填写6位验证码
function fillVerificationCode(code) {
  console.log('🔄 开始填写验证码函数');
  console.log('📋 接收到的验证码:', code);
  console.log('📋 验证码长度:', code ? code.length : 'undefined');
  console.log('📋 验证码类型:', typeof code);

  if (!code) {
    console.error('❌ 验证码为空或未定义');
    return false;
  }

  if (code.length !== 6) {
    console.error('❌ 验证码长度不正确:', code, '长度:', code.length);
    return false;
  }

  console.log('🔍 开始查找验证码输入框...');
  let successCount = 0;

  for (let i = 0; i < 6; i++) {
    const input = document.querySelector(`#codeEntry-${i}`);
    console.log(`🔍 查找输入框 #codeEntry-${i}:`, input ? '找到' : '未找到');

    if (input) {
      input.value = code[i];
      input.dispatchEvent(new Event('input', { bubbles: true }));
      input.dispatchEvent(new Event('change', { bubbles: true }));
      console.log(`✅ 填写位置 ${i}: ${code[i]}`);
      successCount++;
    } else {
      console.log(`❌ 未找到输入框 #codeEntry-${i}`);
    }
  }

  console.log(`📊 填写结果: ${successCount}/6 个输入框填写成功`);
  console.log('✅ 验证码填写完成:', code);

  if (successCount === 6) {
    // 自动提交验证码
    setTimeout(() => {
      console.log('🔍 查找提交按钮...');

      // 多种选择器尝试查找提交按钮
      const selectors = [
        'button[type="submit"][data-testid="primaryButton"]',
        'button[data-testid="primaryButton"]',
        'button[type="submit"]',
        'input[type="submit"]',
        'button:contains("下一步")',
        'button:contains("提交")',
        'button:contains("继续")',
        'button:contains("Next")',
        'button:contains("Submit")',
        'button:contains("Continue")',
        '[role="button"][data-testid="primaryButton"]',
        '.primaryButton',
        '#primaryButton'
      ];

      let nextButton = null;
      let usedSelector = '';

      // 尝试每个选择器
      for (const selector of selectors) {
        try {
          if (selector.includes(':contains(')) {
            // 对于包含文本的选择器，手动查找
            const buttonType = selector.split(':contains(')[0];
            const text = selector.match(/\("([^"]+)"\)/)?.[1];
            if (text) {
              const buttons = document.querySelectorAll(buttonType);
              for (const btn of buttons) {
                if (btn.textContent.trim().includes(text)) {
                  nextButton = btn;
                  usedSelector = selector;
                  break;
                }
              }
            }
          } else {
            nextButton = document.querySelector(selector);
            if (nextButton) {
              usedSelector = selector;
              break;
            }
          }
        } catch (e) {
          console.log(`⚠️ 选择器 "${selector}" 查找失败:`, e.message);
        }
      }

      console.log('🔍 提交按钮:', nextButton ? '找到' : '未找到');
      if (nextButton) {
        console.log('📋 使用的选择器:', usedSelector);
        console.log('📋 按钮文本:', nextButton.textContent.trim());
        console.log('📋 按钮类型:', nextButton.type);
        console.log('📋 按钮是否禁用:', nextButton.disabled);
      }

      if (nextButton && !nextButton.disabled) {
        nextButton.click();
        console.log('✅ 已点击提交按钮');
        // 使用阶段化状态机进入下一阶段
        advanceToNextStage('verification_code');
        saveMicrosoftRegistrationState();

        // 验证码提交成功后清除验证码相关信息
        setTimeout(() => {
          clearVerificationCodeData();
        }, 2000);

      } else if (nextButton && nextButton.disabled) {
        console.log('⚠️ 提交按钮被禁用，可能验证码不正确');
      } else {
        console.log('❌ 未找到提交按钮');
        console.log('📋 页面上的所有按钮:');
        const allButtons = document.querySelectorAll('button, input[type="submit"], [role="button"]');
        allButtons.forEach((btn, index) => {
          console.log(`按钮 ${index}:`, {
            tagName: btn.tagName,
            type: btn.type,
            textContent: btn.textContent.trim().substring(0, 30),
            className: btn.className,
            id: btn.id,
            disabled: btn.disabled,
            'data-testid': btn.getAttribute('data-testid')
          });
        });
        console.log('❌ 请手动提交验证码');
      }
    }, 1000);
    return true;
  } else {
    console.log('❌ 验证码填写不完整，请检查页面状态');
    return false;
  }
}

// 处理个人信息页面（生日信息）
function handlePersonalInfoPage() {
  console.log('检测到个人信息页面');
  // 使用统一的状态管理函数设置个人信息阶段
  if (msRegistrationState.currentStep !== 'personal_info') {
    setCurrentStage('personal_info');
  }

  // 生成随机生日信息
  const randomYear = getRandomYear();
  const randomMonth = getRandomMonth();
  const randomDay = getRandomDay();

  // 填写年份
  const yearInput = document.querySelector('input[name="BirthYear"]');
  if (yearInput) {
    yearInput.value = randomYear.toString();
    yearInput.dispatchEvent(new Event('input', { bubbles: true }));
    yearInput.dispatchEvent(new Event('change', { bubbles: true }));
    console.log('已填写年份:', randomYear);
  }

  // 改进的月份和日期选择逻辑
  setTimeout(() => {
    console.log('🔍 开始检查页面上的生日选择元素...');

    // 检查月份下拉框
    const monthDropdown = document.querySelector('button[name="BirthMonth"]');
    console.log('🔍 月份下拉框:', monthDropdown ? '找到' : '未找到');
    if (monthDropdown) {
      console.log('📋 月份下拉框文本:', monthDropdown.textContent.trim());
    }

    // 检查日期下拉框
    const dayDropdown = document.querySelector('button[name="BirthDay"]');
    console.log('🔍 日期下拉框:', dayDropdown ? '找到' : '未找到');
    if (dayDropdown) {
      console.log('📋 日期下拉框文本:', dayDropdown.textContent.trim());
    }

    // 如果找不到，尝试其他可能的选择器
    if (!monthDropdown || !dayDropdown) {
      console.log('🔍 尝试查找其他可能的生日选择元素...');
      const allButtons = document.querySelectorAll('button');
      const allSelects = document.querySelectorAll('select');
      console.log('📋 页面上的按钮数量:', allButtons.length);
      console.log('📋 页面上的选择框数量:', allSelects.length);

      // 输出前10个按钮的信息
      Array.from(allButtons).slice(0, 10).forEach((btn, index) => {
        console.log(`📋 按钮 ${index}:`, {
          text: btn.textContent.trim(),
          name: btn.getAttribute('name'),
          id: btn.id,
          className: btn.className
        });
      });
    }

    selectBirthDateSequentially(randomMonth, randomDay);
  }, 1000);

  // 点击下一步
  setTimeout(() => {
    console.log('🔍 开始查找个人信息页面的下一步按钮...');

    // 尝试多种可能的下一步按钮选择器
    const buttonSelectors = [
      'button[type="submit"][data-testid="primaryButton"]',
      'button[data-testid="primaryButton"]',
      'button[type="submit"]',
      'button:contains("下一个")',
      'button:contains("下一步")',
      'button:contains("Next")',
      'input[type="submit"]'
    ];

    let nextButton = null;
    let usedSelector = '';

    for (const selector of buttonSelectors) {
      if (selector.includes(':contains')) {
        // 对于包含文本的选择器，手动查找
        const buttons = document.querySelectorAll('button');
        for (const btn of buttons) {
          const text = btn.textContent.trim();
          if (text.includes('下一个') || text.includes('下一步') || text.includes('Next')) {
            nextButton = btn;
            usedSelector = `button with text "${text}"`;
            break;
          }
        }
      } else {
        nextButton = document.querySelector(selector);
        if (nextButton) {
          usedSelector = selector;
          break;
        }
      }
    }

    console.log('🔍 下一步按钮查找结果:', nextButton ? '找到' : '未找到');
    if (nextButton) {
      console.log('📋 使用的选择器:', usedSelector);
      console.log('📋 按钮文本:', nextButton.textContent.trim());
      console.log('📋 按钮是否禁用:', nextButton.disabled);

      if (!nextButton.disabled) {
        nextButton.click();
        console.log('✅ 已点击个人信息页面的下一步按钮');

        // 等待页面跳转后再推进阶段
        setTimeout(() => {
          // 检查是否已经跳转到下一个页面
          if (!document.querySelector('button[name="BirthMonth"]') &&
              !document.querySelector('input[name="BirthYear"]')) {
            console.log('✅ 个人信息页面已跳转，推进到下一阶段');
            advanceToNextStage('personal_info');
          } else {
            console.log('⚠️ 页面未跳转，可能填写不完整');
          }
        }, 2000);
      } else {
        console.log('⚠️ 下一步按钮被禁用，可能需要完成所有字段填写');
      }
    } else {
      console.log('❌ 未找到下一步按钮，输出页面调试信息');
      // 输出调试信息
      const allButtons = document.querySelectorAll('button');
      console.log('📋 页面上所有按钮:', Array.from(allButtons).map(btn => ({
        text: btn.textContent.trim(),
        disabled: btn.disabled,
        type: btn.type,
        testId: btn.getAttribute('data-testid'),
        className: btn.className
      })));
    }
  }, 4000); // 增加等待时间，确保月份和日期选择完成
}

// 处理姓名页面
function handleNamePage() {
  console.log('检测到姓名页面');
  // 使用统一的状态管理函数设置姓名阶段
  if (msRegistrationState.currentStep !== 'name') {
    setCurrentStage('name');
  }

  // 生成随机姓名
  const lastName = generateRandomLastName();
  const firstName = generateRandomFirstName();

  // 填写姓氏
  const lastNameInput = document.querySelector('#lastNameInput');
  if (lastNameInput) {
    lastNameInput.value = lastName;
    lastNameInput.dispatchEvent(new Event('input', { bubbles: true }));
    lastNameInput.dispatchEvent(new Event('change', { bubbles: true }));
    console.log('已填写姓氏:', lastName);
  }

  // 填写名字
  const firstNameInput = document.querySelector('#firstNameInput');
  if (firstNameInput) {
    firstNameInput.value = firstName;
    firstNameInput.dispatchEvent(new Event('input', { bubbles: true }));
    firstNameInput.dispatchEvent(new Event('change', { bubbles: true }));
    console.log('已填写名字:', firstName);
  }

  // 点击下一步
  setTimeout(() => {
    const nextButton = document.querySelector('button[type="submit"][data-testid="primaryButton"]');
    if (nextButton && nextButton.textContent.includes('下一个')) {
      nextButton.click();
      console.log('✅ 已点击姓名页面的下一步按钮');

      // 等待页面跳转后再推进阶段
      setTimeout(() => {
        // 检查是否已经跳转到下一个页面
        if (!document.querySelector('#lastNameInput') &&
            !document.querySelector('#firstNameInput')) {
          console.log('✅ 姓名页面已跳转，推进到下一阶段');
          advanceToNextStage('name');
        } else {
          console.log('⚠️ 页面未跳转，可能填写不完整');
        }
      }, 2000);
    } else {
      console.log('❌ 未找到下一步按钮，请检查页面状态');
      // 输出调试信息
      const allButtons = document.querySelectorAll('button');
      console.log('页面上所有按钮:', Array.from(allButtons).map(btn => ({
        text: btn.textContent.trim(),
        disabled: btn.disabled,
        type: btn.type,
        testId: btn.getAttribute('data-testid')
      })));
    }
  }, 1500);
}

// 处理人机验证页面
function handleCaptchaPage() {
  console.log('🔍 检测到人机验证页面');
  setCurrentStage('captcha');

  // 检查是否有卡住的状态，如果超过30秒则重置
  if (window.captchaInProgress) {
    const now = Date.now();
    if (!window.captchaStartTime) {
      window.captchaStartTime = now;
    }

    const elapsedTime = now - window.captchaStartTime;
    if (elapsedTime > 30000) { // 30秒超时
      console.log('⚠️ 人机验证超时，重置状态');
      window.captchaInProgress = false;
      window.captchaStep = 1;
      window.captchaStartTime = null;
    } else {
      console.log(`⚠️ 人机验证已在进行中，已进行${Math.floor(elapsedTime/1000)}秒`);
      return;
    }
  }

  // 检查是否有"再次尝试"的提示
  const retryText = document.querySelector('#LKlphTDXuogbAXR');
  if (retryText && retryText.textContent.includes('请再试一次')) {
    console.log('🔄 检测到"请再试一次"提示，重新开始验证流程');
    msRegistrationState.attempts++;

    if (msRegistrationState.attempts < msRegistrationState.maxAttempts) {
      // 重置状态并重新开始
      window.captchaInProgress = false;
      window.captchaStep = 1;
      setTimeout(() => handleCaptchaPage(), 2000);
      return;
    } else {
      console.error('❌ 人机验证失败次数过多，停止重试');
      return;
    }
  }

  // 开始人机验证流程
  window.captchaInProgress = true;
  window.captchaStartTime = Date.now();

  // 初始化验证步骤
  if (!window.captchaStep) {
    window.captchaStep = 1;
  }

  console.log(`🎯 开始人机验证流程 - 步骤 ${window.captchaStep}`);
  console.log('🔍 当前页面元素检查:');
  console.log('  - 第一个按钮 #GMGNsNHVCsQWOSo:', !!document.querySelector('#GMGNsNHVCsQWOSo'));
  console.log('  - 第二个按钮 #TmLzkpoGUMIggiM:', !!document.querySelector('#TmLzkpoGUMIggiM'));
  console.log('  - 重试提示 #LKlphTDXuogbAXR:', !!document.querySelector('#LKlphTDXuogbAXR'));

  if (window.captchaStep === 1) {
    // 第一步：点击 #GMGNsNHVCsQWOSo
    const firstButton = document.querySelector('#GMGNsNHVCsQWOSo');
    if (firstButton) {
      console.log('✅ 找到第一个验证按钮 #GMGNsNHVCsQWOSo');
      console.log('🖱️ 点击第一个验证按钮...');

      try {
        firstButton.click();
        console.log('✅ 第一个按钮点击成功');

        // 等待15秒后进行第二步
        window.captchaStep = 2;
        console.log('⏰ 等待15秒后进行第二步...');
        setTimeout(() => {
          handleCaptchaPage();
        }, 15000);

      } catch (error) {
        console.error('❌ 点击第一个按钮失败:', error);
        window.captchaInProgress = false;
        window.captchaStep = 1;
        setTimeout(() => handleCaptchaPage(), 2000);
      }
    } else {
      console.log('❌ 未找到第一个验证按钮 #GMGNsNHVCsQWOSo');
      // 尝试查找其他可能的按钮
      handleFallbackCaptchaDetection();
    }
  } else if (window.captchaStep === 2) {
    // 第二步：点击 #TmLzkpoGUMIggiM
    const secondButton = document.querySelector('#TmLzkpoGUMIggiM');
    if (secondButton) {
      console.log('✅ 找到第二个验证按钮 #TmLzkpoGUMIggiM');
      console.log('🖱️ 点击第二个验证按钮...');

      try {
        secondButton.click();
        console.log('✅ 第二个按钮点击成功');

        // 验证完成，检查结果
        window.captchaStep = 3;
        setTimeout(() => {
          checkCaptchaVerificationResult();
        }, 3000);

      } catch (error) {
        console.error('❌ 点击第二个按钮失败:', error);
        window.captchaInProgress = false;
        window.captchaStep = 1;
        setTimeout(() => handleCaptchaPage(), 2000);
      }
    } else {
      console.log('❌ 未找到第二个验证按钮 #TmLzkpoGUMIggiM');
      // 可能还需要等待，继续检查
      setTimeout(() => {
        handleCaptchaPage();
      }, 2000);
    }
  }
}

// 备用的人机验证检测方法
function handleFallbackCaptchaDetection() {
  console.log('🔄 使用备用方法检测人机验证按钮...');

  // 检查是否显示"按住"或"长按"文本
  let foundHoldText = false;

  // 方法1: 检查特定ID的元素
  const holdTextById = document.querySelector('#GlAInHXoXszrxIm');
  if (holdTextById && (holdTextById.textContent.includes('按住') || holdTextById.textContent.includes('长按'))) {
    foundHoldText = true;
    console.log('通过ID找到"按住"或"长按"文本');
  }

  // 方法2: 检查所有span标签
  if (!foundHoldText) {
    const allSpans = document.querySelectorAll('span');
    for (let span of allSpans) {
      if (span.textContent.includes('按住') || span.textContent.includes('长按')) {
        foundHoldText = true;
        console.log('通过span标签找到"按住"或"长按"文本');
        break;
      }
    }
  }

  if (foundHoldText) {
    console.log('✅ 找到长按文本，使用传统长按方式');

    // 查找iframe中的验证按钮
    const iframes = document.querySelectorAll('iframe[data-testid="humanCaptchaIframe"], iframe[title*="验证"], iframe[src*="hsprotect"]');
    if (iframes.length > 0) {
      console.log('找到人机验证iframe，尝试与iframe交互');
      const iframeContainer = iframes[0].parentElement;
      if (iframeContainer) {
        console.log('使用iframe容器作为验证按钮');
        simulateLongPress(iframeContainer);
        return;
      }
    }

    // 如果没有找到iframe，尝试查找其他按钮
    const allButtons = document.querySelectorAll('button, div[role="button"], [tabindex="0"]');
    for (let btn of allButtons) {
      if (btn.textContent.includes('按住') || btn.textContent.includes('长按')) {
        console.log('找到传统验证按钮，开始长按操作');
        simulateLongPress(btn);
        return;
      }
    }
  }

  // 如果都没找到，重置状态并重试
  console.log('❌ 备用检测也未找到验证按钮');
  window.captchaInProgress = false;
  window.captchaStep = 1;

  // 添加防止无限循环的机制
  if (!window.captchaRetryCount) {
    window.captchaRetryCount = 0;
  }
  window.captchaRetryCount++;

  if (window.captchaRetryCount < 5) {
    setTimeout(() => handleCaptchaPage(), 3000);
  } else {
    console.error('❌ 人机验证页面检测重试次数过多，停止重试');
    window.captchaRetryCount = 0;
  }
}

// 处理可访问性挑战按钮（第一步）
function handleAccessibilityChallenge(button) {
  console.log('🎯 开始处理可访问性挑战按钮');
  console.log('按钮信息:', {
    tagName: button.tagName,
    id: button.id,
    className: button.className,
    ariaLabel: button.getAttribute('aria-label'),
    textContent: button.textContent.trim()
  });

  // 防止重复执行
  if (window.accessibilityChallengeInProgress) {
    console.log('⚠️ 可访问性挑战已在进行中，跳过重复执行');
    return;
  }

  window.accessibilityChallengeInProgress = true;

  try {
    // 确保元素可见和可交互
    button.scrollIntoView({ behavior: 'smooth', block: 'center' });

    // 等待滚动完成后点击
    setTimeout(() => {
      try {
        // 添加焦点
        button.focus();

        // 点击按钮
        button.click();
        console.log('✅ 已点击可访问性挑战按钮');

        // 等待页面响应，然后检查是否出现"再次按下"按钮
        setTimeout(() => {
          checkForPressAgainButton();
        }, 2000);

      } catch (error) {
        console.error('❌ 点击可访问性挑战按钮失败:', error);
        window.accessibilityChallengeInProgress = false;
        // 重试
        setTimeout(() => handleCaptchaPage(), 2000);
      }
    }, 500);

  } catch (error) {
    console.error('❌ 可访问性挑战处理失败:', error);
    window.accessibilityChallengeInProgress = false;
  }
}

// 检查是否出现"再次按下"按钮
function checkForPressAgainButton() {
  console.log('🔍 检查是否出现"再次按下"按钮...');

  // 使用多种方法查找"再次按下"按钮
  function findPressAgainButton() {
    // 方法1: 通过aria-label查找
    let button = document.querySelector('[role="button"][aria-label*="再次按下"]');
    if (button) return button;

    // 方法2: 通过ID和类名查找
    button = document.querySelector('#iJONTsjCTbadsDp, .CCovUDZBqqyRyvP');
    if (button) return button;

    // 方法3: 通过文本内容查找
    const allButtons = document.querySelectorAll('[role="button"], button, div[tabindex="0"]');
    for (let btn of allButtons) {
      if (btn.textContent.includes('再次按下')) {
        return btn;
      }
    }

    return null;
  }

  const pressAgainButton = findPressAgainButton();
  if (pressAgainButton) {
    console.log('✅ 找到"再次按下"按钮');
    window.accessibilityChallengeInProgress = false;
    handlePressAgainChallenge(pressAgainButton);
  } else {
    console.log('⚠️ 未找到"再次按下"按钮，继续等待...');
    // 继续检查，最多等待10秒
    const maxWaitTime = 10000;
    const checkInterval = 1000;
    let waitTime = 0;

    const checkInterval_id = setInterval(() => {
      waitTime += checkInterval;
      const button = findPressAgainButton();

      if (button) {
        clearInterval(checkInterval_id);
        console.log('✅ 延迟找到"再次按下"按钮');
        window.accessibilityChallengeInProgress = false;
        handlePressAgainChallenge(button);
      } else if (waitTime >= maxWaitTime) {
        clearInterval(checkInterval_id);
        console.log('⚠️ 等待"再次按下"按钮超时，重新开始验证流程');
        window.accessibilityChallengeInProgress = false;
        setTimeout(() => handleCaptchaPage(), 1000);
      }
    }, checkInterval);
  }
}

// 处理"再次按下"挑战按钮（第二步）
function handlePressAgainChallenge(button) {
  console.log('🎯 开始处理"再次按下"挑战按钮');
  console.log('按钮信息:', {
    tagName: button.tagName,
    id: button.id,
    className: button.className,
    ariaLabel: button.getAttribute('aria-label'),
    textContent: button.textContent.trim()
  });

  // 防止重复执行
  if (window.pressAgainChallengeInProgress) {
    console.log('⚠️ "再次按下"挑战已在进行中，跳过重复执行');
    return;
  }

  window.pressAgainChallengeInProgress = true;

  try {
    // 确保元素可见和可交互
    button.scrollIntoView({ behavior: 'smooth', block: 'center' });

    // 等待滚动完成后点击
    setTimeout(() => {
      try {
        // 添加焦点
        button.focus();

        // 点击按钮
        button.click();
        console.log('✅ 已点击"再次按下"按钮');

        // 等待4秒后检查验证结果
        setTimeout(() => {
          checkCaptchaVerificationResult();
        }, 4000);

      } catch (error) {
        console.error('❌ 点击"再次按下"按钮失败:', error);
        window.pressAgainChallengeInProgress = false;
        // 重试
        setTimeout(() => handleCaptchaPage(), 2000);
      }
    }, 500);

  } catch (error) {
    console.error('❌ "再次按下"挑战处理失败:', error);
    window.pressAgainChallengeInProgress = false;
  }
}

// 检查人机验证结果
function checkCaptchaVerificationResult() {
  console.log('🔍 检查人机验证结果...');

  // 检查是否验证成功（跳转到登录完成页面）
  if (isLoginCompletePage()) {
    console.log('🎉 人机验证成功！跳转到登录完成页面');
    // 重置所有验证状态
    window.captchaInProgress = false;
    window.captchaStep = 1;
    window.pressAgainChallengeInProgress = false;
    window.longPressInProgress = false;
    window.captchaRetryCount = 0;

    advanceToNextStage('captcha');
    handleLoginCompletePage();
    return;
  }

  // 检查是否有错误提示
  const errorElement = document.querySelector('#LKlphTDXuogbAXR, [class*="error"], .error-message');
  if (errorElement && errorElement.textContent.includes('请再试一次')) {
    console.log('❌ 验证失败，需要重试');
    // 重置状态并重新开始
    window.captchaInProgress = false;
    window.captchaStep = 1;
    window.pressAgainChallengeInProgress = false;
    window.longPressInProgress = false;

    // 触发重试逻辑
    setTimeout(() => {
      handleCaptchaPage();
    }, 2000);
    return;
  }

  // 检查页面是否有变化（可能验证成功但页面还在加载）
  const currentUrl = window.location.href;
  console.log('当前页面URL:', currentUrl);

  // 如果页面没有明显变化，继续等待一段时间
  setTimeout(() => {
    if (isLoginCompletePage()) {
      console.log('🎉 延迟检测到验证成功！');
      // 重置所有验证状态
      window.captchaInProgress = false;
      window.captchaStep = 1;
      window.pressAgainChallengeInProgress = false;
      window.longPressInProgress = false;
      window.captchaRetryCount = 0;

      advanceToNextStage('captcha');
      handleLoginCompletePage();
    } else {
      console.log('⚠️ 验证结果不明确，重新开始验证流程');
      // 重置状态并重试
      window.captchaInProgress = false;
      window.captchaStep = 1;
      window.pressAgainChallengeInProgress = false;
      window.longPressInProgress = false;

      setTimeout(() => handleCaptchaPage(), 1000);
    }
  }, 3000);
}

// 模拟长按操作 - 增强版
function simulateLongPress(element) {
  console.log('开始模拟长按操作');

  // 防止重复执行
  if (window.longPressInProgress) {
    console.log('⚠️ 长按操作已在进行中，跳过重复执行');
    return;
  }

  window.longPressInProgress = true;

  try {
    // 确保元素可见和可交互
    element.scrollIntoView({ behavior: 'smooth', block: 'center' });

    // 等待滚动完成
    setTimeout(() => {
      try {
        // 添加焦点
        element.focus();

        // 获取元素位置信息
        const rect = element.getBoundingClientRect();
        const centerX = rect.left + rect.width / 2;
        const centerY = rect.top + rect.height / 2;

        console.log('元素位置信息:', {
          x: centerX,
          y: centerY,
          width: rect.width,
          height: rect.height
        });

        // 触发多种事件以确保兼容性
        const events = [];

        // 鼠标事件
        try {
          events.push(new MouseEvent('mousedown', {
            bubbles: true,
            cancelable: true,
            view: window,
            button: 0,
            buttons: 1,
            clientX: centerX,
            clientY: centerY,
            screenX: centerX + window.screenX,
            screenY: centerY + window.screenY
          }));
        } catch (e) {
          console.log('创建MouseEvent失败:', e.message);
        }

        // 指针事件
        try {
          events.push(new PointerEvent('pointerdown', {
            bubbles: true,
            cancelable: true,
            view: window,
            pointerId: 1,
            button: 0,
            buttons: 1,
            clientX: centerX,
            clientY: centerY,
            screenX: centerX + window.screenX,
            screenY: centerY + window.screenY,
            pointerType: 'mouse'
          }));
        } catch (e) {
          console.log('创建PointerEvent失败:', e.message);
        }

        // 触摸事件 - 使用兼容性更好的方式
        try {
          // 检查是否支持Touch构造函数
          if (typeof Touch !== 'undefined') {
            const touch = new Touch({
              identifier: 1,
              target: element,
              clientX: centerX,
              clientY: centerY,
              screenX: centerX + window.screenX,
              screenY: centerY + window.screenY
            });

            events.push(new TouchEvent('touchstart', {
              bubbles: true,
              cancelable: true,
              view: window,
              touches: [touch],
              targetTouches: [touch],
              changedTouches: [touch]
            }));
          } else {
            // 备用方案：创建简化的TouchEvent
            const touchEvent = new TouchEvent('touchstart', {
              bubbles: true,
              cancelable: true,
              view: window
            });
            events.push(touchEvent);
          }
        } catch (e) {
          console.log('创建TouchEvent失败:', e.message);
          // 如果TouchEvent也不支持，跳过触摸事件
        }

        // 自定义事件
        try {
          events.push(new CustomEvent('longpressstart', {
            bubbles: true,
            cancelable: true,
            detail: { duration: 3000 }
          }));
        } catch (e) {
          console.log('创建CustomEvent失败:', e.message);
        }

        // 触发开始事件
        let successfulEvents = 0;
        events.forEach((event, index) => {
          try {
            const result = element.dispatchEvent(event);
            console.log(`事件 ${index} (${event.type}) 触发${result ? '成功' : '被阻止'}`);
            if (result) successfulEvents++;
          } catch (e) {
            console.log(`事件 ${index} (${event.type}) 触发失败:`, e.message);
          }
        });

        console.log(`长按开始，成功触发 ${successfulEvents}/${events.length} 个事件，保持3秒...`);

        // 在长按期间定期触发持续事件
        const holdInterval = setInterval(() => {
          try {
            // 触发持续按住事件
            const holdEvent = new CustomEvent('longpresshold', {
              bubbles: true,
              cancelable: true,
              detail: { timestamp: Date.now() }
            });
            element.dispatchEvent(holdEvent);
          } catch (e) {
            console.log('持续事件触发失败:', e.message);
          }
        }, 500);

        // 保持按住状态3秒
        setTimeout(() => {
          clearInterval(holdInterval);
          console.log('准备释放长按');

          // 触发结束事件
          const endRect = element.getBoundingClientRect();
          const endCenterX = endRect.left + endRect.width / 2;
          const endCenterY = endRect.top + endRect.height / 2;

          const endEvents = [];

          // 鼠标释放事件
          try {
            endEvents.push(new MouseEvent('mouseup', {
              bubbles: true,
              cancelable: true,
              view: window,
              button: 0,
              buttons: 0,
              clientX: endCenterX,
              clientY: endCenterY,
              screenX: endCenterX + window.screenX,
              screenY: endCenterY + window.screenY
            }));
          } catch (e) {
            console.log('创建MouseEvent mouseup失败:', e.message);
          }

          // 指针释放事件
          try {
            endEvents.push(new PointerEvent('pointerup', {
              bubbles: true,
              cancelable: true,
              view: window,
              pointerId: 1,
              button: 0,
              buttons: 0,
              clientX: endCenterX,
              clientY: endCenterY,
              screenX: endCenterX + window.screenX,
              screenY: endCenterY + window.screenY,
              pointerType: 'mouse'
            }));
          } catch (e) {
            console.log('创建PointerEvent pointerup失败:', e.message);
          }

          // 触摸结束事件 - 使用兼容性更好的方式
          try {
            // 检查是否支持Touch构造函数
            if (typeof Touch !== 'undefined') {
              const endTouch = new Touch({
                identifier: 1,
                target: element,
                clientX: endCenterX,
                clientY: endCenterY,
                screenX: endCenterX + window.screenX,
                screenY: endCenterY + window.screenY
              });

              endEvents.push(new TouchEvent('touchend', {
                bubbles: true,
                cancelable: true,
                view: window,
                touches: [],
                targetTouches: [],
                changedTouches: [endTouch]
              }));
            } else {
              // 备用方案：创建简化的TouchEvent
              const touchEndEvent = new TouchEvent('touchend', {
                bubbles: true,
                cancelable: true,
                view: window
              });
              endEvents.push(touchEndEvent);
            }
          } catch (e) {
            console.log('创建TouchEvent touchend失败:', e.message);
            // 如果TouchEvent也不支持，跳过触摸事件
          }

          // 点击事件
          try {
            endEvents.push(new MouseEvent('click', {
              bubbles: true,
              cancelable: true,
              view: window,
              button: 0,
              buttons: 0,
              clientX: endCenterX,
              clientY: endCenterY,
              screenX: endCenterX + window.screenX,
              screenY: endCenterY + window.screenY
            }));
          } catch (e) {
            console.log('创建MouseEvent click失败:', e.message);
          }

          // 自定义长按结束事件
          try {
            endEvents.push(new CustomEvent('longpressend', {
              bubbles: true,
              cancelable: true,
              detail: { duration: 3000 }
            }));
          } catch (e) {
            console.log('创建CustomEvent longpressend失败:', e.message);
          }

          // 触发结束事件
          let successfulEndEvents = 0;
          endEvents.forEach((event, index) => {
            try {
              const result = element.dispatchEvent(event);
              console.log(`结束事件 ${index} (${event.type}) 触发${result ? '成功' : '被阻止'}`);
              if (result) successfulEndEvents++;
            } catch (e) {
              console.log(`结束事件 ${index} (${event.type}) 触发失败:`, e.message);
            }
          });

          console.log(`完成长按操作，成功触发 ${successfulEndEvents}/${endEvents.length} 个结束事件`);
          window.longPressInProgress = false;

          // 检查是否验证成功 - 多次检查以确保准确性
          let checkCount = 0;
          const maxChecks = 5;

          function checkVerificationResult() {
            checkCount++;
            console.log(`第 ${checkCount}/${maxChecks} 次检查验证结果...`);

            if (isLoginCompletePage()) {
              console.log('🎉 人机验证成功！');
              advanceToNextStage('captcha');
              handleLoginCompletePage();
              return;
            }

            // 检查是否有错误提示
            const errorElement = document.querySelector('#LKlphTDXuogbAXR, [class*="error"], .error-message');
            if (errorElement && errorElement.textContent.includes('请再试一次')) {
              console.log('❌ 验证失败，需要重试');
              window.longPressInProgress = false;
              // 触发重试逻辑
              setTimeout(() => {
                if (typeof handleCaptchaPage === 'function') {
                  handleCaptchaPage();
                }
              }, 2000);
              return;
            }

            if (checkCount < maxChecks) {
              setTimeout(checkVerificationResult, 2000);
            } else {
              console.log('⚠️ 验证结果检查超时，可能需要手动处理');
              window.longPressInProgress = false;
            }
          }

          // 开始检查验证结果
          setTimeout(checkVerificationResult, 1000);
        }, 3000); // 保持3秒长按
      } catch (error) {
        console.error('长按操作出错:', error);
        window.longPressInProgress = false;
      }
    }, 500); // 等待滚动完成
  } catch (error) {
    console.error('长按操作初始化失败:', error);
    window.longPressInProgress = false;
  }
}

// 处理登录完成页面
function handleLoginCompletePage() {
  console.log('检测到登录完成页面');
  setCurrentStage('login_complete');

  // 点击"暂时跳过"按钮
  const skipButton = document.querySelector('button[data-testid="secondaryButton"]');
  if (skipButton && skipButton.textContent.includes('暂时跳过')) {
    skipButton.click();
    console.log('已点击暂时跳过');
    setCurrentStage('rewards_welcome');
  }
}

// 处理Microsoft Rewards欢迎页面
function handleRewardsWelcomePage() {
  console.log('检测到Microsoft Rewards欢迎页面');
  setCurrentStage('rewards_welcome');

  // 点击接受按钮
  const acceptButton = document.querySelector('button._1XuCi2WhiqeWRUVp3pnFG3.erL690_8JwUW-R4bJRcfl');
  if (acceptButton && acceptButton.textContent.includes('接受')) {
    acceptButton.click();
    console.log('已点击接受按钮');
  }

  // 点击关闭按钮
  setTimeout(() => {
    const closeButton = document.querySelector('button.c-glyph.glyph-cancel[data-modal-close-button]');
    if (closeButton) {
      closeButton.click();
      console.log('已点击关闭按钮');
    }
  }, 1000);

  // 检查并点击奖励项目
  setTimeout(() => {
    const promoItem = document.querySelector('#promo-item > section > div > div > div');
    if (promoItem) {
      promoItem.click();
      console.log('已点击奖励项目');

      // 注册完成，保存账号信息
      setTimeout(() => {
        saveAccountInfo();
      }, 2000);
    } else {
      // 如果没找到，尝试访问指定URL
      window.location.href = 'https://rewards.bing.com/?form=BILREW';
    }
  }, 2000);
}

// 清除验证码相关数据
function clearVerificationCodeData() {
  console.log('🧹 开始清除验证码相关数据...');

  // 停止IMAP邮件检查
  if (imapService.isWaitingForCode) {
    console.log('🛑 停止IMAP邮件检查服务');
    stopImapEmailCheck();
  }

  // 删除邮箱中的验证码邮件
  if (msRegistrationState.currentAccount && imapService.serverUrl) {
    try {
      console.log('🗑️ 开始删除邮箱中的验证码邮件...');
      fetch(`${imapService.serverUrl}/verification-email/${encodeURIComponent(msRegistrationState.currentAccount)}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json'
        }
      }).then(response => response.json())
        .then(result => {
          if (result.success) {
            console.log('✅ 验证码邮件删除成功:', result);
            console.log(`🗑️ 已删除 ${result.deleted} 封验证码邮件`);
          } else {
            console.log('⚠️ 验证码邮件删除失败:', result.error);
          }
        })
        .catch(error => {
          console.log('⚠️ 删除验证码邮件时出错:', error);
        });
    } catch (error) {
      console.log('⚠️ 删除验证码邮件请求失败:', error);
    }
  }

  // 清除IMAP服务中的验证码缓存
  if (typeof window !== 'undefined' && window.imapServer) {
    try {
      // 清除服务器端的验证码缓存
      fetch(`${imapService.serverUrl}/clear-verification-codes`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          email: msRegistrationState.currentAccount
        })
      }).then(response => response.json())
        .then(result => {
          console.log('✅ 服务器端验证码缓存已清除:', result);
        })
        .catch(error => {
          console.log('⚠️ 清除服务器端验证码缓存失败:', error);
        });
    } catch (error) {
      console.log('⚠️ 清除服务器端验证码缓存时出错:', error);
    }
  }

  // 清除页面上的验证码输入框
  for (let i = 0; i < 6; i++) {
    const codeInput = document.querySelector(`#codeEntry-${i}`);
    if (codeInput && codeInput.value) {
      console.log(`清除验证码输入框 ${i}: ${codeInput.value}`);
      codeInput.value = '';
      codeInput.dispatchEvent(new Event('input', { bubbles: true }));
      codeInput.dispatchEvent(new Event('change', { bubbles: true }));
    }
  }

  // 重置IMAP服务状态
  imapService.isWaitingForCode = false;
  imapService.checkInterval = null;

  console.log('✅ 验证码相关数据清除完成');
}

// 清除随机账号生成数据
function clearRandomAccountData() {
  console.log('🧹 开始清除随机账号生成数据...');

  // 清除页面上的邮箱输入框
  const emailInputs = document.querySelectorAll('input[type="email"], input[name="MemberName"], input[name="电子邮件"]');
  emailInputs.forEach(input => {
    if (input.value && input.value.includes('@s4464.cfd')) {
      console.log(`清除随机账号输入框: ${input.value}`);
      input.value = '';
      input.dispatchEvent(new Event('input', { bubbles: true }));
      input.dispatchEvent(new Event('change', { bubbles: true }));
    }
  });

  // 清除本地存储中的账号信息（但保留当前注册状态）
  chrome.storage.local.remove([
    'microsoft_current_account_temp',
    'microsoft_generated_account'
  ], () => {
    console.log('✅ 临时账号数据已清除');
  });

  console.log('✅ 随机账号生成数据清除完成');
}

// 清除Microsoft注册相关信息（增强版本）
function clearMicrosoftRegistrationData() {
  console.log('🧹 开始清除Microsoft注册相关信息...');

  // 记录清除前的状态
  const oldAccount = msRegistrationState.currentAccount;
  const oldStep = msRegistrationState.currentStep;
  const wasWaitingForCode = imapService.isWaitingForCode;

  console.log(`📊 清除前状态: 账号=${oldAccount || '无'}, 步骤=${oldStep}, 等待验证码=${wasWaitingForCode}`);

  // 停止IMAP邮件检查
  if (imapService.isWaitingForCode) {
    console.log('🛑 停止IMAP邮件检查服务');
    stopImapEmailCheck();
  }

  // 清除服务器端验证码缓存
  if (oldAccount && imapService.serverUrl) {
    try {
      console.log('🗑️ 清除服务器端验证码缓存...');
      fetch(`${imapService.serverUrl}/clear-verification-codes`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          email: oldAccount
        })
      }).then(response => response.json())
        .then(result => {
          console.log('✅ 服务器端验证码缓存已清除:', result);
        })
        .catch(error => {
          console.log('⚠️ 清除服务器端验证码缓存失败:', error);
        });
    } catch (error) {
      console.log('⚠️ 清除服务器端验证码缓存时出错:', error);
    }
  }

  // 使用统一的重置函数
  resetMicrosoftRegistrationState();

  // 清除本地存储中的Microsoft注册状态
  const storageKeysToRemove = [
    'microsoft_registration_status',
    'microsoft_current_account',
    'microsoft_verification_status',
    'microsoft_current_account_temp',
    'microsoft_generated_account',
    'microsoft_stage_progress'
  ];

  chrome.storage.local.remove(storageKeysToRemove, () => {
    console.log('✅ 本地存储中的Microsoft注册状态已清除');
    console.log('🗑️ 已清除的存储键:', storageKeysToRemove);
  });

  // 重置IMAP服务状态
  imapService.currentEmail = null;
  imapService.isWaitingForCode = false;
  imapService.checkInterval = null;
  imapService.retryCount = 0;
  imapService.consecutiveFailures = 0;
  imapService.lastCheckTime = null;

  // 停止检测定时器
  if (msRegistrationState.detectionInterval) {
    clearInterval(msRegistrationState.detectionInterval);
    msRegistrationState.detectionInterval = null;
    console.log('✅ 检测定时器已停止');
  }

  // 清除页面上可能残留的输入值
  clearPageInputs();

  // 清除验证码相关数据
  clearVerificationCodeData();

  console.log(`✅ Microsoft注册信息清除完成`);
  console.log(`📊 清除统计: 原账号=${oldAccount || '无'}, 原步骤=${oldStep}`);
}

// 清除页面上的输入值
function clearPageInputs() {
  console.log('🧹 清除页面输入值...');

  // 清除邮箱输入框
  const emailInputs = document.querySelectorAll('input[type="email"], input[name="MemberName"], input[name="电子邮件"]');
  emailInputs.forEach(input => {
    if (input.value) {
      console.log(`清除邮箱输入框: ${input.value}`);
      input.value = '';
      input.dispatchEvent(new Event('input', { bubbles: true }));
      input.dispatchEvent(new Event('change', { bubbles: true }));
    }
  });

  // 清除验证码输入框
  for (let i = 0; i < 6; i++) {
    const codeInput = document.querySelector(`#codeEntry-${i}`);
    if (codeInput && codeInput.value) {
      console.log(`清除验证码输入框 ${i}: ${codeInput.value}`);
      codeInput.value = '';
      codeInput.dispatchEvent(new Event('input', { bubbles: true }));
      codeInput.dispatchEvent(new Event('change', { bubbles: true }));
    }
  }

  // 清除姓名输入框
  const nameInputs = document.querySelectorAll('#lastNameInput, #firstNameInput');
  nameInputs.forEach(input => {
    if (input.value) {
      console.log(`清除姓名输入框: ${input.value}`);
      input.value = '';
      input.dispatchEvent(new Event('input', { bubbles: true }));
      input.dispatchEvent(new Event('change', { bubbles: true }));
    }
  });

  // 重置下拉框
  const dropdowns = document.querySelectorAll('button[name="BirthMonth"], button[name="BirthDay"]');
  dropdowns.forEach(dropdown => {
    dropdown.setAttribute('aria-expanded', 'false');
    dropdown.blur();
  });

  console.log('✅ 页面输入值清除完成');
}

// 紧急清除所有敏感信息（用于异常情况）
function emergencyClearAllData() {
  console.log('🚨 执行紧急清除所有敏感信息...');

  try {
    // 停止所有定时器和检查
    if (imapService.checkInterval) {
      clearInterval(imapService.checkInterval);
      imapService.checkInterval = null;
    }

    if (msRegistrationState.detectionInterval) {
      clearInterval(msRegistrationState.detectionInterval);
      msRegistrationState.detectionInterval = null;
    }

    // 清除所有相关的本地存储
    const allStorageKeys = [
      'microsoft_registration_status',
      'microsoft_current_account',
      'microsoft_verification_status',
      'microsoft_current_account_temp',
      'microsoft_generated_account',
      'microsoft_stage_progress'
    ];

    chrome.storage.local.remove(allStorageKeys, () => {
      console.log('🗑️ 紧急清除：所有本地存储已清除');
    });

    // 使用统一的重置函数
    resetMicrosoftRegistrationState();

    // 清除页面上的所有相关输入
    try {
      // 清除邮箱输入框
      const emailInputs = document.querySelectorAll('input[type="email"], input[name="MemberName"], input[name="电子邮件"]');
      emailInputs.forEach(input => {
        if (input.value) {
          input.value = '';
          input.dispatchEvent(new Event('input', { bubbles: true }));
          input.dispatchEvent(new Event('change', { bubbles: true }));
        }
      });

      // 清除验证码输入框
      for (let i = 0; i < 6; i++) {
        const codeInput = document.querySelector(`#codeEntry-${i}`);
        if (codeInput && codeInput.value) {
          codeInput.value = '';
          codeInput.dispatchEvent(new Event('input', { bubbles: true }));
          codeInput.dispatchEvent(new Event('change', { bubbles: true }));
        }
      }

      // 清除其他可能的输入框
      const allInputs = document.querySelectorAll('input[type="text"], input[type="password"]');
      allInputs.forEach(input => {
        if (input.value && (input.value.includes('@s4464.cfd') || /^\d{6}$/.test(input.value))) {
          input.value = '';
          input.dispatchEvent(new Event('input', { bubbles: true }));
          input.dispatchEvent(new Event('change', { bubbles: true }));
        }
      });
    } catch (pageError) {
      console.log('⚠️ 清除页面输入时出错:', pageError);
    }

    console.log('✅ 紧急清除完成');
  } catch (error) {
    console.error('❌ 紧急清除过程中出错:', error);
  }
}

// 保存账号信息到文件
function saveAccountInfo() {
  if (!msRegistrationState.currentAccount) {
    console.error('没有账号信息可保存');
    return;
  }

  console.log('开始保存账号信息:', msRegistrationState.currentAccount);

  // 发送消息给background script保存文件
  chrome.runtime.sendMessage({
    action: 'saveMicrosoftAccount',
    account: msRegistrationState.currentAccount,
    timestamp: new Date().toISOString(),
    registrationComplete: true
  }, (response) => {
    if (response && response.success) {
      console.log('账号信息保存成功:', response.filename);
      setCurrentStage('completed');
      msRegistrationState.registrationComplete = true;
      msRegistrationState.verificationStatus = '注册完成';

      // 保存成功后清除所有注册信息
      setTimeout(() => {
        clearMicrosoftRegistrationData();
      }, 2000); // 等待2秒让用户看到完成状态

    } else {
      console.error('账号信息保存失败:', response?.error);
      msRegistrationState.verificationStatus = '保存失败';
      saveMicrosoftRegistrationState();
    }
  });
}

// 错误处理和重试机制
function handleRegistrationError(error, step) {
  console.error(`Microsoft注册在步骤 ${step} 出错:`, error);
  msRegistrationState.verificationStatus = `错误: ${error.message}`;
  msRegistrationState.attempts++;

  if (msRegistrationState.attempts < msRegistrationState.maxAttempts) {
    console.log(`尝试重试，当前尝试次数: ${msRegistrationState.attempts}`);
    setTimeout(() => {
      handleMicrosoftRegistration();
    }, 5000); // 5秒后重试
  } else {
    console.error('达到最大重试次数，停止注册流程');
    msRegistrationState.verificationStatus = '注册失败';
  }

  saveMicrosoftRegistrationState();
}

// 检查扩展上下文是否有效
function isExtensionContextValid() {
  try {
    // 尝试访问chrome.runtime，如果失效会抛出错误
    if (typeof chrome !== 'undefined' && chrome.runtime && chrome.runtime.id) {
      return true;
    }
    return false;
  } catch (error) {
    console.warn('🔄 扩展上下文检查失败:', error.message);
    return false;
  }
}

// 安全执行函数，带有上下文检查
function safeExecute(func, errorContext = '未知操作') {
  try {
    if (!isExtensionContextValid()) {
      console.warn('⚠️ 扩展上下文已失效，跳过执行:', errorContext);
      return false;
    }
    return func();
  } catch (error) {
    if (error.message.includes('Extension context invalidated')) {
      console.warn('🔄 扩展上下文失效，停止当前操作:', errorContext);
      // 停止所有定时器
      if (msRegistrationState.detectionInterval) {
        clearInterval(msRegistrationState.detectionInterval);
        msRegistrationState.detectionInterval = null;
      }
      return false;
    } else {
      console.error('❌ 执行出错:', errorContext, error);
      throw error;
    }
  }
}

// 检查页面加载状态
function waitForPageLoad(callback, timeout = 10000) {
  const startTime = Date.now();
  console.log('⏳ 等待页面加载完成，当前状态:', document.readyState);

  function checkLoad() {
    // 检查扩展上下文
    if (!isExtensionContextValid()) {
      console.warn('⚠️ 扩展上下文失效，停止页面加载检查');
      return;
    }

    if (document.readyState === 'complete') {
      console.log('✅ 页面加载完成，等待1秒后执行回调');
      setTimeout(() => {
        safeExecute(callback, '页面加载回调');
      }, 1000);
    } else if (Date.now() - startTime < timeout) {
      console.log('⏳ 页面仍在加载中，状态:', document.readyState);
      setTimeout(checkLoad, 500);
    } else {
      console.error('❌ 页面加载超时');
      safeExecute(() => {
        handleRegistrationError(new Error('页面加载超时'), msRegistrationState.currentStep);
      }, '页面加载超时处理');
    }
  }

  checkLoad();
}

// 监听URL变化
function setupUrlChangeListener() {
  let lastUrl = window.location.href;

  // 监听pushState和replaceState
  const originalPushState = history.pushState;
  const originalReplaceState = history.replaceState;

  history.pushState = function() {
    originalPushState.apply(history, arguments);
    setTimeout(() => {
      if (window.location.href !== lastUrl) {
        console.log('🔄 检测到URL变化 (pushState):', window.location.href);
        lastUrl = window.location.href;
        // URL变化时重置处理状态，允许重新检测
        msRegistrationState.isProcessing = false;
      }
    }, 100);
  };

  history.replaceState = function() {
    originalReplaceState.apply(history, arguments);
    setTimeout(() => {
      if (window.location.href !== lastUrl) {
        console.log('🔄 检测到URL变化 (replaceState):', window.location.href);
        lastUrl = window.location.href;
        msRegistrationState.isProcessing = false;
      }
    }, 100);
  };

  // 监听popstate事件
  window.addEventListener('popstate', () => {
    setTimeout(() => {
      if (window.location.href !== lastUrl) {
        console.log('🔄 检测到URL变化 (popstate):', window.location.href);
        lastUrl = window.location.href;
        msRegistrationState.isProcessing = false;
      }
    }, 100);
  });
}

// 启动Microsoft注册页面检测
function startMicrosoftRegistrationDetection() {
  // 检查扩展上下文
  if (!isExtensionContextValid()) {
    console.warn('⚠️ 扩展上下文无效，无法启动检测');
    return;
  }

  // 如果已经有检测定时器在运行，先清除
  if (msRegistrationState.detectionInterval) {
    console.log('清除现有的检测定时器');
    clearInterval(msRegistrationState.detectionInterval);
  }

  // 设置URL变化监听
  setupUrlChangeListener();

  // 立即执行一次检测
  console.log('启动Microsoft注册页面检测');
  handleMicrosoftRegistration();

  // 设置定时器持续检测页面变化
  msRegistrationState.detectionInterval = setInterval(() => {
    // 在每次执行前检查扩展上下文
    if (!isExtensionContextValid()) {
      console.warn('⚠️ 扩展上下文失效，停止检测定时器');
      clearInterval(msRegistrationState.detectionInterval);
      msRegistrationState.detectionInterval = null;
      return;
    }
    handleMicrosoftRegistration();
  }, 2000);

  console.log('Microsoft注册检测定时器已启动，间隔2秒');
}

// 停止Microsoft注册页面检测
function stopMicrosoftRegistrationDetection() {
  if (msRegistrationState.detectionInterval) {
    console.log('停止Microsoft注册页面检测');
    clearInterval(msRegistrationState.detectionInterval);
    msRegistrationState.detectionInterval = null;
  }
}

// 检测当前页面类型和状态
function detectCurrentPageState() {
  const currentUrl = window.location.href;

  // 添加详细的调试信息
  console.log('🔍 检测页面状态:', currentUrl);

  // 优先检测具体的页面类型，再检测通用的页面类型
  if (isDataPermissionPage()) {
    console.log('✅ 检测到数据许可页面');
    return { type: 'data_permission', url: currentUrl };
  } else if (isVerificationCodePage()) {
    console.log('✅ 检测到验证码输入页面');
    return { type: 'verification_code', url: currentUrl };
  } else if (isEmailVerificationPage()) {
    console.log('✅ 检测到邮件验证页面');
    return { type: 'email_verification', url: currentUrl };
  } else if (isPersonalInfoPage()) {
    console.log('✅ 检测到个人信息页面');
    return { type: 'personal_info', url: currentUrl };
  } else if (isNamePage()) {
    console.log('✅ 检测到姓名页面');
    return { type: 'name', url: currentUrl };
  } else if (isCaptchaPage()) {
    console.log('✅ 检测到人机验证页面');
    return { type: 'captcha', url: currentUrl };
  } else if (isLoginCompletePage()) {
    console.log('✅ 检测到登录完成页面');
    return { type: 'login_complete', url: currentUrl };
  } else if (isRewardsWelcomePage()) {
    console.log('✅ 检测到奖励欢迎页面');
    return { type: 'rewards_welcome', url: currentUrl };
  } else if (isMicrosoftSignupPage()) {
    console.log('✅ 检测到Microsoft注册页面');
    return { type: 'signup', url: currentUrl };
  }

  console.log('❓ 未识别的页面类型');
  // 输出页面基本信息用于调试
  const bodyText = document.body ? document.body.textContent.substring(0, 300) : '';
  console.log('页面文本预览:', bodyText);

  return { type: 'unknown', url: currentUrl };
}

// 检查是否需要处理当前页面
function shouldProcessCurrentPage() {
  const currentState = detectCurrentPageState();
  const currentStage = getCurrentStage();

  console.log(`📋 页面处理检查: 检测到=${currentState.type}, 当前阶段=${currentStage}`);

  // 严格的阶段控制：只处理当前阶段应该处理的页面类型
  const stagePageMapping = {
    'none': ['data_permission', 'signup', 'personal_info'],
    'data_permission': ['data_permission', 'signup'],
    'signup': ['signup', 'personal_info'],
    'personal_info': ['personal_info', 'verification_code'],
    'verification_code': ['verification_code', 'email_verification', 'captcha'],
    'email_verification': ['email_verification', 'captcha'],
    'captcha': ['captcha', 'login_complete'],
    'login_complete': ['login_complete', 'rewards_welcome']
  };

  const allowedPages = stagePageMapping[currentStage] || [];

  if (!allowedPages.includes(currentState.type)) {
    console.log(`⏭️ 跳过处理: 当前阶段 ${currentStage} 不应处理 ${currentState.type} 页面`);
    return false;
  }

  // 特殊处理：人机验证页面需要停止IMAP服务
  if (currentState.type === 'captcha') {
    console.log('🔍 当前是人机验证页面，停止验证码相关检测');

    // 停止IMAP服务的验证码等待
    if (imapService.isWaitingForCode) {
      console.log('🛑 停止IMAP验证码等待服务');
      stopImapEmailCheck();
    }

    // 对于人机验证页面，需要持续处理，因为验证过程可能需要多次尝试
    console.log('🔄 人机验证页面，保持持续检测和处理');
    return true;
  }

  // 对于验证码相关页面，始终保持检测，不受页面变化限制
  if (currentState.type === 'verification_code' || currentState.type === 'email_verification') {
    console.log('🔄 验证码相关页面，保持持续检测:', currentState.type);
    return true;
  }

  // 如果正在处理中，跳过其他页面的检测
  if (msRegistrationState.isProcessing) {
    console.log('正在处理中，跳过检测');
    return false;
  }

  // 如果URL发生了变化，说明页面跳转了，需要处理
  if (currentState.url !== msRegistrationState.lastProcessedUrl) {
    console.log('检测到页面URL变化:', {
      from: msRegistrationState.lastProcessedUrl,
      to: currentState.url
    });
    return true;
  }

  // 如果URL相同但页面类型不同，说明页面内容变化了，需要处理
  if (currentState.type !== msRegistrationState.lastProcessedStep) {
    console.log('检测到页面类型变化:', {
      from: msRegistrationState.lastProcessedStep,
      to: currentState.type
    });
    return true;
  }

  // 如果是第一次检测（没有处理过任何页面），需要处理
  if (!msRegistrationState.lastProcessedUrl && !msRegistrationState.lastProcessedStep) {
    console.log('首次检测页面，需要处理:', currentState.type);
    return true;
  }

  // 其他情况跳过
  console.log('页面状态未变化，跳过处理:', currentState.type);
  return false;
}

// 标记页面处理完成
function markPageProcessed(pageType, url) {
  msRegistrationState.lastProcessedUrl = url;
  msRegistrationState.lastProcessedStep = pageType;
  msRegistrationState.isProcessing = false;
  console.log('✅ 页面处理完成:', { type: pageType, url: url });
}

// 主要的Microsoft注册处理函数 - 基于阶段的处理（增强错误恢复版）
function handleMicrosoftRegistration() {
  return safeExecute(() => {
    // 检查是否需要处理
    if (!shouldProcessCurrentPage()) {
      return;
    }

    // 检测当前页面状态
    const currentState = detectCurrentPageState();

    // 检查是否应该处理这个阶段
    if (!shouldProcessStage(currentState.type)) {
      console.log(`⏭️ 跳过阶段 ${currentState.type}，当前应处理阶段: ${getExpectedStage()}`);
      return;
    }

    // 检查是否有卡住的状态需要重置
    if (window.birthDateSelectionInProgress || window.longPressInProgress) {
      console.log('⚠️ 检测到可能卡住的状态，重置后继续');
      window.birthDateSelectionInProgress = false;
      window.longPressInProgress = false;
    }

    // 设置处理状态
    msRegistrationState.isProcessing = true;
    console.log(`🎯 开始处理阶段: ${currentState.type}`);

    // 等待页面完全加载
    waitForPageLoad(() => {
      safeExecute(() => {
        // 根据阶段执行相应的处理
        switch (currentState.type) {
          case 'data_permission':
            handleDataPermissionPage();
            setTimeout(() => {
              if (window.location.href === currentState.url) {
                console.log('⚠️ 数据许可页面操作可能失败，重置处理状态');
                msRegistrationState.isProcessing = false;
              } else {
                advanceToNextStage('data_permission');
              }
            }, 5000);
            break;

          case 'signup':
            // 确保设置正确的阶段索引
            if (msRegistrationState.currentStageIndex === -1) {
              setCurrentStage('signup');
            }

            handleMicrosoftSignupPage();
            setTimeout(() => {
              if (window.location.href === currentState.url) {
                console.log('⚠️ 注册页面操作可能失败，重置处理状态');
                msRegistrationState.isProcessing = false;
              } else {
                advanceToNextStage('signup');
              }
            }, 5000);
            break;

          case 'email_verification':
            handleEmailVerificationPage();
            // 邮件验证页面的阶段推进在 handleEmailVerificationPage 中处理
            msRegistrationState.isProcessing = false;
            break;

          case 'verification_code':
            handleVerificationCodePage();
            // 验证码页面不需要立即跳转，等待验证码输入完成
            // advanceToNextStage 会在验证码提交成功后调用
            msRegistrationState.isProcessing = false;
            break;
          case 'personal_info':
            handlePersonalInfoPage();
            // 个人信息页面不需要立即跳转，等待表单填写和提交完成
            // advanceToNextStage 会在下一步按钮点击成功后调用
            msRegistrationState.isProcessing = false;
            break;

          case 'name':
            handleNamePage();
            // 姓名页面不需要立即跳转，等待表单填写和提交完成
            // advanceToNextStage 会在下一步按钮点击成功后调用
            msRegistrationState.isProcessing = false;
            break;

          case 'captcha':
            handleCaptchaPage();
            // 人机验证页面不立即标记为已处理，因为验证过程可能需要多次尝试
            // 验证成功后会在 checkCaptchaVerificationResult 中处理阶段推进
            msRegistrationState.isProcessing = false;
            break;

          case 'login_complete':
            handleLoginCompletePage();
            advanceToNextStage('login_complete');
            markPageProcessed(currentState.type, currentState.url);
            break;

          case 'rewards_welcome':
            handleRewardsWelcomePage();
            advanceToNextStage('rewards_welcome');
            markPageProcessed(currentState.type, currentState.url);
            break;

          default:
            console.log('❓ 未识别的Microsoft页面:', currentState.url);
            msRegistrationState.isProcessing = false;
        }
      }, '页面处理回调');
    });
  }, 'Microsoft注册处理主函数');
}

// 页面处理错误的专门处理函数
function handlePageProcessingError(error, currentState) {
  console.log('🔧 处理页面处理错误:', {
    error: error.message,
    pageType: currentState.type,
    url: currentState.url
  });

  // 根据错误类型进行不同的恢复策略
  if (error.message.includes('querySelector')) {
    console.log('🔄 选择器错误，尝试重新检测页面元素');

    // 针对不同页面类型的特定恢复
    switch (currentState.type) {
      case 'personal_info':
        console.log('🔄 个人信息页面选择器错误，重置生日选择状态');
        window.birthDateSelectionInProgress = false;
        setTimeout(() => {
          try {
            handlePersonalInfoPage();
          } catch (retryError) {
            console.error('🚨 个人信息页面重试失败:', retryError);
          }
        }, 3000);
        break;

      case 'captcha':
        console.log('🔄 人机验证页面选择器错误，重置长按状态');
        window.longPressInProgress = false;
        setTimeout(() => {
          try {
            handleCaptchaPage();
          } catch (retryError) {
            console.error('🚨 人机验证页面重试失败:', retryError);
          }
        }, 3000);
        break;

      default:
        console.log('🔄 通用选择器错误恢复');
        setTimeout(() => {
          msRegistrationState.isProcessing = false;
        }, 2000);
    }
  } else if (error.message.includes('timeout') || error.message.includes('等待')) {
    console.log('🔄 超时错误，增加等待时间后重试');
    setTimeout(() => {
      msRegistrationState.isProcessing = false;
      // 重新尝试处理当前页面
      setTimeout(() => {
        handleMicrosoftRegistration();
      }, 5000);
    }, 2000);
  } else {
    console.log('🔄 未知错误，执行通用恢复策略');
    // 重置所有可能的卡住状态
    window.birthDateSelectionInProgress = false;
    window.longPressInProgress = false;

    setTimeout(() => {
      msRegistrationState.isProcessing = false;
    }, 3000);
  }
}

// ==================== 结束Microsoft账号注册功能 ====================

// 哔哩搜索页面处理
function handleBiliSearchPage() {
  // 发送页面加载完毕状态
  chrome.storage.local.set({
    'biliSearch_status': {
      pageLoaded: true,
      searchModified: false,
      searchCompleted: false,
      timestamp: new Date().toISOString()
    }
  });

  // 等待页面完全加载
  setTimeout(() => {
    const searchInput = document.querySelector('#sb_form_q');
    const searchButton = document.querySelector('#sb_form_go');

    if (searchInput && searchButton) {
      // 生成随机搜索词：哔x哩x哔x哩
      const randomChar1 = getRandomChar();
      const randomChar2 = getRandomChar();
      const randomChar3 = getRandomChar();
      const newSearchTerm = `哔${randomChar1}哩${randomChar2}哔${randomChar3}哩`;

      // 修改搜索词
      searchInput.value = newSearchTerm;

      // 触发input事件，确保页面识别到值的变化
      const inputEvent = new Event('input', { bubbles: true });
      searchInput.dispatchEvent(inputEvent);

      // 更新状态
      chrome.storage.local.set({
        'biliSearch_status': {
          pageLoaded: true,
          searchModified: true,
          searchCompleted: false,
          searchTerm: newSearchTerm,
          timestamp: new Date().toISOString()
        }
      });

      // 触发搜索 - 多种方式确保成功
      // 方式1: 直接点击按钮
      searchButton.click();

      // 方式2: 触发表单提交
      const form = document.querySelector('#sb_form');
      if (form) {
        form.submit();
      }

      // 方式3: 模拟键盘回车
      const enterEvent = new KeyboardEvent('keydown', {
        key: 'Enter',
        code: 'Enter',
        keyCode: 13,
        which: 13,
        bubbles: true
      });
      searchInput.dispatchEvent(enterEvent);

      // 方式4: 模拟鼠标点击事件
      const mouseEvent = new MouseEvent('click', {
        view: window,
        bubbles: true,
        cancelable: true,
        clientX: 0,
        clientY: 0
      });
      searchButton.dispatchEvent(mouseEvent);

      // 方式5: 直接导航到搜索URL
      setTimeout(() => {
        const searchUrl = `https://cn.bing.com/search?q=${encodeURIComponent(newSearchTerm)}&qs=n&form=BILREW`;
        window.location.href = searchUrl;
      }, 500);

      // 不再在这里设置延时，因为页面会跳转，这些代码不会执行
    }
  }, 1000);
}

// 处理哔哩搜索结果页面
function handleBiliSearchResultPage() {
  // 从存储中获取当前状态，保持搜索词不变
  chrome.storage.local.get('biliSearch_status', (result) => {
    const currentStatus = result.biliSearch_status || {};

    // 更新状态为搜索完成，保持原有的搜索词
    chrome.storage.local.set({
      'biliSearch_status': {
        pageLoaded: true,
        searchModified: true,
        searchCompleted: true,
        searchTerm: currentStatus.searchTerm || '搜索已完成',
        timestamp: new Date().toISOString()
      }
    });
  });

  // 等待3秒后关闭标签页
  setTimeout(() => {
    chrome.runtime.sendMessage({
      action: 'closeBiliTab'
    });
  }, 3000);
}

// 页面加载完成后开始检测
console.log('📄 页面状态:', document.readyState, '| URL:', window.location.href);

try {
  if (document.readyState === 'loading') {
    console.log('⏳ 等待DOM加载完成...');
    document.addEventListener('DOMContentLoaded', () => {
      console.log('✅ DOM加载完成，开始初始化...');
      initializePageDetection();
    });
  } else {
    console.log('✅ DOM已就绪，立即初始化...');
    initializePageDetection();
  }
} catch (error) {
  console.error('🚨 页面初始化失败:', error);
}

// 页面初始化函数
function initializePageDetection() {
  try {
    // 初始化错误恢复机制
    initializeErrorRecovery();

    console.log('🔍 开始页面类型检测...');

    if (isBiliSearchPage()) {
      console.log('📍 检测到哔哩搜索页面');
      handleBiliSearchPage();
    } else if (isBiliSearchResultPage()) {
      console.log('📍 检测到哔哩搜索结果页面');
      handleBiliSearchResultPage();
    } else if (isDataPermissionPage() || isMicrosoftSignupPage() || isVerificationCodePage() || isEmailVerificationPage() ||
               isPersonalInfoPage() || isNamePage() || isCaptchaPage() || isLoginCompletePage() || isRewardsWelcomePage()) {
      // Microsoft注册相关页面 - 使用智能检测
      console.log('📍 检测到Microsoft注册相关页面');
      console.log('🚀 启动Microsoft注册检测，已启用错误恢复机制');
      startMicrosoftRegistrationDetection();
    } else {
      console.log('📍 检测到其他页面，启动通用检测');
      startDetection();
    }
  } catch (error) {
    console.error('🚨 页面检测初始化失败:', error);
  }
}

// 页面卸载时清理资源
window.addEventListener('beforeunload', () => {
  console.log('页面即将卸载，清理资源...');
  stopMicrosoftRegistrationDetection();
  stopImapEmailCheck();
});

// 页面隐藏时暂停检测（可选优化）
document.addEventListener('visibilitychange', () => {
  if (document.hidden) {
    console.log('页面隐藏，暂停不必要的检测');
  } else {
    console.log('页面显示，恢复检测');
    // 如果是Microsoft相关页面，重新启动检测
    if (isDataPermissionPage() || isMicrosoftSignupPage() || isVerificationCodePage() || isEmailVerificationPage() ||
        isPersonalInfoPage() || isNamePage() || isCaptchaPage() || isLoginCompletePage() || isRewardsWelcomePage()) {
      startMicrosoftRegistrationDetection();
    }
  }
});

// 监听来自popup的消息 - v3版本
chrome.runtime.onMessage.addListener(async (message, sender, sendResponse) => {
  console.log('📨 收到消息:', message.action);

  try {
    switch (message.action) {
      case 'startMicrosoftRegistration':
        console.log('🚀 启动Microsoft注册流程');
        const startResult = await startMicrosoftRegistration();
        sendResponse({ success: startResult });
        break;

      case 'stopMicrosoftRegistration':
        console.log('🛑 停止Microsoft注册流程');
        const stopResult = await stopMicrosoftRegistration();
        sendResponse({ success: stopResult });
        break;

      case 'getMicrosoftRegistrationStatus':
        console.log('📊 获取注册状态');
        const status = getMicrosoftRegistrationStatus();
        sendResponse({ success: true, status });
        break;

      case 'resetMicrosoftRegistration':
        console.log('🔄 重置注册流程');
        const resetResult = await resetMicrosoftRegistration();
        sendResponse({ success: resetResult });
        break;

      case 'checkNewRegistration':
        console.log('📨 收到popup的新注册检查请求');
        // 兼容旧版本popup
        const isNewRegistrationStartPage = isNewRegistrationStart();
        const hasExistingAccount = msRegistrationState.currentAccount !== null;
        const isLoginScenario = isEmailVerificationLogin();

        if (isNewRegistrationStartPage && hasExistingAccount && !isLoginScenario) {
          console.log('🔄 popup触发：检测到全新的注册流程开始，清除之前的账号状态');
          console.log('🗑️ popup触发：清除之前的账号:', msRegistrationState.currentAccount);

          // 使用统一的重置函数
          resetMicrosoftRegistrationState();

          // 清除本地存储中的旧状态
          chrome.storage.local.remove(['microsoft_registration_status'], () => {
            console.log('✅ popup触发：已清除本地存储中的旧注册状态');
            sendResponse({ success: true, cleared: true });
          });
        } else {
          sendResponse({ success: true, cleared: false });
        }
        break;

      default:
        console.log('❓ 未知消息类型:', message.action);
        sendResponse({ success: false, error: 'Unknown action' });
        break;
    }

    return true; // 保持消息通道开放
  } catch (error) {
    console.error('❌ 处理消息时发生错误:', error);
    sendResponse({ success: false, error: error.message });
    return true;
  }
});





