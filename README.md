# RTBS - Real-Time Browser Service

RTBS是一个智能的浏览器扩展和IMAP服务器系统，专为自动化Microsoft账户注册和验证码处理而设计。

## 项目结构

```
RTBS/
├── src/                    # 源代码目录
│   ├── extension/          # 浏览器扩展代码
│   │   ├── manifest.json   # 扩展配置文件
│   │   ├── background.js   # 后台脚本
│   │   ├── content.js      # 内容脚本
│   │   ├── popup.html      # 弹出页面
│   │   ├── popup.js        # 弹出页面脚本
│   │   ├── icon/           # 扩展图标
│   │   └── *.js            # 其他扩展脚本
│   ├── server/             # 服务器代码
│   │   ├── imap-server.js  # 主IMAP服务器
│   │   └── *.js            # 其他服务器脚本
│   └── shared/             # 共享代码和调试工具
├── config/                 # 配置文件
│   └── imap-config.json    # IMAP配置
├── docs/                   # 项目文档
├── scripts/                # 批处理脚本
├── tests/                  # 测试文件
├── logs/                   # 日志目录
└── node_modules/           # 依赖包

```

## 功能特性

- **自动化注册**: 智能处理Microsoft账户注册流程
- **验证码处理**: 实时接收和处理邮箱验证码
- **状态机管理**: 基于状态机的注册流程控制
- **错误处理**: 完善的错误处理和重试机制
- **IMAP服务**: 高效的邮件监控和处理服务

## 快速开始

### 1. 安装依赖
```bash
npm install
```

### 2. 配置IMAP
编辑 `config/imap-config.json` 文件，配置您的邮箱信息。

### 3. 启动服务器
```bash
npm start
```

### 4. 安装浏览器扩展
1. 打开Chrome浏览器
2. 进入扩展管理页面 (chrome://extensions/)
3. 开启开发者模式
4. 点击"加载已解压的扩展程序"
5. 选择 `src/extension` 目录

## 开发指南

### 启动开发模式
```bash
npm run dev
```

### 运行测试
```bash
# 运行所有测试
cd tests
# 在浏览器中打开测试文件
```

### 项目脚本
- `scripts/start-imap-server.bat` - 启动IMAP服务器
- `scripts/setup-config.bat` - 配置设置
- `scripts/smart-start.bat` - 智能启动

## 文档

详细文档请查看 `docs/` 目录：
- [快速开始指南](docs/QUICK_START_GUIDE.md)
- [IMAP设置指南](docs/IMAP_SETUP_GUIDE.md)
- [Microsoft注册指南](docs/MICROSOFT_REGISTRATION_GUIDE.md)
- [部署检查清单](docs/DEPLOYMENT_CHECKLIST.md)

## 版本信息

- **当前版本**: 3.0.0
- **Node.js要求**: >= 14.0.0
- **npm要求**: >= 6.0.0

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request来改进项目。

## 支持

如有问题，请查看文档或提交Issue。
