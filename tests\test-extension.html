<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RTBS扩展测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-button {
            background: #007cba;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #005a87;
        }
        .log-area {
            background: #f8f8f8;
            border: 1px solid #ddd;
            padding: 10px;
            height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <h1>🚀 RTBS扩展测试页面</h1>
    
    <div class="test-section">
        <h2>📊 扩展状态检测</h2>
        <div id="extension-status" class="status info">正在检测扩展状态...</div>
        <button class="test-button" onclick="checkExtensionStatus()">重新检测扩展</button>
        <button class="test-button" onclick="testConsoleOutput()">测试控制台输出</button>
    </div>

    <div class="test-section">
        <h2>🔧 页面类型模拟</h2>
        <p>点击下面的按钮来模拟不同类型的页面，测试扩展的检测功能：</p>
        <button class="test-button" onclick="simulateMicrosoftPage()">模拟Microsoft注册页面</button>
        <button class="test-button" onclick="simulateBiliPage()">模拟哔哩搜索页面</button>
        <button class="test-button" onclick="simulateRewardsPage()">模拟Rewards页面</button>
    </div>

    <div class="test-section">
        <h2>📝 控制台日志</h2>
        <div id="log-area" class="log-area">等待日志输出...</div>
        <button class="test-button" onclick="clearLogs()">清空日志</button>
        <button class="test-button" onclick="exportLogs()">导出日志</button>
    </div>

    <script>
        let logs = [];
        
        // 重写console.log来捕获日志
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;
        
        function addLog(type, ...args) {
            const timestamp = new Date().toLocaleTimeString();
            const message = args.map(arg => 
                typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
            ).join(' ');
            
            logs.push(`[${timestamp}] ${type.toUpperCase()}: ${message}`);
            updateLogDisplay();
            
            // 调用原始的console方法
            if (type === 'log') originalLog(...args);
            else if (type === 'error') originalError(...args);
            else if (type === 'warn') originalWarn(...args);
        }
        
        console.log = (...args) => addLog('log', ...args);
        console.error = (...args) => addLog('error', ...args);
        console.warn = (...args) => addLog('warn', ...args);
        
        function updateLogDisplay() {
            const logArea = document.getElementById('log-area');
            logArea.textContent = logs.slice(-50).join('\n'); // 只显示最近50条日志
            logArea.scrollTop = logArea.scrollHeight;
        }
        
        function clearLogs() {
            logs = [];
            updateLogDisplay();
        }
        
        function exportLogs() {
            const logText = logs.join('\n');
            const blob = new Blob([logText], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `rtbs-logs-${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.txt`;
            a.click();
            URL.revokeObjectURL(url);
        }
        
        function checkExtensionStatus() {
            const statusDiv = document.getElementById('extension-status');
            
            // 检查是否有RTBS相关的全局变量或函数
            const hasRTBSGlobals = typeof window.msRegistrationState !== 'undefined' ||
                                  typeof window.imapService !== 'undefined' ||
                                  typeof window.birthDateSelectionInProgress !== 'undefined';
            
            if (hasRTBSGlobals) {
                statusDiv.className = 'status success';
                statusDiv.textContent = '✅ RTBS扩展已成功加载并运行';
            } else {
                statusDiv.className = 'status error';
                statusDiv.textContent = '❌ RTBS扩展未检测到或未正常运行';
            }
            
            console.log('🔍 扩展状态检测完成');
            console.log('全局变量检测:', {
                msRegistrationState: typeof window.msRegistrationState,
                imapService: typeof window.imapService,
                birthDateSelectionInProgress: typeof window.birthDateSelectionInProgress
            });
        }
        
        function testConsoleOutput() {
            console.log('🧪 测试控制台输出功能');
            console.warn('⚠️ 这是一个警告消息');
            console.error('❌ 这是一个错误消息');
            console.log('📊 当前页面URL:', window.location.href);
            console.log('📊 当前时间:', new Date().toISOString());
        }
        
        function simulateMicrosoftPage() {
            console.log('🎭 模拟Microsoft注册页面环境');
            // 创建一些Microsoft页面的典型元素
            const testDiv = document.createElement('div');
            testDiv.innerHTML = `
                <h1 data-testid="title">创建账户</h1>
                <input name="BirthYear" placeholder="年">
                <button name="BirthMonth">月</button>
                <button name="BirthDay">日</button>
            `;
            document.body.appendChild(testDiv);
            console.log('✅ Microsoft页面元素已添加');
        }
        
        function simulateBiliPage() {
            console.log('🎭 模拟哔哩搜索页面环境');
            // 修改URL来模拟哔哩搜索页面
            history.pushState({}, '', 'https://cn.bing.com/search?q=%E5%93%94%E5%93%A9%E5%93%94%E5%93%A9&qs=n&form=BILREW');
            console.log('✅ URL已修改为哔哩搜索页面');
        }
        
        function simulateRewardsPage() {
            console.log('🎭 模拟Rewards页面环境');
            // 修改URL来模拟Rewards页面
            history.pushState({}, '', 'https://rewards.bing.com/');
            console.log('✅ URL已修改为Rewards页面');
        }
        
        // 页面加载完成后自动检测扩展状态
        window.addEventListener('load', () => {
            setTimeout(checkExtensionStatus, 1000);
            console.log('🚀 测试页面已加载完成');
        });
    </script>
</body>
</html>
