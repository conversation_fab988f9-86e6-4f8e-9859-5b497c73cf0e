<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <style>
    body {
      width: 220px;
      padding: 12px;
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      margin: 0;
      border-radius: 8px;
      overflow: hidden;
    }
    
    /* 标题行 */
    .header-row {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 12px;
    }

    .title {
      font-size: 16px;
      font-weight: bold;
      color: #333;
    }

    .indicator {
      display: inline-block;
      width: 8px;
      height: 8px;
      border-radius: 50%;
      margin-right: 4px;
    }

    .indicator.active {
      background: #107c10;
    }

    .indicator.inactive {
      background: #d13438;
    }

    /* 信息行通用样式 */
    .info-row {
      display: flex;
      align-items: center;
      margin-bottom: 8px;
      padding: 8px 10px;
      border-radius: 5px;
      border-left: 3px solid;
    }

    /* 账号信息行 */
    .account-row {
      background: linear-gradient(135deg, #e8f5e8 0%, #f1f8e9 100%);
      border-left-color: #4caf50;
    }

    .account-value {
      font-size: 11px;
      color: #1b5e20;
      font-family: 'Courier New', monospace;
      font-weight: bold;
      word-break: break-all;
      flex: 1;
    }

    .account-value.waiting {
      font-weight: normal;
    }



    /* 天数信息行 */
    .days-row {
      background: linear-gradient(135deg, #fff3e0 0%, #fce4ec 100%);
      border-left-color: #ff9800;
    }

    .days-progress {
      font-size: 11px;
      color: #bf360c;
      font-weight: bold;
      font-family: 'Courier New', monospace;
      flex: 1;
    }

    /* 详细信息行 */
    .details-row {
      background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
      border-left-color: #2196f3;
      align-items: center;
    }

    .details-content {
      font-size: 11px;
      color: #1565c0;
      font-family: 'Courier New', monospace;
      font-weight: bold;
      flex: 1;
    }

    /* 行标签 */
    .row-label {
      font-size: 11px;
      color: #666;
      font-weight: 600;
      width: 32px;
      margin-right: 8px;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }
    /* 按钮区域 */
    .buttons-row {
      display: flex;
      gap: 6px;
      margin-top: 4px;
    }

    .btn-compact {
      flex: 1;
      padding: 6px 8px;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 11px;
      font-weight: 500;
      background: #fafafa;
      color: #757575;
      border: 1px solid #e0e0e0;
    }

    .btn-compact:hover {
      background: #f0f0f0;
      color: #424242;
      border-color: #bdbdbd;
    }
    
    .logs {
      margin-top: 15px;
      max-height: 200px;
      overflow-y: auto;
    }
    
    .log-item {
      background: #f9f9f9;
      border-left: 3px solid #0078d4;
      padding: 8px;
      margin-bottom: 5px;
      font-size: 11px;
    }
    
    .log-time {
      color: #666;
      font-weight: bold;
    }
    
    .log-message {
      color: #333;
      margin-top: 2px;
    }
    

    
    .no-data {
      text-align: center;
      color: #666;
      font-style: normal;
      font-size: 12px;
      padding: 20px;
    }

    /* RewardTracker标题样式 */
    .main-title {
      background: linear-gradient(135deg, #2196f3 0%, #1565c0 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      font-weight: bold;
    }

    /* BiliSearch标题样式 */
    .bili-title {
      background: linear-gradient(135deg, #ff6b9d 0%, #c44569 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      font-weight: bold;
    }

    /* BiliSearch信息行粉色系渐变 - 更浅更柔和 */
    .bili-page-row {
      background: linear-gradient(135deg, #fdf2f8 0%, #fce7f3 100%);
      border-left-color: #ec4899;
    }

    .bili-search-row {
      background: linear-gradient(135deg, #faf5ff 0%, #f3e8ff 100%);
      border-left-color: #a855f7;
    }

    .bili-term-row {
      background: linear-gradient(135deg, #fff7ed 0%, #fed7aa 100%);
      border-left-color: #f59e0b;
    }

    .bili-complete-row {
      background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
      border-left-color: #22c55e;
    }

    /* 面板间距控制 */
    #biliSearchPanel.with-margin {
      margin-top: 12px;
    }

    /* Microsoft注册标题样式 */
    .ms-title {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      font-weight: bold;
    }

    /* Microsoft注册信息行紫色系渐变 */
    .ms-account-row {
      background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
      border-left-color: #0ea5e9;
    }

    .ms-step-row {
      background: linear-gradient(135deg, #fefce8 0%, #fef3c7 100%);
      border-left-color: #eab308;
    }

    .ms-verify-row {
      background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
      border-left-color: #22c55e;
    }

    .ms-status-row {
      background: linear-gradient(135deg, #faf5ff 0%, #f3e8ff 100%);
      border-left-color: #a855f7;
    }

    /* 版本标识样式 */
    .version-badge {
      display: inline-block;
      padding: 2px 6px;
      border-radius: 10px;
      font-size: 10px;
      font-weight: bold;
      margin-left: 8px;
      vertical-align: middle;
    }

    .version-badge.v3 {
      background: linear-gradient(135deg, #10b981 0%, #059669 100%);
      color: white;
      box-shadow: 0 1px 3px rgba(16, 185, 129, 0.3);
    }

    .version-badge.legacy {
      background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
      color: white;
      box-shadow: 0 1px 3px rgba(245, 158, 11, 0.3);
    }

    /* 状态样式增强 */
    .details-content.error {
      color: #dc2626;
      font-weight: 500;
    }

    .details-content.success {
      color: #059669;
      font-weight: 500;
    }

    .details-content.processing {
      color: #0ea5e9;
      font-weight: 500;
      animation: pulse 2s infinite;
    }

    @keyframes pulse {
      0%, 100% { opacity: 1; }
      50% { opacity: 0.7; }
    }

    /* Microsoft注册面板间距控制 */
    #microsoftRegPanel.with-margin {
      margin-top: 12px;
    }


  </style>
</head>
<body>
  <!-- RewardTracker面板 -->
  <div id="rewardTrackerPanel">
    <!-- 标题行 -->
    <div class="header-row">
      <div class="title main-title">RewardTracker</div>
      <span class="indicator active" id="statusIndicator"></span>
    </div>

    <!-- 账号信息 -->
    <div class="info-row account-row" id="accountInfo">
      <div class="row-label">账号</div>
      <div class="account-value" id="currentAccount">等待检测...</div>
    </div>

    <!-- 天数信息 -->
    <div class="info-row days-row" id="daysHighlight">
      <div class="row-label">天数</div>
      <div class="days-progress" id="daysProgress">--/--</div>
    </div>

    <!-- 详细信息 -->
    <div class="info-row details-row" id="searchInfo">
      <div class="row-label">详情</div>
      <div class="details-content" id="searchProgress">连续搜索--/--天</div>
    </div>

    <!-- 按钮区域 -->
    <div class="buttons-row">
      <button class="btn-compact" id="refreshBtn">刷新</button>
      <button class="btn-compact" id="viewLogsBtn">查看</button>
      <button class="btn-compact" id="clearLogsBtn">清除</button>
      <button class="btn-compact" id="openTargetBtn">目标</button>
    </div>

    <div class="logs" id="logsContainer" style="display: none;">
      <div id="logsList"></div>
    </div>
  </div>

  <!-- BiliSearch面板 -->
  <div id="biliSearchPanel">
    <!-- 哔哩搜索标题行 -->
    <div class="header-row">
      <div class="title bili-title">BiliSearch</div>
      <span class="indicator active" id="biliStatusIndicator"></span>
    </div>

    <!-- 页面加载信息 -->
    <div class="info-row bili-page-row" id="biliPageInfo">
      <div class="row-label">页面</div>
      <div class="account-value" id="biliPageStatus">--/--</div>
    </div>

    <!-- 搜索修改信息 -->
    <div class="info-row bili-search-row" id="biliSearchInfo">
      <div class="row-label">修改</div>
      <div class="days-progress" id="biliSearchStatus">--/--</div>
    </div>

    <!-- 搜索信息 -->
    <div class="info-row bili-term-row" id="biliTermInfo">
      <div class="row-label">搜索</div>
      <div class="details-content" id="biliSearchTerm">--/--</div>
    </div>

    <!-- 搜索完成信息 -->
    <div class="info-row bili-complete-row" id="biliCompleteInfo">
      <div class="row-label">完成</div>
      <div class="details-content" id="biliCompleteStatus">--/--</div>
    </div>
  </div>

  <!-- Microsoft注册面板 -->
  <div id="microsoftRegPanel">
    <!-- Microsoft注册标题行 -->
    <div class="header-row">
      <div class="title ms-title">Microsoft注册</div>
      <span class="indicator active" id="msStatusIndicator"></span>
    </div>

    <!-- 当前账号信息 -->
    <div class="info-row ms-account-row" id="msAccountInfo">
      <div class="row-label">账号</div>
      <div class="account-value" id="msCurrentAccount">--/--</div>
    </div>

    <!-- 注册步骤信息 -->
    <div class="info-row ms-step-row" id="msStepInfo">
      <div class="row-label">步骤</div>
      <div class="days-progress" id="msCurrentStep">--/--</div>
    </div>

    <!-- 验证状态信息 -->
    <div class="info-row ms-verify-row" id="msVerifyInfo">
      <div class="row-label">验证</div>
      <div class="details-content" id="msVerifyStatus">--/--</div>
    </div>

    <!-- 注册状态信息 -->
    <div class="info-row ms-status-row" id="msStatusInfo">
      <div class="row-label">状态</div>
      <div class="details-content" id="msRegistrationStatus">--/--</div>
    </div>
  </div>

  <!-- 空状态提示 -->
  <div id="emptyState" style="display: none;">
    <div class="no-data">
      --/--
    </div>
  </div>

  <script src="popup.js"></script>
</body>
</html>
