// RTBS - 简化版内容脚本（用于测试）
console.log('🚀 RTBS简化版内容脚本已加载');

// 基本的页面检测函数
function isTargetPage() {
  const url = window.location.href;
  console.log('🔍 检查目标页面:', url);
  return url.includes('rewards.bing.com') ||
         url.includes('bing.com/rewards') ||
         url.includes('www.bing.com/rewards');
}

function isBiliSearchPage() {
  const url = window.location.href;
  console.log('🔍 检查哔哩搜索页面:', url);
  return url.includes('cn.bing.com/search?q=%E5%93%94%E5%93%A9%E5%93%94%E5%93%A9&qs=n&form=BILREW') ||
         url.includes('cn.bing.com/search?q=哔哩哔哩') ||
         url.includes('cn.bing.com/search?q=%E5%93%94%E5%93%A9%E5%93%94%E5%93%A9');
}

function isMicrosoftSignupPage() {
  const url = window.location.href;
  console.log('🔍 检查Microsoft注册页面:', url);
  return url.includes('signup.live.com/signup');
}

// 简化的页面处理函数
function handleTargetPage() {
  console.log('✅ 处理目标页面');
  // 简单的测试逻辑
  const testDiv = document.createElement('div');
  testDiv.style.cssText = `
    position: fixed;
    top: 10px;
    right: 10px;
    background: #4CAF50;
    color: white;
    padding: 10px;
    border-radius: 4px;
    z-index: 10000;
    font-size: 12px;
  `;
  testDiv.textContent = '✅ RTBS扩展正在运行';
  document.body.appendChild(testDiv);
  
  setTimeout(() => {
    testDiv.remove();
  }, 3000);
}

function handleBiliSearchPage() {
  console.log('✅ 处理哔哩搜索页面');
  handleTargetPage();
}

function handleMicrosoftPage() {
  console.log('✅ 处理Microsoft页面');
  handleTargetPage();
}

// 简化的初始化函数
function initializeSimpleDetection() {
  try {
    console.log('🔧 开始简化版页面检测...');
    console.log('📄 当前页面URL:', window.location.href);
    console.log('📄 页面标题:', document.title);
    
    if (isTargetPage()) {
      console.log('📍 检测到目标页面');
      handleTargetPage();
    } else if (isBiliSearchPage()) {
      console.log('📍 检测到哔哩搜索页面');
      handleBiliSearchPage();
    } else if (isMicrosoftSignupPage()) {
      console.log('📍 检测到Microsoft注册页面');
      handleMicrosoftPage();
    } else {
      console.log('📍 未匹配到特定页面类型');
    }
    
    // 设置全局变量用于测试
    window.rtbsSimpleLoaded = true;
    window.rtbsVersion = 'simple-test';
    
    console.log('✅ 简化版检测完成');
  } catch (error) {
    console.error('🚨 简化版检测失败:', error);
  }
}

// 页面加载检测
console.log('📄 页面状态:', document.readyState);

if (document.readyState === 'loading') {
  console.log('⏳ 等待DOM加载完成...');
  document.addEventListener('DOMContentLoaded', () => {
    console.log('✅ DOM加载完成');
    setTimeout(initializeSimpleDetection, 100);
  });
} else {
  console.log('✅ DOM已就绪');
  setTimeout(initializeSimpleDetection, 100);
}

// 监听页面变化
let lastUrl = window.location.href;
setInterval(() => {
  if (window.location.href !== lastUrl) {
    console.log('🔄 页面URL发生变化:', {
      from: lastUrl,
      to: window.location.href
    });
    lastUrl = window.location.href;
    setTimeout(initializeSimpleDetection, 500);
  }
}, 1000);

console.log('🎯 简化版内容脚本初始化完成');
