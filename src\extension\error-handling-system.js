/**
 * 错误处理和重试系统
 * 智能的错误恢复和重试机制
 * 
 * <AUTHOR> Team
 * @version 3.0.0
 * @description 实现智能错误处理、重试策略和故障恢复
 */

/**
 * 错误类型定义
 */
const ERROR_TYPES = {
  NETWORK: 'network',
  TIMEOUT: 'timeout',
  IMAP: 'imap',
  PAGE_DETECTION: 'page_detection',
  FORM_FILLING: 'form_filling',
  VERIFICATION: 'verification',
  CAPTCHA: 'captcha',
  SYSTEM: 'system',
  UNKNOWN: 'unknown'
};

/**
 * 错误严重程度
 */
const ERROR_SEVERITY = {
  LOW: 'low',       // 可以忽略或简单重试
  MEDIUM: 'medium', // 需要重试或降级处理
  HIGH: 'high',     // 需要用户干预或系统重置
  CRITICAL: 'critical' // 系统无法继续运行
};

/**
 * 重试策略类型
 */
const RETRY_STRATEGIES = {
  IMMEDIATE: 'immediate',     // 立即重试
  LINEAR: 'linear',          // 线性延迟重试
  EXPONENTIAL: 'exponential', // 指数退避重试
  CUSTOM: 'custom'           // 自定义重试策略
};

/**
 * 错误分析器
 * 分析错误类型和严重程度
 */
class ErrorAnalyzer {
  constructor() {
    this.errorPatterns = this.initializeErrorPatterns();
  }

  /**
   * 初始化错误模式
   */
  initializeErrorPatterns() {
    return {
      [ERROR_TYPES.NETWORK]: {
        patterns: [
          /network/i,
          /connection/i,
          /fetch/i,
          /xhr/i,
          /timeout/i,
          /offline/i
        ],
        severity: ERROR_SEVERITY.MEDIUM
      },
      
      [ERROR_TYPES.TIMEOUT]: {
        patterns: [
          /timeout/i,
          /超时/i,
          /time.*out/i,
          /deadline/i
        ],
        severity: ERROR_SEVERITY.MEDIUM
      },
      
      [ERROR_TYPES.IMAP]: {
        patterns: [
          /imap/i,
          /邮件/i,
          /email/i,
          /mail/i,
          /verification.*code/i,
          /验证码/i
        ],
        severity: ERROR_SEVERITY.HIGH
      },
      
      [ERROR_TYPES.PAGE_DETECTION]: {
        patterns: [
          /page.*detect/i,
          /页面.*检测/i,
          /element.*not.*found/i,
          /selector/i,
          /querySelector/i
        ],
        severity: ERROR_SEVERITY.MEDIUM
      },
      
      [ERROR_TYPES.FORM_FILLING]: {
        patterns: [
          /form/i,
          /input/i,
          /表单/i,
          /填写/i,
          /click/i,
          /submit/i
        ],
        severity: ERROR_SEVERITY.MEDIUM
      },
      
      [ERROR_TYPES.VERIFICATION]: {
        patterns: [
          /verification/i,
          /verify/i,
          /验证/i,
          /code/i,
          /captcha/i
        ],
        severity: ERROR_SEVERITY.HIGH
      },
      
      [ERROR_TYPES.CAPTCHA]: {
        patterns: [
          /captcha/i,
          /人机验证/i,
          /hold/i,
          /按住/i,
          /robot/i
        ],
        severity: ERROR_SEVERITY.HIGH
      },
      
      [ERROR_TYPES.SYSTEM]: {
        patterns: [
          /system/i,
          /系统/i,
          /state.*machine/i,
          /状态机/i,
          /controller/i
        ],
        severity: ERROR_SEVERITY.CRITICAL
      }
    };
  }

  /**
   * 分析错误
   * @param {Error|string} error - 错误对象或错误消息
   * @returns {Object} 错误分析结果
   */
  analyzeError(error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    const stack = error instanceof Error ? error.stack : '';
    
    // 检测错误类型
    const errorType = this.detectErrorType(errorMessage, stack);
    
    // 确定严重程度
    const severity = this.determineSeverity(errorType, errorMessage);
    
    // 生成错误ID
    const errorId = this.generateErrorId(errorType, errorMessage);
    
    return {
      id: errorId,
      type: errorType,
      severity,
      message: errorMessage,
      stack,
      timestamp: new Date().toISOString(),
      context: this.extractContext(error)
    };
  }

  /**
   * 检测错误类型
   * @param {string} message - 错误消息
   * @param {string} stack - 错误堆栈
   * @returns {string} 错误类型
   */
  detectErrorType(message, stack) {
    const fullText = (message + ' ' + stack).toLowerCase();
    
    for (const [type, config] of Object.entries(this.errorPatterns)) {
      for (const pattern of config.patterns) {
        if (pattern.test(fullText)) {
          return type;
        }
      }
    }
    
    return ERROR_TYPES.UNKNOWN;
  }

  /**
   * 确定错误严重程度
   * @param {string} errorType - 错误类型
   * @param {string} message - 错误消息
   * @returns {string} 严重程度
   */
  determineSeverity(errorType, message) {
    const baseConfig = this.errorPatterns[errorType];
    if (baseConfig) {
      return baseConfig.severity;
    }
    
    // 根据消息内容调整严重程度
    if (message.includes('critical') || message.includes('fatal')) {
      return ERROR_SEVERITY.CRITICAL;
    }
    
    return ERROR_SEVERITY.LOW;
  }

  /**
   * 生成错误ID
   * @param {string} type - 错误类型
   * @param {string} message - 错误消息
   * @returns {string} 错误ID
   */
  generateErrorId(type, message) {
    const hash = this.simpleHash(message);
    return `${type}_${hash}_${Date.now()}`;
  }

  /**
   * 简单哈希函数
   * @param {string} str - 输入字符串
   * @returns {string} 哈希值
   */
  simpleHash(str) {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // 转换为32位整数
    }
    return Math.abs(hash).toString(16);
  }

  /**
   * 提取错误上下文
   * @param {Error} error - 错误对象
   * @returns {Object} 上下文信息
   */
  extractContext(error) {
    return {
      url: location.href,
      userAgent: navigator.userAgent,
      timestamp: Date.now(),
      errorName: error.name,
      errorConstructor: error.constructor.name
    };
  }
}

/**
 * 重试策略管理器
 * 管理不同类型的重试策略
 */
class RetryStrategyManager {
  constructor() {
    this.strategies = this.initializeStrategies();
  }

  /**
   * 初始化重试策略
   */
  initializeStrategies() {
    return {
      [ERROR_TYPES.NETWORK]: {
        strategy: RETRY_STRATEGIES.EXPONENTIAL,
        maxAttempts: 5,
        baseDelay: 1000,
        maxDelay: 30000,
        backoffFactor: 2
      },
      
      [ERROR_TYPES.TIMEOUT]: {
        strategy: RETRY_STRATEGIES.LINEAR,
        maxAttempts: 3,
        baseDelay: 2000,
        maxDelay: 10000
      },
      
      [ERROR_TYPES.IMAP]: {
        strategy: RETRY_STRATEGIES.EXPONENTIAL,
        maxAttempts: 4,
        baseDelay: 3000,
        maxDelay: 20000,
        backoffFactor: 1.5
      },
      
      [ERROR_TYPES.PAGE_DETECTION]: {
        strategy: RETRY_STRATEGIES.LINEAR,
        maxAttempts: 3,
        baseDelay: 1000,
        maxDelay: 5000
      },
      
      [ERROR_TYPES.FORM_FILLING]: {
        strategy: RETRY_STRATEGIES.LINEAR,
        maxAttempts: 3,
        baseDelay: 500,
        maxDelay: 2000
      },
      
      [ERROR_TYPES.VERIFICATION]: {
        strategy: RETRY_STRATEGIES.EXPONENTIAL,
        maxAttempts: 3,
        baseDelay: 5000,
        maxDelay: 15000,
        backoffFactor: 1.5
      },
      
      [ERROR_TYPES.CAPTCHA]: {
        strategy: RETRY_STRATEGIES.LINEAR,
        maxAttempts: 3,
        baseDelay: 3000,
        maxDelay: 10000
      },
      
      [ERROR_TYPES.SYSTEM]: {
        strategy: RETRY_STRATEGIES.IMMEDIATE,
        maxAttempts: 1,
        baseDelay: 0
      },
      
      [ERROR_TYPES.UNKNOWN]: {
        strategy: RETRY_STRATEGIES.LINEAR,
        maxAttempts: 2,
        baseDelay: 1000,
        maxDelay: 5000
      }
    };
  }

  /**
   * 获取重试策略
   * @param {string} errorType - 错误类型
   * @returns {Object} 重试策略配置
   */
  getRetryStrategy(errorType) {
    return this.strategies[errorType] || this.strategies[ERROR_TYPES.UNKNOWN];
  }

  /**
   * 计算重试延迟
   * @param {Object} strategy - 重试策略
   * @param {number} attempt - 当前尝试次数
   * @returns {number} 延迟时间（毫秒）
   */
  calculateDelay(strategy, attempt) {
    switch (strategy.strategy) {
      case RETRY_STRATEGIES.IMMEDIATE:
        return 0;
        
      case RETRY_STRATEGIES.LINEAR:
        return Math.min(
          strategy.baseDelay * attempt,
          strategy.maxDelay || strategy.baseDelay * 10
        );
        
      case RETRY_STRATEGIES.EXPONENTIAL:
        const backoffFactor = strategy.backoffFactor || 2;
        return Math.min(
          strategy.baseDelay * Math.pow(backoffFactor, attempt - 1),
          strategy.maxDelay || strategy.baseDelay * 32
        );
        
      default:
        return strategy.baseDelay || 1000;
    }
  }

  /**
   * 检查是否应该重试
   * @param {Object} strategy - 重试策略
   * @param {number} attempt - 当前尝试次数
   * @param {string} severity - 错误严重程度
   * @returns {boolean} 是否应该重试
   */
  shouldRetry(strategy, attempt, severity) {
    // 严重错误不重试
    if (severity === ERROR_SEVERITY.CRITICAL) {
      return false;
    }
    
    // 检查最大尝试次数
    return attempt < strategy.maxAttempts;
  }
}

/**
 * 错误恢复管理器
 * 实现智能的错误恢复策略
 */
class ErrorRecoveryManager {
  constructor() {
    this.recoveryActions = this.initializeRecoveryActions();
  }

  /**
   * 初始化恢复动作
   */
  initializeRecoveryActions() {
    return {
      [ERROR_TYPES.NETWORK]: [
        () => this.checkNetworkConnection(),
        () => this.refreshPage(),
        () => this.clearCache()
      ],
      
      [ERROR_TYPES.TIMEOUT]: [
        () => this.increaseTimeout(),
        () => this.refreshPage()
      ],
      
      [ERROR_TYPES.IMAP]: [
        () => this.resetImapConnection(),
        () => this.clearEmailCache(),
        () => this.switchImapServer()
      ],
      
      [ERROR_TYPES.PAGE_DETECTION]: [
        () => this.refreshPage(),
        () => this.waitForPageLoad(),
        () => this.scrollPage()
      ],
      
      [ERROR_TYPES.FORM_FILLING]: [
        () => this.refocusElement(),
        () => this.clearFormData(),
        () => this.refreshPage()
      ],
      
      [ERROR_TYPES.VERIFICATION]: [
        () => this.clearVerificationCache(),
        () => this.requestNewCode(),
        () => this.resetVerificationFlow()
      ],
      
      [ERROR_TYPES.CAPTCHA]: [
        () => this.refreshCaptcha(),
        () => this.waitLonger(),
        () => this.resetCaptchaFlow()
      ],
      
      [ERROR_TYPES.SYSTEM]: [
        () => this.resetStateMachine(),
        () => this.reinitializeSystem()
      ]
    };
  }

  /**
   * 执行恢复动作
   * @param {string} errorType - 错误类型
   * @param {number} attempt - 尝试次数
   * @returns {Promise<boolean>} 恢复是否成功
   */
  async executeRecovery(errorType, attempt) {
    const actions = this.recoveryActions[errorType] || [];
    
    if (attempt > actions.length) {
      console.log('❌ 没有更多恢复动作可执行');
      return false;
    }
    
    const action = actions[attempt - 1];
    if (!action) {
      return false;
    }
    
    try {
      console.log(`🔧 执行恢复动作 ${attempt}: ${action.name}`);
      const result = await action();
      console.log(`✅ 恢复动作执行${result ? '成功' : '失败'}`);
      return result;
    } catch (error) {
      console.error('❌ 恢复动作执行异常:', error);
      return false;
    }
  }

  // 恢复动作实现
  async checkNetworkConnection() {
    try {
      const response = await fetch('https://www.google.com/favicon.ico', {
        method: 'HEAD',
        mode: 'no-cors',
        cache: 'no-cache'
      });
      return true;
    } catch {
      return false;
    }
  }

  async refreshPage() {
    location.reload();
    return true;
  }

  async clearCache() {
    if ('caches' in window) {
      const cacheNames = await caches.keys();
      await Promise.all(cacheNames.map(name => caches.delete(name)));
    }
    return true;
  }

  async increaseTimeout() {
    // 这里可以增加全局超时设置
    return true;
  }

  async resetImapConnection() {
    // 重置IMAP连接
    return true;
  }

  async clearEmailCache() {
    // 清除邮件缓存
    return true;
  }

  async switchImapServer() {
    // 切换IMAP服务器
    return true;
  }

  async waitForPageLoad() {
    return new Promise(resolve => {
      if (document.readyState === 'complete') {
        resolve(true);
      } else {
        window.addEventListener('load', () => resolve(true), { once: true });
        setTimeout(() => resolve(false), 10000); // 10秒超时
      }
    });
  }

  async scrollPage() {
    window.scrollTo(0, document.body.scrollHeight);
    await new Promise(resolve => setTimeout(resolve, 1000));
    return true;
  }

  async refocusElement() {
    const activeElement = document.activeElement;
    if (activeElement && activeElement !== document.body) {
      activeElement.blur();
      await new Promise(resolve => setTimeout(resolve, 100));
      activeElement.focus();
    }
    return true;
  }

  async clearFormData() {
    const inputs = document.querySelectorAll('input, textarea, select');
    inputs.forEach(input => {
      if (input.type !== 'submit' && input.type !== 'button') {
        input.value = '';
      }
    });
    return true;
  }

  async clearVerificationCache() {
    // 清除验证码缓存
    return true;
  }

  async requestNewCode() {
    // 请求新的验证码
    return true;
  }

  async resetVerificationFlow() {
    // 重置验证流程
    return true;
  }

  async refreshCaptcha() {
    const captchaElements = document.querySelectorAll('[data-testid*="captcha"], [id*="captcha"]');
    captchaElements.forEach(element => {
      if (element.click) element.click();
    });
    return true;
  }

  async waitLonger() {
    await new Promise(resolve => setTimeout(resolve, 5000));
    return true;
  }

  async resetCaptchaFlow() {
    // 重置验证码流程
    return true;
  }

  async resetStateMachine() {
    // 重置状态机
    return true;
  }

  async reinitializeSystem() {
    // 重新初始化系统
    return true;
  }
}

/**
 * 统一错误处理器
 * 集成所有错误处理组件
 */
class UnifiedErrorHandler {
  constructor() {
    this.analyzer = new ErrorAnalyzer();
    this.retryManager = new RetryStrategyManager();
    this.recoveryManager = new ErrorRecoveryManager();
    this.errorHistory = [];
    this.maxHistorySize = 100;
  }

  /**
   * 处理错误
   * @param {Error|string} error - 错误对象或消息
   * @param {Object} context - 错误上下文
   * @returns {Promise<Object>} 处理结果
   */
  async handleError(error, context = {}) {
    console.log('🚨 开始处理错误:', error);

    // 分析错误
    const analysis = this.analyzer.analyzeError(error);
    console.log('📊 错误分析结果:', analysis);

    // 记录错误历史
    this.recordError(analysis, context);

    // 获取重试策略
    const retryStrategy = this.retryManager.getRetryStrategy(analysis.type);

    // 检查是否应该重试
    const currentAttempt = context.attempt || 1;
    const shouldRetry = this.retryManager.shouldRetry(
      retryStrategy,
      currentAttempt,
      analysis.severity
    );

    if (!shouldRetry) {
      console.log('❌ 不应该重试，错误处理结束');
      return {
        success: false,
        analysis,
        action: 'no_retry',
        reason: '达到最大重试次数或错误过于严重'
      };
    }

    // 计算重试延迟
    const delay = this.retryManager.calculateDelay(retryStrategy, currentAttempt);

    console.log(`🔄 准备重试 (第${currentAttempt}次)，延迟: ${delay}ms`);

    // 执行恢复动作
    const recoverySuccess = await this.recoveryManager.executeRecovery(
      analysis.type,
      currentAttempt
    );

    // 等待重试延迟
    if (delay > 0) {
      await this.wait(delay);
    }

    return {
      success: true,
      analysis,
      action: 'retry',
      delay,
      recoverySuccess,
      nextAttempt: currentAttempt + 1,
      maxAttempts: retryStrategy.maxAttempts
    };
  }

  /**
   * 记录错误历史
   * @param {Object} analysis - 错误分析结果
   * @param {Object} context - 错误上下文
   */
  recordError(analysis, context) {
    this.errorHistory.push({
      ...analysis,
      context,
      handledAt: new Date().toISOString()
    });

    // 限制历史记录大小
    if (this.errorHistory.length > this.maxHistorySize) {
      this.errorHistory.shift();
    }
  }

  /**
   * 获取错误统计
   * @returns {Object} 错误统计信息
   */
  getErrorStatistics() {
    const stats = {
      total: this.errorHistory.length,
      byType: {},
      bySeverity: {},
      recent: this.errorHistory.slice(-10)
    };

    this.errorHistory.forEach(error => {
      // 按类型统计
      stats.byType[error.type] = (stats.byType[error.type] || 0) + 1;

      // 按严重程度统计
      stats.bySeverity[error.severity] = (stats.bySeverity[error.severity] || 0) + 1;
    });

    return stats;
  }

  /**
   * 清除错误历史
   */
  clearErrorHistory() {
    this.errorHistory = [];
    console.log('🧹 错误历史已清除');
  }

  /**
   * 等待指定时间
   * @param {number} ms - 等待毫秒数
   */
  async wait(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 检查系统健康状态
   * @returns {Object} 健康状态报告
   */
  getHealthStatus() {
    const recentErrors = this.errorHistory.slice(-10);
    const criticalErrors = recentErrors.filter(e => e.severity === ERROR_SEVERITY.CRITICAL);
    const highErrors = recentErrors.filter(e => e.severity === ERROR_SEVERITY.HIGH);

    let status = 'healthy';
    let message = '系统运行正常';

    if (criticalErrors.length > 0) {
      status = 'critical';
      message = `发现 ${criticalErrors.length} 个严重错误`;
    } else if (highErrors.length >= 3) {
      status = 'warning';
      message = `发现 ${highErrors.length} 个高级错误`;
    } else if (recentErrors.length >= 5) {
      status = 'degraded';
      message = `最近发生 ${recentErrors.length} 个错误`;
    }

    return {
      status,
      message,
      errorCount: this.errorHistory.length,
      recentErrorCount: recentErrors.length,
      criticalErrorCount: criticalErrors.length,
      timestamp: new Date().toISOString()
    };
  }
}

// 导出错误处理系统
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    ERROR_TYPES,
    ERROR_SEVERITY,
    RETRY_STRATEGIES,
    ErrorAnalyzer,
    RetryStrategyManager,
    ErrorRecoveryManager,
    UnifiedErrorHandler
  };
} else {
  // 浏览器环境
  window.ErrorHandlingSystem = {
    ERROR_TYPES,
    ERROR_SEVERITY,
    RETRY_STRATEGIES,
    ErrorAnalyzer,
    RetryStrategyManager,
    ErrorRecoveryManager,
    UnifiedErrorHandler
  };
}
