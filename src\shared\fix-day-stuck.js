// 快速修复日期选择卡住问题的脚本

console.log('🔧 日期选择卡住问题修复脚本');

// 强制重置下拉框状态
function forceResetDropdowns() {
  console.log('\n🔄 强制重置下拉框状态:');
  
  // 查找所有下拉框
  const dropdowns = document.querySelectorAll('button[name="BirthMonth"], button[name="BirthDay"]');
  
  dropdowns.forEach(dropdown => {
    const name = dropdown.getAttribute('name');
    console.log(`重置 ${name} 下拉框`);
    
    // 设置为未展开状态
    dropdown.setAttribute('aria-expanded', 'false');
    
    // 移除焦点
    dropdown.blur();
    
    // 触发失焦事件
    dropdown.dispatchEvent(new Event('blur', { bubbles: true }));
  });
  
  // 清除可能存在的选项面板
  const optionPanels = document.querySelectorAll('[role="listbox"], .dropdown-options, .fui-Dropdown__listbox');
  optionPanels.forEach(panel => {
    if (panel.style) {
      panel.style.display = 'none';
    }
  });
  
  console.log('✓ 下拉框状态重置完成');
}

// 强制选择日期（多种方法）
function forceSelectDay(targetDay = 15) {
  console.log(`\n💪 强制选择日期: ${targetDay}日`);
  
  const dayDropdown = document.querySelector('button[name="BirthDay"]');
  if (!dayDropdown) {
    console.log('❌ 未找到日期下拉框');
    return false;
  }
  
  // 方法1: 直接设置值（如果支持）
  console.log('尝试方法1: 直接设置值');
  try {
    dayDropdown.setAttribute('value', targetDay);
    dayDropdown.textContent = `${targetDay}日`;
    
    // 触发变化事件
    dayDropdown.dispatchEvent(new Event('change', { bubbles: true }));
    dayDropdown.dispatchEvent(new Event('input', { bubbles: true }));
    
    console.log('✓ 方法1成功');
    return true;
  } catch (error) {
    console.log('❌ 方法1失败:', error.message);
  }
  
  // 方法2: 模拟完整的用户交互
  console.log('尝试方法2: 模拟用户交互');
  try {
    // 先重置状态
    forceResetDropdowns();
    
    setTimeout(() => {
      // 点击下拉框
      dayDropdown.click();
      
      setTimeout(() => {
        // 查找选项
        const options = document.querySelectorAll('[role="option"]');
        console.log(`找到 ${options.length} 个选项`);
        
        if (options.length > 0) {
          let targetOption = null;
          
          // 查找目标选项
          for (const option of options) {
            const text = option.textContent.trim();
            if (text.includes(`${targetDay}日`) || text === targetDay.toString()) {
              targetOption = option;
              break;
            }
          }
          
          // 如果找不到目标，选择最接近的
          if (!targetOption) {
            const availableDays = Array.from(options).map(opt => {
              const match = opt.textContent.match(/(\d+)/);
              return match ? parseInt(match[1]) : 0;
            }).filter(day => day > 0);
            
            const closestDay = availableDays.reduce((prev, curr) => 
              Math.abs(curr - targetDay) < Math.abs(prev - targetDay) ? curr : prev
            );
            
            targetOption = Array.from(options).find(opt => 
              opt.textContent.includes(closestDay.toString())
            );
            
            console.log(`目标日期不可用，选择最接近的: ${closestDay}日`);
          }
          
          if (targetOption) {
            targetOption.click();
            console.log('✓ 方法2成功');
            return true;
          }
        }
        
        console.log('❌ 方法2失败: 无可用选项');
        return false;
      }, 1000);
    }, 500);
  } catch (error) {
    console.log('❌ 方法2失败:', error.message);
  }
  
  return false;
}

// 检查并修复卡住状态
function checkAndFixStuckState() {
  console.log('\n🔍 检查并修复卡住状态:');
  
  const monthDropdown = document.querySelector('button[name="BirthMonth"]');
  const dayDropdown = document.querySelector('button[name="BirthDay"]');
  
  if (!monthDropdown || !dayDropdown) {
    console.log('❌ 未找到必要的下拉框');
    return;
  }
  
  // 检查月份状态
  const monthValue = monthDropdown.textContent.trim();
  const monthSelected = monthValue && !monthValue.includes('月份') && monthValue !== '';
  
  // 检查日期状态
  const dayValue = dayDropdown.textContent.trim();
  const daySelected = dayValue && dayValue.includes('日') && dayValue !== '';
  
  console.log('当前状态:');
  console.log(`  月份: ${monthSelected ? '已选择' : '未选择'} (${monthValue})`);
  console.log(`  日期: ${daySelected ? '已选择' : '未选择'} (${dayValue})`);
  
  // 检查是否有下拉框处于展开状态
  const monthExpanded = monthDropdown.getAttribute('aria-expanded') === 'true';
  const dayExpanded = dayDropdown.getAttribute('aria-expanded') === 'true';
  
  console.log('展开状态:');
  console.log(`  月份下拉框: ${monthExpanded ? '展开' : '收起'}`);
  console.log(`  日期下拉框: ${dayExpanded ? '展开' : '收起'}`);
  
  // 如果日期下拉框展开但没有选项，说明卡住了
  if (dayExpanded) {
    const options = document.querySelectorAll('[role="option"]');
    if (options.length === 0) {
      console.log('⚠️ 检测到卡住状态: 日期下拉框展开但无选项');
      console.log('执行修复...');
      
      forceResetDropdowns();
      
      setTimeout(() => {
        forceSelectDay(15);
      }, 1000);
    }
  }
  
  // 如果月份已选择但日期未选择，尝试选择日期
  if (monthSelected && !daySelected) {
    console.log('月份已选择，尝试选择日期...');
    setTimeout(() => {
      forceSelectDay(15);
    }, 500);
  }
}

// 完整的修复流程
function runCompleteFixFlow(targetMonth = 8, targetDay = 15) {
  console.log(`\n🚀 运行完整修复流程: ${targetMonth}月${targetDay}日`);
  
  // 步骤1: 重置状态
  forceResetDropdowns();
  
  // 步骤2: 选择月份（如果需要）
  setTimeout(() => {
    const monthDropdown = document.querySelector('button[name="BirthMonth"]');
    const monthValue = monthDropdown ? monthDropdown.textContent.trim() : '';
    
    if (!monthValue || monthValue.includes('月份')) {
      console.log('选择月份...');
      monthDropdown.click();
      
      setTimeout(() => {
        const monthOptions = document.querySelectorAll('[role="option"]');
        for (const option of monthOptions) {
          if (option.textContent.includes(`${targetMonth}月`)) {
            option.click();
            console.log(`✓ 已选择月份: ${targetMonth}月`);
            break;
          }
        }
        
        // 等待月份选择完成后选择日期
        setTimeout(() => {
          forceSelectDay(targetDay);
        }, 2000);
      }, 1000);
    } else {
      console.log('月份已选择，直接选择日期');
      forceSelectDay(targetDay);
    }
  }, 1000);
}

// 导出修复函数
window.fixDayStuck = {
  reset: forceResetDropdowns,
  forceSelect: forceSelectDay,
  checkAndFix: checkAndFixStuckState,
  runComplete: runCompleteFixFlow
};

console.log('\n=== 修复脚本加载完成 ===');
console.log('快速修复函数:');
console.log('- fixDayStuck.reset() - 重置下拉框状态');
console.log('- fixDayStuck.forceSelect(15) - 强制选择日期');
console.log('- fixDayStuck.checkAndFix() - 检查并修复卡住状态');
console.log('- fixDayStuck.runComplete(8, 15) - 完整修复流程');
console.log('\n推荐使用: fixDayStuck.runComplete(8, 15)');
