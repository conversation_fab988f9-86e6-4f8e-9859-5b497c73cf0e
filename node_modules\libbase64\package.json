{"name": "libbase64", "version": "1.2.1", "description": "Encode and decode base64 encoded strings", "main": "lib/libbase64.js", "scripts": {"test": "grunt"}, "repository": {"type": "git", "url": "git://github.com/nodemailer/libbase64.git"}, "keywords": ["base64", "mime"], "author": "<PERSON><PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/nodemailer/libbase64/issues"}, "homepage": "https://github.com/nodemailer/libbase64", "devDependencies": {"chai": "4.2.0", "eslint-config-nodemailer": "1.2.0", "eslint-config-prettier": "6.0.0", "grunt": "1.0.4", "grunt-cli": "1.3.2", "grunt-eslint": "22.0.0", "grunt-mocha-test": "0.13.3", "mocha": "6.2.0"}, "dependencies": {}}