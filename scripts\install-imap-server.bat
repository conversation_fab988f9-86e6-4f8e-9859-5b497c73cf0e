@echo off
title RTBS IMAP Server Installation

echo ========================================
echo    RTBS IMAP Server Installation Script
echo ========================================
echo.

echo [1/6] Checking Node.js environment...
call node --version >nul 2>&1
if errorlevel 1 (
    echo Error: Node.js not detected
    echo Please install Node.js 14.0 or higher first
    echo Download from: https://nodejs.org/
    pause
    exit /b 1
)
echo Node.js detected successfully

echo [2/6] Checking npm package manager...
call npm --version >nul 2>&1
if errorlevel 1 (
    echo Error: npm is not available
    pause
    exit /b 1
)
echo npm detected successfully

echo [3/6] Installing Node.js dependencies...
echo Installing: express, imap, mailparser, cors...
call npm install express@^4.18.2 imap@^0.8.19 mailparser@^3.6.5 cors@^2.8.5
if errorlevel 1 (
    echo Error: Failed to install dependencies
    pause
    exit /b 1
)
echo Dependencies installed successfully

echo [4/6] Installing development dependencies...
call npm install --save-dev nodemon@^3.0.2
if errorlevel 1 (
    echo Warning: Failed to install dev dependencies, but normal usage is not affected
)
echo Development dependencies installed successfully

echo [5/6] Checking configuration file...
if not exist "config\imap-config.json" (
    echo Error: Cannot find config\imap-config.json configuration file
    echo Please ensure the configuration file exists and contains correct IMAP settings
    pause
    exit /b 1
)
echo Configuration file exists

echo [6/6] Testing server startup...
echo Starting IMAP server for testing...
timeout /t 2 /nobreak >nul

start /b node src/server/imap-server.js
timeout /t 5 /nobreak >nul

curl -s http://localhost:3000/health >nul 2>&1
if errorlevel 1 (
    echo Server startup test failed
    echo Please check configuration file and network connection
) else (
    echo Server started successfully
    echo.
    echo ========================================
    echo           Installation Complete!
    echo ========================================
    echo.
    echo IMAP server has been successfully installed and started
    echo Service address: http://localhost:3000
    echo Health check: http://localhost:3000/health
    echo.
    echo Available commands:
    echo   npm start     - Start server
    echo   npm run dev   - Start in development mode
    echo   npm test      - Run tests
    echo.
    echo Next steps:
    echo 1. Edit config\imap-config.json to configure real email password
    echo 2. Ensure s4464.cfd domain email service is working properly
    echo 3. Run npm test to verify configuration
)

echo Stopping test server...
taskkill /f /im node.exe >nul 2>&1

echo.
echo ========================================
echo Installation script completed
echo ========================================
echo Press any key to exit...
pause
