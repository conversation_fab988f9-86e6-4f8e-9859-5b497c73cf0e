/**
 * 测试邮件验证逻辑修复效果
 */

// 模拟 IMAP 服务器类的相关方法
class TestImapServer {
  // 计算邮件与目标邮箱的相关性评分（修复版本）
  getEmailRelevanceScore(emailData, targetEmail) {
    let score = 0;
    const targetEmailLower = targetEmail.toLowerCase();
    const targetUsername = targetEmail.split('@')[0].toLowerCase();
    const targetDomain = targetEmail.split('@')[1].toLowerCase();

    console.log(`🔍 计算邮件相关性评分 - 目标邮箱: ${targetEmail}`);

    // 检查收件人字段 (最高权重) - 必须精确匹配邮箱地址
    if (emailData.to) {
      const toLower = emailData.to.toLowerCase();
      
      // 精确匹配完整邮箱地址
      if (toLower.includes(targetEmailLower)) {
        score += 50;
        console.log(`✅ 收件人匹配完整邮箱 (+50): ${emailData.to}`);
      } 
      // 用户名匹配需要更严格的条件：必须是独立的词，且同域名
      else if (toLower.includes(targetUsername) && toLower.includes(targetDomain)) {
        // 检查用户名是否作为独立词出现（前后有分隔符）
        const usernameRegex = new RegExp(`\\b${targetUsername.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}\\b`);
        if (usernameRegex.test(toLower)) {
          score += 25; // 降低分数，因为不是完整匹配
          console.log(`✅ 收件人匹配用户名且同域 (+25): ${emailData.to}`);
        }
      }
    }

    // 检查抄送字段 - 同样严格匹配
    if (emailData.cc) {
      const ccLower = emailData.cc.toLowerCase();
      if (ccLower.includes(targetEmailLower)) {
        score += 40;
        console.log(`✅ 抄送匹配完整邮箱 (+40): ${emailData.cc}`);
      } else if (ccLower.includes(targetUsername) && ccLower.includes(targetDomain)) {
        const usernameRegex = new RegExp(`\\b${targetUsername.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}\\b`);
        if (usernameRegex.test(ccLower)) {
          score += 20; // 降低分数
          console.log(`✅ 抄送匹配用户名且同域 (+20): ${emailData.cc}`);
        }
      }
    }

    // 检查邮件内容 (中等权重) - 完整邮箱地址优先
    const allContent = ((emailData.text || '') + ' ' + (emailData.html || '')).toLowerCase();
    if (allContent.includes(targetEmailLower)) {
      score += 30; // 提高完整邮箱匹配的分数
      console.log(`✅ 邮件内容匹配完整邮箱 (+30)`);
    } else if (allContent.includes(targetUsername)) {
      // 用户名匹配分数大幅降低，避免误匹配
      score += 5;
      console.log(`✅ 邮件内容匹配用户名 (+5)`);
    }

    // 检查邮件主题 (中等权重)
    if (emailData.subject) {
      const subjectLower = emailData.subject.toLowerCase();
      if (subjectLower.includes(targetEmailLower)) {
        score += 15;
        console.log(`✅ 主题匹配完整邮箱 (+15): ${emailData.subject}`);
      } else if (subjectLower.includes(targetUsername)) {
        score += 3; // 大幅降低用户名匹配分数
        console.log(`✅ 主题匹配用户名 (+3): ${emailData.subject}`);
      }
    }

    // 检查发件人域名 (低权重，用于Microsoft等官方邮件)
    if (emailData.from) {
      const fromLower = emailData.from.toLowerCase();
      const officialDomains = ['microsoft.com', 'outlook.com', 'live.com', 'hotmail.com'];

      for (const domain of officialDomains) {
        if (fromLower.includes(domain)) {
          score += 5;
          console.log(`✅ 发件人为官方域名 (+5): ${domain}`);
          break;
        }
      }
    }

    // 检查是否是验证码相关的邮件 (额外加分)
    const verificationKeywords = ['verification', 'verify', '验证', '验证码', 'code', 'confirm', '确认', 'security code', '安全代码'];
    const subjectAndContent = ((emailData.subject || '') + ' ' + allContent).toLowerCase();

    for (const keyword of verificationKeywords) {
      if (subjectAndContent.includes(keyword)) {
        score += 3;
        console.log(`✅ 包含验证相关关键词 (+3): ${keyword}`);
        break; // 只加一次分
      }
    }

    console.log(`📊 最终相关性评分: ${score}`);
    return score;
  }

  // 验证验证码是否属于请求的账号（修复版本）
  validateCodeForAccount(codeResult, requestedEmail) {
    if (!codeResult || !codeResult.email) {
      console.log('⚠️ 验证码结果缺少邮件信息');
      return false;
    }

    // 检查邮件的相关性评分
    const relevanceScore = codeResult.relevanceScore || 0;
    console.log(`🔍 验证码账号匹配检查: 请求邮箱=${requestedEmail}, 相关性评分=${relevanceScore}`);

    // 提高验证阈值：相关性评分大于等于40分才认为是匹配的
    // 这确保至少需要完整邮箱地址匹配或高质量的部分匹配
    if (relevanceScore >= 40) {
      console.log('✅ 验证码账号匹配验证通过');
      return true;
    } else {
      console.log(`❌ 验证码账号匹配验证失败，相关性评分过低 (${relevanceScore} < 40)`);
      
      // 详细记录邮件信息用于调试
      if (codeResult.email) {
        console.log(`📧 邮件详情:`);
        console.log(`   收件人: ${codeResult.email.to || 'N/A'}`);
        console.log(`   抄送: ${codeResult.email.cc || 'N/A'}`);
        console.log(`   主题: ${codeResult.email.subject || 'N/A'}`);
        console.log(`   发件人: ${codeResult.email.from || 'N/A'}`);
      }
      
      return false;
    }
  }
}

// 测试用例
function runTests() {
  const server = new TestImapServer();
  
  console.log('='.repeat(80));
  console.log('🧪 开始测试邮件验证逻辑修复效果');
  console.log('='.repeat(80));

  // 测试用例1：正确的邮件（应该通过）
  console.log('\n📋 测试用例1：正确的验证码邮件');
  console.log('-'.repeat(50));
  const correctEmail = {
    to: '<EMAIL>',
    from: '<EMAIL>',
    subject: 'Microsoft account security code',
    text: 'Your security code is: 123456. Use this code to verify your <NAME_EMAIL>',
    html: '<p>Your security code is: <strong>123456</strong></p>'
  };
  
  const score1 = server.getEmailRelevanceScore(correctEmail, '<EMAIL>');
  const result1 = server.validateCodeForAccount({
    email: correctEmail,
    relevanceScore: score1,
    code: '123456'
  }, '<EMAIL>');
  
  console.log(`\n结果: ${result1 ? '✅ 通过' : '❌ 失败'}\n`);

  // 测试用例2：错误的邮件（应该被拒绝）
  console.log('\n📋 测试用例2：其他用户的验证码邮件');
  console.log('-'.repeat(50));
  const wrongEmail = {
    to: '<EMAIL>',
    from: '<EMAIL>',
    subject: 'Gmail verification code',
    text: 'Your verification code is: 654321. Use this code to verify your <NAME_EMAIL>',
    html: '<p>Your verification code is: <strong>654321</strong></p>'
  };
  
  const score2 = server.getEmailRelevanceScore(wrongEmail, '<EMAIL>');
  const result2 = server.validateCodeForAccount({
    email: wrongEmail,
    relevanceScore: score2,
    code: '654321'
  }, '<EMAIL>');
  
  console.log(`\n结果: ${result2 ? '❌ 错误通过' : '✅ 正确拒绝'}\n`);

  // 测试用例3：边界情况 - 用户名相似但域名不同
  console.log('\n📋 测试用例3：用户名相似但域名不同的邮件');
  console.log('-'.repeat(50));
  const similarEmail = {
    to: '<EMAIL>',
    from: '<EMAIL>',
    subject: 'Gmail verification code',
    text: 'Your verification code is: 789012. Use this code to verify your <NAME_EMAIL>',
    html: '<p>Your verification code is: <strong>789012</strong></p>'
  };
  
  const score3 = server.getEmailRelevanceScore(similarEmail, '<EMAIL>');
  const result3 = server.validateCodeForAccount({
    email: similarEmail,
    relevanceScore: score3,
    code: '789012'
  }, '<EMAIL>');
  
  console.log(`\n结果: ${result3 ? '❌ 错误通过' : '✅ 正确拒绝'}\n`);

  console.log('='.repeat(80));
  console.log('🎯 测试总结');
  console.log('='.repeat(80));
  console.log(`测试用例1 (正确邮件): ${result1 ? '✅ 通过' : '❌ 失败'}`);
  console.log(`测试用例2 (错误邮件): ${result2 ? '❌ 错误通过' : '✅ 正确拒绝'}`);
  console.log(`测试用例3 (相似邮件): ${result3 ? '❌ 错误通过' : '✅ 正确拒绝'}`);
  
  const passedTests = [result1, !result2, !result3].filter(Boolean).length;
  console.log(`\n总体结果: ${passedTests}/3 个测试通过`);
  
  if (passedTests === 3) {
    console.log('🎉 所有测试通过！验证逻辑修复成功！');
  } else {
    console.log('⚠️ 部分测试失败，需要进一步调整');
  }
}

// 运行测试
runTests();
