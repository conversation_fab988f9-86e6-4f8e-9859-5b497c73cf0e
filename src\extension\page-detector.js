/**
 * 页面检测器
 * 负责检测当前页面类型和注册阶段
 * 
 * <AUTHOR> Team
 * @version 3.0.0
 * @description 智能页面检测和阶段识别
 */

/**
 * 页面检测器类
 * 通过多种方式检测当前页面的注册阶段
 */
class PageDetector {
  constructor() {
    this.detectionRules = this.initializeDetectionRules();
    this.lastDetectedStage = null;
    this.detectionHistory = [];
    this.maxHistoryLength = 10;
  }

  /**
   * 初始化检测规则
   * 每个阶段都有多个检测规则，提高准确性
   */
  initializeDetectionRules() {
    return {
      [REGISTRATION_STAGES.DATA_PERMISSION]: [
        {
          name: 'accept_button',
          check: () => document.querySelector('button[data-testid="accept-button"]'),
          weight: 10
        },
        {
          name: 'privacy_text',
          check: () => document.body.textContent.includes('数据使用') || 
                      document.body.textContent.includes('privacy'),
          weight: 5
        },
        {
          name: 'accept_elements',
          check: () => document.querySelector('button[id*="accept"], button[class*="accept"]'),
          weight: 8
        }
      ],

      [REGISTRATION_STAGES.EMAIL_INPUT]: [
        {
          name: 'email_input',
          check: () => document.querySelector('input[type="email"]'),
          weight: 10
        },
        {
          name: 'email_placeholder',
          check: () => document.querySelector('input[placeholder*="email"], input[placeholder*="邮箱"]'),
          weight: 8
        },
        {
          name: 'signup_url',
          check: () => location.href.includes('signup'),
          weight: 5
        },
        {
          name: 'email_text',
          check: () => document.body.textContent.includes('邮箱') || 
                      document.body.textContent.includes('email'),
          weight: 3
        }
      ],

      [REGISTRATION_STAGES.EMAIL_VERIFICATION]: [
        {
          name: 'verification_text',
          check: () => document.body.textContent.includes('验证') || 
                      document.body.textContent.includes('verify'),
          weight: 8
        },
        {
          name: 'email_sent_text',
          check: () => document.body.textContent.includes('发送') || 
                      document.body.textContent.includes('sent'),
          weight: 6
        },
        {
          name: 'verification_elements',
          check: () => document.querySelector('[data-testid*="verification"], [id*="verification"]'),
          weight: 7
        },
        {
          name: 'check_email_text',
          check: () => document.body.textContent.includes('检查邮件') || 
                      document.body.textContent.includes('check email'),
          weight: 9
        }
      ],

      [REGISTRATION_STAGES.VERIFICATION_CODE]: [
        {
          name: 'code_inputs',
          check: () => {
            const inputs = document.querySelectorAll('input[type="text"], input[type="number"]');
            return Array.from(inputs).some(input => 
              input.maxLength === 1 || 
              input.pattern === '[0-9]' || 
              input.getAttribute('data-testid')?.includes('code')
            );
          },
          weight: 10
        },
        {
          name: 'six_digit_inputs',
          check: () => {
            const inputs = document.querySelectorAll('input[maxlength="1"]');
            return inputs.length >= 6;
          },
          weight: 9
        },
        {
          name: 'verification_code_text',
          check: () => document.body.textContent.includes('验证码') || 
                      document.body.textContent.includes('verification code'),
          weight: 7
        }
      ],

      [REGISTRATION_STAGES.PERSONAL_INFO]: [
        {
          name: 'birthday_selects',
          check: () => document.querySelector('select[name*="birth"], select[id*="birth"]'),
          weight: 10
        },
        {
          name: 'date_selects',
          check: () => {
            const yearSelect = document.querySelector('select[name*="year"], select[id*="year"]');
            const monthSelect = document.querySelector('select[name*="month"], select[id*="month"]');
            return yearSelect && monthSelect;
          },
          weight: 9
        },
        {
          name: 'birthday_text',
          check: () => document.body.textContent.includes('生日') || 
                      document.body.textContent.includes('birthday'),
          weight: 6
        }
      ],

      [REGISTRATION_STAGES.NAME_INFO]: [
        {
          name: 'name_inputs',
          check: () => {
            const firstName = document.querySelector('input[name*="first"], input[id*="first"]');
            const lastName = document.querySelector('input[name*="last"], input[id*="last"]');
            return firstName && lastName;
          },
          weight: 10
        },
        {
          name: 'name_text',
          check: () => document.body.textContent.includes('姓名') || 
                      document.body.textContent.includes('name'),
          weight: 5
        },
        {
          name: 'first_last_labels',
          check: () => {
            const text = document.body.textContent.toLowerCase();
            return text.includes('first') && text.includes('last');
          },
          weight: 8
        }
      ],

      [REGISTRATION_STAGES.CAPTCHA]: [
        {
          name: 'hold_text',
          check: () => document.body.textContent.includes('按住') || 
                      document.body.textContent.includes('hold'),
          weight: 10
        },
        {
          name: 'captcha_elements',
          check: () => document.querySelector('[data-testid*="captcha"], [id*="captcha"]'),
          weight: 9
        },
        {
          name: 'verify_human_text',
          check: () => document.body.textContent.includes('人机验证') || 
                      document.body.textContent.includes('verify you are human'),
          weight: 8
        },
        {
          name: 'hold_button',
          check: () => document.querySelector('button[aria-label*="hold"]'),
          weight: 9
        }
      ],

      [REGISTRATION_STAGES.LOGIN_COMPLETE]: [
        {
          name: 'skip_button',
          check: () => document.querySelector('button[data-testid*="skip"], button[id*="skip"]'),
          weight: 8
        },
        {
          name: 'welcome_text',
          check: () => document.body.textContent.includes('欢迎') || 
                      document.body.textContent.includes('welcome'),
          weight: 6
        },
        {
          name: 'setup_text',
          check: () => document.body.textContent.includes('设置') || 
                      document.body.textContent.includes('setup'),
          weight: 5
        },
        {
          name: 'complete_url',
          check: () => location.href.includes('complete') || location.href.includes('finish'),
          weight: 7
        }
      ],

      [REGISTRATION_STAGES.REWARDS_WELCOME]: [
        {
          name: 'rewards_url',
          check: () => location.href.includes('rewards') || location.href.includes('microsoft.com'),
          weight: 8
        },
        {
          name: 'rewards_text',
          check: () => document.body.textContent.includes('奖励') || 
                      document.body.textContent.includes('rewards'),
          weight: 7
        },
        {
          name: 'points_text',
          check: () => document.body.textContent.includes('积分') || 
                      document.body.textContent.includes('points'),
          weight: 6
        }
      ]
    };
  }

  /**
   * 检测当前页面的注册阶段
   * @returns {string|null} 检测到的阶段，如果无法确定则返回null
   */
  detectCurrentStage() {
    const scores = {};
    
    // 为每个阶段计算得分
    for (const [stage, rules] of Object.entries(this.detectionRules)) {
      scores[stage] = this.calculateStageScore(rules);
    }

    // 找到得分最高的阶段
    const bestMatch = this.findBestMatch(scores);
    
    // 记录检测历史
    this.recordDetection(bestMatch);
    
    // 验证检测结果
    const validatedStage = this.validateDetection(bestMatch);
    
    if (validatedStage) {
      console.log(`🎯 页面检测结果: ${validatedStage} (得分: ${scores[validatedStage]})`);
      this.lastDetectedStage = validatedStage;
    } else {
      console.log('🔍 未检测到明确的注册阶段');
    }

    return validatedStage;
  }

  /**
   * 计算阶段得分
   * @param {Array} rules - 检测规则数组
   * @returns {number} 总得分
   */
  calculateStageScore(rules) {
    let totalScore = 0;
    
    for (const rule of rules) {
      try {
        if (rule.check()) {
          totalScore += rule.weight;
          console.log(`✅ 规则匹配: ${rule.name} (+${rule.weight})`);
        }
      } catch (error) {
        console.warn(`⚠️ 规则检查失败: ${rule.name}`, error);
      }
    }
    
    return totalScore;
  }

  /**
   * 找到得分最高的阶段
   * @param {Object} scores - 各阶段得分
   * @returns {Object} 最佳匹配结果
   */
  findBestMatch(scores) {
    let bestStage = null;
    let bestScore = 0;
    
    for (const [stage, score] of Object.entries(scores)) {
      if (score > bestScore) {
        bestScore = score;
        bestStage = stage;
      }
    }
    
    return {
      stage: bestStage,
      score: bestScore,
      allScores: scores
    };
  }

  /**
   * 验证检测结果
   * @param {Object} detection - 检测结果
   * @returns {string|null} 验证后的阶段
   */
  validateDetection(detection) {
    const { stage, score } = detection;
    
    // 最低得分阈值
    const minScore = 5;
    if (score < minScore) {
      console.log(`⚠️ 得分过低，忽略检测结果: ${score} < ${minScore}`);
      return null;
    }
    
    // 检查是否与历史检测结果一致
    if (this.isConsistentWithHistory(stage)) {
      return stage;
    }
    
    // 如果不一致，需要更高的得分才能接受
    const highConfidenceScore = 8;
    if (score >= highConfidenceScore) {
      console.log(`🔥 高置信度检测，接受结果: ${stage} (${score})`);
      return stage;
    }
    
    console.log(`⚠️ 检测结果与历史不一致，且置信度不够: ${stage} (${score})`);
    return null;
  }

  /**
   * 检查检测结果是否与历史一致
   * @param {string} stage - 检测到的阶段
   * @returns {boolean} 是否一致
   */
  isConsistentWithHistory(stage) {
    if (this.detectionHistory.length === 0) {
      return true; // 没有历史记录，认为一致
    }
    
    // 检查最近几次检测结果
    const recentDetections = this.detectionHistory.slice(-3);
    const consistentCount = recentDetections.filter(d => d.stage === stage).length;
    
    return consistentCount >= Math.ceil(recentDetections.length / 2);
  }

  /**
   * 记录检测结果
   * @param {Object} detection - 检测结果
   */
  recordDetection(detection) {
    this.detectionHistory.push({
      ...detection,
      timestamp: Date.now(),
      url: location.href
    });
    
    // 限制历史记录长度
    if (this.detectionHistory.length > this.maxHistoryLength) {
      this.detectionHistory.shift();
    }
  }

  /**
   * 获取检测历史
   * @returns {Array} 检测历史数组
   */
  getDetectionHistory() {
    return [...this.detectionHistory];
  }

  /**
   * 清除检测历史
   */
  clearHistory() {
    this.detectionHistory = [];
    this.lastDetectedStage = null;
    console.log('🧹 检测历史已清除');
  }

  /**
   * 强制检测指定阶段
   * @param {string} stage - 要检测的阶段
   * @returns {boolean} 是否检测到该阶段
   */
  forceDetectStage(stage) {
    const rules = this.detectionRules[stage];
    if (!rules) {
      console.error(`❌ 未知的阶段: ${stage}`);
      return false;
    }
    
    const score = this.calculateStageScore(rules);
    console.log(`🔍 强制检测 ${stage}: 得分 ${score}`);
    
    return score > 0;
  }
}

// 导出页面检测器
if (typeof module !== 'undefined' && module.exports) {
  module.exports = PageDetector;
} else {
  // 浏览器环境
  window.PageDetector = PageDetector;
}
