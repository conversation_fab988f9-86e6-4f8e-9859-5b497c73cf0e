/**
 * Microsoft注册系统 v3.0 测试脚本
 * 用于验证系统各个组件的功能
 * 
 * <AUTHOR> Team
 * @version 3.0.0
 * @description 自动化测试脚本，验证v3系统的完整性和功能
 */

/**
 * 测试套件管理器
 */
class V3SystemTestSuite {
  constructor() {
    this.testResults = [];
    this.totalTests = 0;
    this.passedTests = 0;
    this.failedTests = 0;
    this.startTime = null;
  }

  /**
   * 运行所有测试
   */
  async runAllTests() {
    console.log('🧪 开始Microsoft注册系统 v3.0 测试...');
    this.startTime = Date.now();

    const testSuites = [
      () => this.testSystemInitialization(),
      () => this.testStateMachine(),
      () => this.testPageDetector(),
      () => this.testStageHandlers(),
      () => this.testErrorHandling(),
      () => this.testImapService(),
      () => this.testController(),
      () => this.testIntegration()
    ];

    for (const testSuite of testSuites) {
      try {
        await testSuite();
      } catch (error) {
        this.recordTest('测试套件执行', false, error.message);
      }
    }

    this.generateReport();
  }

  /**
   * 测试系统初始化
   */
  async testSystemInitialization() {
    console.log('📋 测试系统初始化...');

    // 测试全局对象存在性
    this.recordTest(
      '全局MicrosoftRegistrationSystemV3类存在',
      typeof window.MicrosoftRegistrationSystemV3 !== 'undefined',
      '全局类未定义'
    );

    this.recordTest(
      '初始化函数存在',
      typeof window.initializeMicrosoftRegistrationV3 === 'function',
      '初始化函数未定义'
    );

    this.recordTest(
      '获取系统实例函数存在',
      typeof window.getMicrosoftRegistrationSystemV3 === 'function',
      '获取实例函数未定义'
    );

    // 测试系统实例化
    try {
      const system = window.getMicrosoftRegistrationSystemV3();
      this.recordTest(
        '系统实例创建成功',
        system instanceof window.MicrosoftRegistrationSystemV3,
        '系统实例创建失败'
      );

      this.recordTest(
        '系统版本正确',
        system.version === '3.0.0',
        `版本不匹配: ${system.version}`
      );

      this.recordTest(
        '系统状态正常',
        ['idle', 'ready', 'running'].includes(system.systemStatus),
        `系统状态异常: ${system.systemStatus}`
      );
    } catch (error) {
      this.recordTest('系统实例化', false, error.message);
    }
  }

  /**
   * 测试状态机
   */
  async testStateMachine() {
    console.log('📋 测试状态机...');

    // 测试状态机类存在性
    this.recordTest(
      'MicrosoftRegistrationStateMachine类存在',
      typeof window.MicrosoftRegistrationStateMachine !== 'undefined',
      '状态机类未定义'
    );

    this.recordTest(
      'REGISTRATION_STAGES常量存在',
      typeof window.MicrosoftRegistrationStateMachine.REGISTRATION_STAGES !== 'undefined',
      '注册阶段常量未定义'
    );

    // 测试状态机实例化
    try {
      const stateMachine = new window.MicrosoftRegistrationStateMachine.MicrosoftRegistrationStateMachine();
      
      this.recordTest(
        '状态机实例创建成功',
        stateMachine !== null,
        '状态机实例创建失败'
      );

      this.recordTest(
        '初始状态正确',
        stateMachine.getCurrentStage() === 'idle',
        `初始状态错误: ${stateMachine.getCurrentStage()}`
      );

      // 测试状态转换
      const transitionResult = await stateMachine.transitionTo('data_permission');
      this.recordTest(
        '状态转换功能',
        typeof transitionResult === 'boolean',
        '状态转换功能异常'
      );

    } catch (error) {
      this.recordTest('状态机功能测试', false, error.message);
    }
  }

  /**
   * 测试页面检测器
   */
  async testPageDetector() {
    console.log('📋 测试页面检测器...');

    this.recordTest(
      'PageDetector类存在',
      typeof window.PageDetector !== 'undefined',
      'PageDetector类未定义'
    );

    try {
      const detector = new window.PageDetector();
      
      this.recordTest(
        '页面检测器实例创建成功',
        detector !== null,
        '页面检测器实例创建失败'
      );

      // 测试检测功能
      const detectedStage = detector.detectCurrentStage();
      this.recordTest(
        '页面检测功能',
        typeof detectedStage === 'string' || detectedStage === null,
        '页面检测功能异常'
      );

      // 测试强制检测
      const forceDetectResult = detector.forceDetectStage('email_input');
      this.recordTest(
        '强制检测功能',
        typeof forceDetectResult === 'boolean',
        '强制检测功能异常'
      );

      // 测试检测历史
      const history = detector.getDetectionHistory();
      this.recordTest(
        '检测历史功能',
        Array.isArray(history),
        '检测历史功能异常'
      );

    } catch (error) {
      this.recordTest('页面检测器功能测试', false, error.message);
    }
  }

  /**
   * 测试阶段处理器
   */
  async testStageHandlers() {
    console.log('📋 测试阶段处理器...');

    const handlerClasses = [
      'DataPermissionHandler',
      'EmailInputHandler',
      'EmailVerificationHandler',
      'VerificationCodeHandler',
      'PersonalInfoHandler',
      'NameInfoHandler',
      'CaptchaHandler',
      'LoginCompleteHandler',
      'RewardsWelcomeHandler',
      'ErrorHandler'
    ];

    for (const handlerClass of handlerClasses) {
      this.recordTest(
        `${handlerClass}类存在`,
        typeof window[handlerClass] !== 'undefined',
        `${handlerClass}类未定义`
      );

      try {
        const handler = new window[handlerClass]();
        this.recordTest(
          `${handlerClass}实例创建成功`,
          handler !== null,
          `${handlerClass}实例创建失败`
        );

        // 测试基本方法
        this.recordTest(
          `${handlerClass}具有onEnter方法`,
          typeof handler.onEnter === 'function',
          `${handlerClass}缺少onEnter方法`
        );

        this.recordTest(
          `${handlerClass}具有execute方法`,
          typeof handler.execute === 'function',
          `${handlerClass}缺少execute方法`
        );

        this.recordTest(
          `${handlerClass}具有onExit方法`,
          typeof handler.onExit === 'function',
          `${handlerClass}缺少onExit方法`
        );

      } catch (error) {
        this.recordTest(`${handlerClass}实例化测试`, false, error.message);
      }
    }
  }

  /**
   * 测试错误处理系统
   */
  async testErrorHandling() {
    console.log('📋 测试错误处理系统...');

    this.recordTest(
      'ErrorHandlingSystem存在',
      typeof window.ErrorHandlingSystem !== 'undefined',
      'ErrorHandlingSystem未定义'
    );

    try {
      const { UnifiedErrorHandler } = window.ErrorHandlingSystem;
      const errorHandler = new UnifiedErrorHandler();

      this.recordTest(
        '错误处理器实例创建成功',
        errorHandler !== null,
        '错误处理器实例创建失败'
      );

      // 测试错误处理
      const testError = new Error('测试错误');
      const handleResult = await errorHandler.handleError(testError);
      
      this.recordTest(
        '错误处理功能',
        typeof handleResult === 'object' && handleResult.hasOwnProperty('success'),
        '错误处理功能异常'
      );

      // 测试错误统计
      const stats = errorHandler.getErrorStatistics();
      this.recordTest(
        '错误统计功能',
        typeof stats === 'object' && stats.hasOwnProperty('total'),
        '错误统计功能异常'
      );

      // 测试健康状态
      const health = errorHandler.getHealthStatus();
      this.recordTest(
        '健康状态检查',
        typeof health === 'object' && health.hasOwnProperty('status'),
        '健康状态检查异常'
      );

    } catch (error) {
      this.recordTest('错误处理系统测试', false, error.message);
    }
  }

  /**
   * 测试IMAP服务
   */
  async testImapService() {
    console.log('📋 测试IMAP服务...');

    // 测试服务器连接
    try {
      const response = await fetch('http://localhost:3000/health');
      this.recordTest(
        'IMAP服务器连接',
        response.ok,
        `服务器响应状态: ${response.status}`
      );

      if (response.ok) {
        const healthData = await response.json();
        this.recordTest(
          'IMAP服务器健康状态',
          healthData.status === 'healthy',
          `服务器状态: ${healthData.status}`
        );
      }
    } catch (error) {
      this.recordTest('IMAP服务器连接', false, error.message);
    }

    // 测试配置端点
    try {
      const response = await fetch('http://localhost:3000/config');
      this.recordTest(
        'IMAP配置端点',
        response.ok,
        `配置端点响应状态: ${response.status}`
      );
    } catch (error) {
      this.recordTest('IMAP配置端点', false, error.message);
    }
  }

  /**
   * 测试控制器
   */
  async testController() {
    console.log('📋 测试控制器...');

    this.recordTest(
      'MicrosoftRegistrationController类存在',
      typeof window.MicrosoftRegistrationController !== 'undefined',
      'MicrosoftRegistrationController类未定义'
    );

    try {
      const controller = new window.MicrosoftRegistrationController();
      
      this.recordTest(
        '控制器实例创建成功',
        controller !== null,
        '控制器实例创建失败'
      );

      this.recordTest(
        '控制器具有start方法',
        typeof controller.start === 'function',
        '控制器缺少start方法'
      );

      this.recordTest(
        '控制器具有stop方法',
        typeof controller.stop === 'function',
        '控制器缺少stop方法'
      );

      this.recordTest(
        '控制器具有getStatus方法',
        typeof controller.getStatus === 'function',
        '控制器缺少getStatus方法'
      );

      // 测试状态获取
      const status = controller.getStatus();
      this.recordTest(
        '控制器状态获取',
        typeof status === 'object',
        '控制器状态获取异常'
      );

    } catch (error) {
      this.recordTest('控制器功能测试', false, error.message);
    }
  }

  /**
   * 测试系统集成
   */
  async testIntegration() {
    console.log('📋 测试系统集成...');

    try {
      const system = window.getMicrosoftRegistrationSystemV3();
      
      // 测试系统信息
      const systemInfo = system.getSystemInfo();
      this.recordTest(
        '系统信息获取',
        typeof systemInfo === 'object' && systemInfo.name,
        '系统信息获取异常'
      );

      // 测试系统状态
      const systemStatus = system.getSystemStatus();
      this.recordTest(
        '系统状态获取',
        typeof systemStatus === 'object' && systemStatus.version,
        '系统状态获取异常'
      );

      // 测试依赖检查
      const dependenciesOk = system.checkDependencies();
      this.recordTest(
        '系统依赖检查',
        dependenciesOk === true,
        '系统依赖检查失败'
      );

    } catch (error) {
      this.recordTest('系统集成测试', false, error.message);
    }
  }

  /**
   * 记录测试结果
   */
  recordTest(testName, passed, errorMessage = '') {
    this.totalTests++;
    
    const result = {
      name: testName,
      passed,
      errorMessage,
      timestamp: new Date().toISOString()
    };

    this.testResults.push(result);

    if (passed) {
      this.passedTests++;
      console.log(`✅ ${testName}`);
    } else {
      this.failedTests++;
      console.log(`❌ ${testName}: ${errorMessage}`);
    }
  }

  /**
   * 生成测试报告
   */
  generateReport() {
    const endTime = Date.now();
    const duration = endTime - this.startTime;

    console.log('\n📊 测试报告');
    console.log('='.repeat(50));
    console.log(`总测试数: ${this.totalTests}`);
    console.log(`通过: ${this.passedTests}`);
    console.log(`失败: ${this.failedTests}`);
    console.log(`成功率: ${((this.passedTests / this.totalTests) * 100).toFixed(2)}%`);
    console.log(`耗时: ${duration}ms`);
    console.log('='.repeat(50));

    if (this.failedTests > 0) {
      console.log('\n❌ 失败的测试:');
      this.testResults
        .filter(result => !result.passed)
        .forEach(result => {
          console.log(`- ${result.name}: ${result.errorMessage}`);
        });
    }

    // 生成建议
    this.generateRecommendations();

    return {
      total: this.totalTests,
      passed: this.passedTests,
      failed: this.failedTests,
      successRate: (this.passedTests / this.totalTests) * 100,
      duration,
      results: this.testResults
    };
  }

  /**
   * 生成建议
   */
  generateRecommendations() {
    console.log('\n💡 建议:');
    
    if (this.failedTests === 0) {
      console.log('🎉 所有测试通过！系统已准备好投入使用。');
    } else if (this.failedTests <= 2) {
      console.log('⚠️ 有少量测试失败，建议检查相关组件。');
    } else if (this.failedTests <= 5) {
      console.log('🔧 有多个测试失败，建议重新检查部署配置。');
    } else {
      console.log('🚨 大量测试失败，建议重新部署系统。');
    }

    // 具体建议
    const failedCategories = this.testResults
      .filter(result => !result.passed)
      .map(result => result.name);

    if (failedCategories.some(name => name.includes('IMAP'))) {
      console.log('- 检查IMAP服务器配置和连接');
    }

    if (failedCategories.some(name => name.includes('类存在'))) {
      console.log('- 检查文件加载顺序和manifest.json配置');
    }

    if (failedCategories.some(name => name.includes('状态机'))) {
      console.log('- 检查状态机相关依赖和初始化');
    }
  }
}

// 自动运行测试（如果在浏览器环境中）
if (typeof window !== 'undefined') {
  // 等待页面加载完成后运行测试
  if (document.readyState === 'complete') {
    setTimeout(() => {
      const testSuite = new V3SystemTestSuite();
      testSuite.runAllTests();
    }, 2000);
  } else {
    window.addEventListener('load', () => {
      setTimeout(() => {
        const testSuite = new V3SystemTestSuite();
        testSuite.runAllTests();
      }, 2000);
    });
  }
}

// 导出测试套件
if (typeof module !== 'undefined' && module.exports) {
  module.exports = V3SystemTestSuite;
} else {
  window.V3SystemTestSuite = V3SystemTestSuite;
}
