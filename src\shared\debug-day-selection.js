// 专门调试日期选择卡住问题的脚本

console.log('=== 日期选择调试脚本启动 ===');

// 分析日期下拉框的详细信息
function analyzeDayDropdown() {
  console.log('\n🔍 分析日期下拉框:');
  
  const dayDropdown = document.querySelector('button[name="BirthDay"]');
  if (dayDropdown) {
    console.log('✓ 找到日期下拉框:');
    console.log('  - 当前值:', dayDropdown.getAttribute('value'));
    console.log('  - 显示文本:', dayDropdown.textContent.trim());
    console.log('  - aria-label:', dayDropdown.getAttribute('aria-label'));
    console.log('  - 是否禁用:', dayDropdown.disabled);
    console.log('  - 是否可见:', dayDropdown.offsetParent !== null);
    
    // 检查是否已展开
    const isExpanded = dayDropdown.getAttribute('aria-expanded') === 'true';
    console.log('  - 是否已展开:', isExpanded);
    
    return dayDropdown;
  } else {
    console.log('❌ 未找到日期下拉框');
    return null;
  }
}

// 分析月份选择状态
function analyzeMonthState() {
  console.log('\n📅 分析月份选择状态:');
  
  const monthDropdown = document.querySelector('button[name="BirthMonth"]');
  if (monthDropdown) {
    console.log('✓ 月份下拉框状态:');
    console.log('  - 当前值:', monthDropdown.getAttribute('value'));
    console.log('  - 显示文本:', monthDropdown.textContent.trim());
    console.log('  - 是否已选择:', monthDropdown.textContent.trim() !== '');
    
    return monthDropdown.textContent.trim() !== '';
  }
  return false;
}

// 测试日期下拉框点击
function testDayDropdownClick() {
  console.log('\n🖱️ 测试日期下拉框点击:');
  
  const dayDropdown = document.querySelector('button[name="BirthDay"]');
  if (!dayDropdown) {
    console.log('❌ 未找到日期下拉框');
    return;
  }
  
  console.log('✓ 点击日期下拉框...');
  dayDropdown.click();
  
  setTimeout(() => {
    const isExpanded = dayDropdown.getAttribute('aria-expanded') === 'true';
    console.log('点击后展开状态:', isExpanded);
    
    // 查找选项
    const options = document.querySelectorAll('[role="option"]');
    console.log(`找到 ${options.length} 个选项:`);
    
    options.forEach((option, index) => {
      console.log(`  选项 ${index + 1}: "${option.textContent.trim()}" (data-value: ${option.getAttribute('data-value')})`);
    });
    
    if (options.length === 0) {
      console.log('⚠️ 没有找到任何选项，可能的原因:');
      console.log('  1. 下拉框没有正确展开');
      console.log('  2. 选项还在加载中');
      console.log('  3. 需要先选择月份');
      console.log('  4. 选项使用了不同的选择器');
    }
  }, 1000);
}

// 等待并重试日期选择
function waitAndRetryDaySelection(targetDay = 15, maxRetries = 5) {
  console.log(`\n⏳ 等待并重试日期选择 (目标: ${targetDay}日, 最大重试: ${maxRetries}次):`);
  
  let retryCount = 0;
  
  function attemptSelection() {
    retryCount++;
    console.log(`\n尝试 ${retryCount}/${maxRetries}:`);
    
    const dayDropdown = document.querySelector('button[name="BirthDay"]');
    if (!dayDropdown) {
      console.log('❌ 未找到日期下拉框');
      return;
    }
    
    // 检查是否已经有值
    const currentValue = dayDropdown.textContent.trim();
    if (currentValue && currentValue.includes('日')) {
      console.log(`✓ 日期已有值: ${currentValue}`);
      return;
    }
    
    console.log('点击日期下拉框...');
    dayDropdown.click();
    
    setTimeout(() => {
      const options = document.querySelectorAll('[role="option"]');
      console.log(`找到 ${options.length} 个选项`);
      
      if (options.length > 0) {
        // 查找目标日期
        let targetOption = null;
        for (const option of options) {
          const text = option.textContent.trim();
          if (text.includes(`${targetDay}日`) || text === targetDay.toString()) {
            targetOption = option;
            break;
          }
        }
        
        if (targetOption) {
          console.log(`✓ 找到目标日期选项: ${targetOption.textContent.trim()}`);
          targetOption.click();
          
          setTimeout(() => {
            const updatedValue = dayDropdown.textContent.trim();
            console.log(`✓ 选择完成，当前值: ${updatedValue}`);
          }, 500);
        } else {
          console.log(`❌ 未找到目标日期 ${targetDay}，选择第一个可用选项`);
          options[0].click();
        }
      } else {
        console.log('❌ 没有找到选项');
        if (retryCount < maxRetries) {
          console.log(`等待 2 秒后重试...`);
          setTimeout(attemptSelection, 2000);
        } else {
          console.log('❌ 达到最大重试次数，放弃选择');
        }
      }
    }, 1000);
  }
  
  attemptSelection();
}

// 完整的调试流程
function runFullDayDebug(targetDay = 15) {
  console.log(`\n🚀 开始完整的日期选择调试 (目标: ${targetDay}日):`);
  
  // 步骤1: 分析月份状态
  const monthSelected = analyzeMonthState();
  if (!monthSelected) {
    console.log('⚠️ 警告: 月份可能未选择，这可能影响日期选项');
  }
  
  // 步骤2: 分析日期下拉框
  setTimeout(() => {
    analyzeDayDropdown();
    
    // 步骤3: 测试点击
    setTimeout(() => {
      testDayDropdownClick();
      
      // 步骤4: 等待并重试选择
      setTimeout(() => {
        waitAndRetryDaySelection(targetDay);
      }, 3000);
    }, 2000);
  }, 1000);
}

// 简化的日期选择函数
function simpleDaySelect(targetDay = 15) {
  console.log(`\n🎯 简化日期选择 (目标: ${targetDay}日):`);
  
  const dayDropdown = document.querySelector('button[name="BirthDay"]');
  if (!dayDropdown) {
    console.log('❌ 未找到日期下拉框');
    return;
  }
  
  // 强制点击
  console.log('强制点击日期下拉框...');
  dayDropdown.click();
  
  // 多次尝试查找选项
  let attemptCount = 0;
  const maxAttempts = 10;
  
  const checkForOptions = () => {
    attemptCount++;
    const options = document.querySelectorAll('[role="option"]');
    
    console.log(`尝试 ${attemptCount}: 找到 ${options.length} 个选项`);
    
    if (options.length > 0) {
      // 找到选项，尝试选择
      let selected = false;
      for (const option of options) {
        const text = option.textContent.trim();
        if (text.includes(`${targetDay}日`) || text === targetDay.toString()) {
          console.log(`✓ 选择: ${text}`);
          option.click();
          selected = true;
          break;
        }
      }
      
      if (!selected && options.length > 0) {
        console.log('选择第一个可用选项');
        options[0].click();
      }
    } else if (attemptCount < maxAttempts) {
      // 继续尝试
      setTimeout(checkForOptions, 500);
    } else {
      console.log('❌ 超时，未找到选项');
    }
  };
  
  setTimeout(checkForOptions, 500);
}

// 导出函数
window.dayDebug = {
  analyze: analyzeDayDropdown,
  analyzeMonth: analyzeMonthState,
  testClick: testDayDropdownClick,
  waitAndRetry: waitAndRetryDaySelection,
  runFull: runFullDayDebug,
  simpleSelect: simpleDaySelect
};

console.log('\n=== 日期调试脚本加载完成 ===');
console.log('可用函数:');
console.log('- dayDebug.analyze() - 分析日期下拉框');
console.log('- dayDebug.analyzeMonth() - 分析月份状态');
console.log('- dayDebug.testClick() - 测试点击');
console.log('- dayDebug.waitAndRetry(日期) - 等待重试选择');
console.log('- dayDebug.runFull(日期) - 完整调试流程');
console.log('- dayDebug.simpleSelect(日期) - 简化选择');
console.log('\n推荐使用: dayDebug.runFull(15)');
