@echo off
echo ========================================
echo   Microsoft Registration System v3.0
echo            Smart Start
echo ========================================
echo.

REM Check Node.js
node --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Node.js not found
    echo Please install Node.js from https://nodejs.org/
    pause
    exit /b 1
)

echo Node.js installed
node --version
echo.

REM Auto-create .env if not exists
if not exist ".env" (
    echo Creating .env configuration file...
    (
        echo # Microsoft Registration System v3.0 Configuration
        echo IMAP_HOST=imap.qq.com
        echo IMAP_PORT=993
        echo IMAP_USER=<EMAIL>
        echo IMAP_PASSWORD=zoqhcyomjbuubace
        echo IMAP_TLS=true
        echo TLS_REJECT_UNAUTHORIZED=false
        echo TLS_MIN_VERSION=TLSv1.2
        echo SERVER_PORT=3000
        echo DEBUG=false
        echo LOG_LEVEL=info
        echo MAX_EMAIL_AGE_MINUTES=10
        echo VERIFICATION_CODE_TTL_MINUTES=5
        echo MAX_CONCURRENT_CONNECTIONS=3
        echo MAX_RETRIES=5
        echo RETRY_DELAY_MS=3000
        echo CONNECTION_TIMEOUT_MS=30000
    ) > .env
    
    if exist ".env" (
        echo [OK] .env file created automatically
    ) else (
        echo [ERROR] Failed to create .env file
        pause
        exit /b 1
    )
) else (
    echo [OK] .env configuration file exists
)

echo.
echo Starting IMAP server...
echo.
echo Server Information:
echo - Port: 3000
echo - Health Check: http://localhost:3000/health
echo - Configuration: http://localhost:3000/config
echo.
echo Press Ctrl+C to stop server
echo.

REM Kill any existing node processes
taskkill /f /im node.exe >nul 2>&1

REM Start server
node src/server/imap-server.js

echo.
echo Server stopped
pause
