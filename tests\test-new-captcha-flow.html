<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>新人机验证流程测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .button {
            background: #0078d4;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .button:hover {
            background: #106ebe;
        }
        .accessibility-button {
            cursor: pointer;
            display: inline-block;
            width: 58px;
            position: relative;
            vertical-align: middle;
            background: white;
            border: 1px solid #ccc;
            border-radius: 50%;
            padding: 5px;
        }
        .press-again-button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            animation: 2322ms ease 0s 1 normal none running textColorInvert;
        }
        .press-again-button:hover {
            background: #45a049;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 10px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
        .hidden {
            display: none;
        }
        @keyframes textColorInvert {
            0% { color: white; }
            50% { color: #333; }
            100% { color: white; }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>新人机验证流程测试</h1>
        <p>此页面用于测试更新后的人机验证处理流程</p>

        <div class="test-section">
            <h3>测试场景 1: 可访问性挑战按钮</h3>
            <p>模拟第一步：点击可访问性挑战按钮</p>
            
            <!-- 模拟可访问性挑战按钮 -->
            <a style="cursor:pointer; display: inline-block;width:58px; position: relative; vertical-align: middle;" 
               tabindex="0" 
               id="gnnyqDwwEqnYTkM" 
               role="button" 
               aria-label="可访问性挑战" 
               aria-describedby="diwTPxsDmVZgfEz"
               class="accessibility-button">
                <svg width="50" height="40" viewBox="0 0 50 50" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <g filter="url(#filter0_d_1_9)">
                        <path d="M25 44C36.0457 44 45 35.0457 45 24C45 12.9543 36.0457 4 25 4C13.9543 4 5 12.9543 5 24C5 35.0457 13.9543 44 25 44Z" fill="#FDFDFF"></path>
                        <path d="M25 44C36.0457 44 45 35.0457 45 24C45 12.9543 36.0457 4 25 4C13.9543 4 5 12.9543 5 24C5 35.0457 13.9543 44 25 44Z" fill="#F7F8FA"></path>
                        <path d="M25 44C36.0457 44 45 35.0457 45 24C45 12.9543 36.0457 4 25 4C13.9543 4 5 12.9543 5 24C5 35.0457 13.9543 44 25 44Z" fill="white"></path>
                        <path d="M45 24C45 12.997 36.057 4 25 4C13.943 4 5 12.997 5 24C5 35.003 13.997 44 25 44C36.003 44 45 35.003 45 24ZM25 10.07C25.3988 10.0693 25.7939 10.1474 26.1625 10.2998C26.531 10.4521 26.8659 10.6758 27.1479 10.9578C27.4299 11.2399 27.6534 11.5748 27.8056 11.9435C27.9578 12.3121 28.0358 12.7072 28.035 13.106C28.0355 13.5047 27.9574 13.8995 27.805 14.2679C27.6527 14.6363 27.4291 14.971 27.1472 15.2529C26.8652 15.5347 26.5304 15.7582 26.162 15.9104C25.7935 16.0626 25.3987 16.1407 25 16.14C24.6013 16.1407 24.2063 16.0626 23.8378 15.9103C23.4693 15.758 23.1344 15.5345 22.8525 15.2525C22.5705 14.9706 22.347 14.6357 22.1947 14.2672C22.0424 13.8987 21.9643 13.5037 21.965 13.105C21.965 11.48 23.32 10.07 25 10.07ZM21.965 36.575C21.8466 36.8501 21.6496 37.0841 21.3988 37.2477C21.148 37.4114 20.8545 37.4974 20.555 37.495C20.339 37.495 20.122 37.442 19.905 37.333C19.092 36.953 18.767 36.033 19.146 35.22C19.146 35.22 22.127 28.39 22.669 25.897C22.886 25.03 22.995 22.699 23.049 21.615C23.049 21.235 22.832 20.911 22.507 20.802L15.786 18.851C14.919 18.58 14.431 17.659 14.702 16.846C14.972 16.033 15.894 15.653 16.707 15.87C16.707 15.87 22.832 17.821 25 17.821C27.168 17.821 33.401 15.816 33.401 15.816C34.214 15.599 35.136 16.086 35.352 16.9C35.569 17.713 35.082 18.634 34.268 18.85L27.602 20.856C27.276 20.965 27.005 21.29 27.06 21.669C27.114 22.753 27.222 25.084 27.439 25.951C27.981 28.444 30.962 35.274 30.962 35.274C31.342 36.087 30.962 37.008 30.203 37.388C30.002 37.4922 29.7794 37.5477 29.553 37.55C28.957 37.55 28.36 37.225 28.143 36.629L25 30.07L21.965 36.575Z" fill="#424257"></path>
                    </g>
                    <defs>
                        <filter id="filter0_d_1_9" x="0" y="0" width="50" height="50" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                            <feFlood flood-opacity="0" result="BackgroundImageFix"></feFlood>
                            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"></feColorMatrix>
                            <feOffset dy="1"></feOffset>
                            <feGaussianBlur stdDeviation="2.5"></feGaussianBlur>
                            <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.206358 0"></feColorMatrix>
                            <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1_9"></feBlend>
                            <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1_9" result="shape"></feBlend>
                        </filter>
                    </defs>
                </svg>
                <div id="hflANTPcpIHRTbf">可访问性挑战</div>
                <span id="diwTPxsDmVZgfEz" style="position: absolute; clip: rect(0 0 0 0); border: 0; width: 1px; height: 1px; margin: -1px; overflow: hidden; padding: 0;">人类挑战需要验证。请按一次按钮，等待确认，并在出现提示时再按一次。</span>
            </a>

            <button class="button" onclick="testAccessibilityChallenge()">测试可访问性挑战检测</button>
        </div>

        <div class="test-section">
            <h3>测试场景 2: "再次按下"按钮</h3>
            <p>模拟第二步：点击"再次按下"按钮</p>
            
            <!-- 模拟"再次按下"按钮 -->
            <div id="iJONTsjCTbadsDp" 
                 class="CCovUDZBqqyRyvP press-again-button hidden" 
                 tabindex="0" 
                 aria-describedby="JQnIkQlvLaHLnQL CteEHWevSIrgTFT" 
                 role="button" 
                 aria-label="再次按下">
                <div id="ZSqumagvhPUXBNa"></div>
                <div id="jJhnWzteQkbuopA" dir="auto">
                    <div id="xoiZVDRpiElShvZ" style="width: 251px;"></div>
                    <div id="UIJpiruaCRPSOna">
                        <p id="NnvvRHVMqEGacqH" class="mcvgqNcojYFUtSY" style="animation: 2322ms ease 0s 1 normal none running textColorInvert; color: rgb(255, 255, 255);">再次按下</p>
                        <span id="JQnIkQlvLaHLnQL" class="mPVgMREmHAuygcg"></span>
                        <span id="xrIChBxYisrQykN" class="mPVgMREmHAuygcg" aria-live="assertive">再次按下</span>
                    </div>
                    <div class="fetching-volume"><span>•</span><span>•</span><span>•</span></div>
                    <div id="checkmark"></div>
                    <div id="ripple"></div>
                </div>
            </div>

            <button class="button" onclick="testPressAgainChallenge()">测试"再次按下"检测</button>
            <button class="button" onclick="showPressAgainButton()">显示"再次按下"按钮</button>
        </div>

        <div class="test-section">
            <h3>测试场景 3: 完整流程测试</h3>
            <p>测试完整的人机验证流程</p>
            
            <button class="button" onclick="testFullCaptchaFlow()">测试完整验证流程</button>
            <button class="button" onclick="resetTest()">重置测试</button>
        </div>

        <div class="test-section">
            <h3>日志输出</h3>
            <div id="log" class="log"></div>
            <button class="button" onclick="clearLog()">清除日志</button>
        </div>
    </div>

    <script>
        // 日志函数
        function log(message) {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${timestamp}] ${message}<br>`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }

        // 模拟扩展的函数（简化版）
        function isCaptchaPage() {
            // 检查新的可访问性挑战按钮
            const accessibilityButton = document.querySelector('[role="button"][aria-label*="可访问性挑战"], #gnnyqDwwEqnYTkM');
            if (accessibilityButton) {
                return true;
            }

            // 检查"再次按下"按钮
            const pressAgainButton = document.querySelector('[role="button"][aria-label*="再次按下"], #iJONTsjCTbadsDp, .CCovUDZBqqyRyvP');
            if (pressAgainButton) {
                return true;
            }

            // 检查是否有包含SVG的可访问性按钮
            const svgButton = document.querySelector('a[role="button"] svg, [tabindex="0"] svg');
            if (svgButton && svgButton.closest('[aria-label*="可访问性"]')) {
                return true;
            }

            return false;
        }

        function findAccessibilityButton() {
            // 首先检查是否有新的可访问性挑战按钮
            let accessibilityButton = document.querySelector('[role="button"][aria-label*="可访问性挑战"]');
            
            // 如果没找到，尝试通过ID查找
            if (!accessibilityButton) {
                accessibilityButton = document.querySelector('#gnnyqDwwEqnYTkM');
            }
            
            // 如果还没找到，尝试查找包含SVG的按钮
            if (!accessibilityButton) {
                const svgButtons = document.querySelectorAll('a[role="button"] svg, [tabindex="0"] svg');
                for (let svg of svgButtons) {
                    const button = svg.closest('[role="button"], a[role="button"], [tabindex="0"]');
                    if (button && (button.getAttribute('aria-label') || '').includes('可访问性')) {
                        accessibilityButton = button;
                        break;
                    }
                }
            }
            
            return accessibilityButton;
        }

        function findPressAgainButton() {
            // 方法1: 通过aria-label查找
            let button = document.querySelector('[role="button"][aria-label*="再次按下"]');
            if (button) return button;
            
            // 方法2: 通过ID和类名查找
            button = document.querySelector('#iJONTsjCTbadsDp, .CCovUDZBqqyRyvP');
            if (button) return button;
            
            // 方法3: 通过文本内容查找
            const allButtons = document.querySelectorAll('[role="button"], button, div[tabindex="0"]');
            for (let btn of allButtons) {
                if (btn.textContent.includes('再次按下')) {
                    return btn;
                }
            }
            
            return null;
        }

        // 测试函数
        function testAccessibilityChallenge() {
            log('🔍 测试可访问性挑战按钮检测...');
            
            if (isCaptchaPage()) {
                log('✅ 成功检测到人机验证页面');
            } else {
                log('❌ 未检测到人机验证页面');
                return;
            }
            
            const button = findAccessibilityButton();
            if (button) {
                log('✅ 找到可访问性挑战按钮');
                log(`按钮信息: ID=${button.id}, 类名=${button.className}, aria-label=${button.getAttribute('aria-label')}`);
                
                // 模拟点击
                button.addEventListener('click', function() {
                    log('🎯 可访问性挑战按钮被点击');
                    setTimeout(() => {
                        showPressAgainButton();
                        log('⏳ 2秒后显示"再次按下"按钮');
                    }, 2000);
                });
                
                button.click();
            } else {
                log('❌ 未找到可访问性挑战按钮');
            }
        }

        function testPressAgainChallenge() {
            log('🔍 测试"再次按下"按钮检测...');
            
            const button = findPressAgainButton();
            if (button) {
                log('✅ 找到"再次按下"按钮');
                log(`按钮信息: ID=${button.id}, 类名=${button.className}, aria-label=${button.getAttribute('aria-label')}`);
                
                // 模拟点击
                button.addEventListener('click', function() {
                    log('🎯 "再次按下"按钮被点击');
                    log('⏳ 等待4秒检查验证结果...');
                    setTimeout(() => {
                        log('🎉 模拟验证成功！');
                    }, 4000);
                });
                
                button.click();
            } else {
                log('❌ 未找到"再次按下"按钮');
            }
        }

        function showPressAgainButton() {
            const button = document.getElementById('iJONTsjCTbadsDp');
            button.classList.remove('hidden');
            log('👁️ "再次按下"按钮已显示');
        }

        function testFullCaptchaFlow() {
            log('🚀 开始完整验证流程测试...');
            resetTest();
            setTimeout(() => {
                testAccessibilityChallenge();
            }, 1000);
        }

        function resetTest() {
            const button = document.getElementById('iJONTsjCTbadsDp');
            button.classList.add('hidden');
            log('🔄 测试已重置');
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            log('📄 测试页面已加载');
            log('🔍 当前页面是否为人机验证页面: ' + (isCaptchaPage() ? '是' : '否'));
        });
    </script>
</body>
</html>
