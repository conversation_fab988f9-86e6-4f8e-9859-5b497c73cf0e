// 测试人机验证页面执行
console.log('🧪 开始测试人机验证页面执行...');

// 模拟当前页面状态
function testCaptchaPageExecution() {
  console.log('📋 测试场景：人机验证页面检测和执行');
  
  // 1. 测试页面检测
  console.log('🔍 步骤1：测试页面检测');
  
  // 模拟页面标题
  const title = document.createElement('h1');
  title.setAttribute('data-testid', 'title');
  title.textContent = '证明你不是机器人';
  document.body.appendChild(title);
  
  // 模拟长按文本
  const description = document.createElement('span');
  description.textContent = '长按该按钮。';
  document.body.appendChild(description);
  
  // 模拟第一个验证按钮
  const firstButton = document.createElement('button');
  firstButton.id = 'GMGNsNHVCsQWOSo';
  firstButton.textContent = '第一个验证按钮';
  document.body.appendChild(firstButton);
  
  // 模拟第二个验证按钮（稍后添加）
  const secondButton = document.createElement('button');
  secondButton.id = 'TmLzkpoGUMIggiM';
  secondButton.textContent = '第二个验证按钮';
  secondButton.style.display = 'none'; // 初始隐藏
  document.body.appendChild(secondButton);
  
  console.log('✅ 页面元素创建完成');
  
  // 2. 测试isCaptchaPage函数
  console.log('🔍 步骤2：测试isCaptchaPage函数');
  
  function testIsCaptchaPage() {
    // 检查标题
    const titleElement = document.querySelector('h1[data-testid="title"]');
    if (titleElement && titleElement.textContent.includes('证明你不是机器人')) {
      console.log('✅ 标题检测通过');
      return true;
    }
    
    // 检查页面文本
    const bodyText = document.body.textContent;
    if (bodyText.includes('证明你不是机器人') || 
        (bodyText.includes('长按') && bodyText.includes('按钮'))) {
      console.log('✅ 页面文本检测通过');
      return true;
    }
    
    console.log('❌ 人机验证页面检测失败');
    return false;
  }
  
  const isCaptchaResult = testIsCaptchaPage();
  console.log(`📊 isCaptchaPage结果: ${isCaptchaResult ? '✅ 通过' : '❌ 失败'}`);
  
  // 3. 测试handleCaptchaPage函数逻辑
  console.log('🔍 步骤3：测试handleCaptchaPage函数逻辑');
  
  function testHandleCaptchaPage() {
    console.log('🎯 模拟handleCaptchaPage执行...');
    
    // 重置状态
    window.captchaInProgress = false;
    window.captchaStep = 1;
    
    // 检查是否有"再次尝试"的提示
    const retryText = document.querySelector('#LKlphTDXuogbAXR');
    if (retryText && retryText.textContent.includes('请再试一次')) {
      console.log('🔄 检测到重试提示');
      return false;
    }
    
    // 开始验证流程
    if (window.captchaInProgress) {
      console.log('⚠️ 验证已在进行中');
      return false;
    }
    
    window.captchaInProgress = true;
    
    if (!window.captchaStep) {
      window.captchaStep = 1;
    }
    
    console.log(`🎯 开始验证流程 - 步骤 ${window.captchaStep}`);
    
    if (window.captchaStep === 1) {
      // 第一步：查找第一个按钮
      const firstBtn = document.querySelector('#GMGNsNHVCsQWOSo');
      if (firstBtn) {
        console.log('✅ 找到第一个验证按钮');
        console.log('🖱️ 模拟点击第一个按钮...');
        
        // 模拟点击
        firstBtn.style.backgroundColor = 'green';
        firstBtn.textContent = '已点击';
        
        // 设置下一步
        window.captchaStep = 2;
        console.log('⏰ 模拟等待15秒...');
        
        // 模拟15秒后显示第二个按钮
        setTimeout(() => {
          secondButton.style.display = 'block';
          console.log('✅ 第二个按钮现在可见');
          testStep2();
        }, 1000); // 为了测试，缩短到1秒
        
        return true;
      } else {
        console.log('❌ 未找到第一个验证按钮');
        return false;
      }
    }
    
    return false;
  }
  
  function testStep2() {
    console.log('🎯 测试第二步...');
    
    if (window.captchaStep === 2) {
      const secondBtn = document.querySelector('#TmLzkpoGUMIggiM');
      if (secondBtn) {
        console.log('✅ 找到第二个验证按钮');
        console.log('🖱️ 模拟点击第二个按钮...');
        
        // 模拟点击
        secondBtn.style.backgroundColor = 'blue';
        secondBtn.textContent = '已点击';
        
        window.captchaStep = 3;
        console.log('✅ 验证流程完成');
        
        // 重置状态
        window.captchaInProgress = false;
        window.captchaStep = 1;
        
        return true;
      } else {
        console.log('❌ 未找到第二个验证按钮');
        return false;
      }
    }
    
    return false;
  }
  
  const handleResult = testHandleCaptchaPage();
  console.log(`📊 handleCaptchaPage结果: ${handleResult ? '✅ 通过' : '❌ 失败'}`);
  
  // 4. 总结
  console.log('📊 测试总结:');
  console.log(`  - 页面检测: ${isCaptchaResult ? '✅ 通过' : '❌ 失败'}`);
  console.log(`  - 处理逻辑: ${handleResult ? '✅ 通过' : '❌ 失败'}`);
  
  if (isCaptchaResult && handleResult) {
    console.log('🎉 所有测试通过！人机验证页面应该能正常工作');
  } else {
    console.log('⚠️ 部分测试失败，需要进一步调试');
  }
}

// 清理页面并运行测试
document.body.innerHTML = '';
testCaptchaPageExecution();
