/**
 * RTBS IMAP邮件服务器
 * 用于接收s4464.cfd域名的邮件并提供验证码给扩展
 *
 * <AUTHOR> Team
 * @version 3.0.0
 * @description 完全重构的IMAP服务器，基于阶段化处理和资源管理
 */

const express = require('express');
const Imap = require('imap');
const { simpleParser } = require('mailparser');
const cors = require('cors');
const fs = require('fs');
const path = require('path');

// 加载环境变量
require('dotenv').config();

// 常量定义
const CONSTANTS = {
  // 超时设置
  CONNECTION_TIMEOUT: 30000,    // 30秒连接超时
  SEARCH_TIMEOUT: 15000,        // 15秒搜索超时
  FETCH_TIMEOUT: 20000,         // 20秒获取超时

  // 重试设置
  MAX_RETRIES: 3,               // 最大重试次数
  RETRY_DELAYS: [2000, 4000, 6000], // 重试延迟时间
  CONNECTION_RETRY_DELAYS: [3000, 6000, 9000], // 连接重试延迟

  // 缓存设置
  VERIFICATION_CODE_TTL: 5 * 60 * 1000, // 验证码缓存5分钟
  EMAIL_SEARCH_WINDOW: 30 * 60 * 1000,  // 搜索最近30分钟邮件

  // 服务器设置
  DEFAULT_PORT: 3000,
  HEALTH_CHECK_INTERVAL: 30000, // 30秒健康检查间隔
};

/**
 * IMAP邮件服务器类
 * 提供邮件接收、验证码提取、邮件删除等功能
 */
class ImapEmailServer {
  /**
   * 构造函数
   * 初始化服务器配置和缓存
   */
  constructor() {
    this.app = express();
    this.port = this.validatePort(process.env.SERVER_PORT) || CONSTANTS.DEFAULT_PORT;

    // IMAP配置验证和初始化
    this.imapConfig = this.initializeImapConfig();

    // 缓存系统
    this.emailCache = new Map(); // 缓存邮件内容
    this.verificationCodes = new Map(); // 缓存验证码

    // 统计信息
    this.stats = {
      totalRequests: 0,
      successfulExtractions: 0,
      failedExtractions: 0,
      deletedEmails: 0,
      startTime: new Date()
    };

    // 健康检查状态
    this.healthStatus = {
      status: 'starting',
      lastCheck: null,
      imapConnection: 'unknown'
    };

    this.setupExpress();
    this.startHealthCheck();
  }

  /**
   * 验证端口号
   * @param {string|number} port - 端口号
   * @returns {number|null} 有效的端口号或null
   */
  validatePort(port) {
    const portNum = parseInt(port);
    if (isNaN(portNum) || portNum < 1 || portNum > 65535) {
      console.warn(`⚠️ 无效端口号: ${port}，使用默认端口`);
      return null;
    }
    return portNum;
  }

  /**
   * 初始化IMAP配置
   * @returns {Object} IMAP配置对象
   */
  initializeImapConfig() {
    const config = {
      user: process.env.IMAP_USER || '<EMAIL>',
      password: process.env.IMAP_PASSWORD || 'your-password-here',
      host: process.env.IMAP_HOST || 'imap.s4464.cfd',
      port: this.validatePort(process.env.IMAP_PORT) || 993,
      tls: process.env.IMAP_TLS !== 'false', // 默认启用TLS
      tlsOptions: {
        rejectUnauthorized: process.env.TLS_REJECT_UNAUTHORIZED === 'true' || false,
        minVersion: process.env.TLS_MIN_VERSION || 'TLSv1.2'
      },
      connTimeout: CONSTANTS.CONNECTION_TIMEOUT,
      authTimeout: CONSTANTS.CONNECTION_TIMEOUT,
      keepalive: false // 避免连接池问题
    };

    // 验证必要的配置
    if (!config.user || !config.password || !config.host) {
      console.error('❌ IMAP配置不完整，请检查环境变量');
      throw new Error('IMAP配置不完整');
    }

    console.log('✅ IMAP配置已初始化:', {
      host: config.host,
      port: config.port,
      user: config.user,
      tls: config.tls
    });

    return config;
  }

  /**
   * 启动健康检查
   */
  startHealthCheck() {
    setInterval(() => {
      this.performHealthCheck();
    }, CONSTANTS.HEALTH_CHECK_INTERVAL);
  }

  /**
   * 执行健康检查
   */
  async performHealthCheck() {
    try {
      this.healthStatus.lastCheck = new Date();
      const testResult = await this.testImapConnection();
      this.healthStatus.imapConnection = testResult ? 'healthy' : 'unhealthy';
      this.healthStatus.status = testResult ? 'healthy' : 'degraded';
    } catch (error) {
      console.error('健康检查失败:', error.message);
      this.healthStatus.status = 'unhealthy';
      this.healthStatus.imapConnection = 'error';
    }
  }

  /**
   * 测试IMAP连接
   * @returns {Promise<boolean>} 连接是否成功
   */
  async testImapConnection() {
    return new Promise((resolve) => {
      const imap = new Imap(this.imapConfig);
      let resolved = false;

      const timeout = setTimeout(() => {
        if (!resolved) {
          resolved = true;
          imap.destroy();
          resolve(false);
        }
      }, 5000);

      imap.once('ready', () => {
        if (!resolved) {
          resolved = true;
          clearTimeout(timeout);
          imap.end();
          resolve(true);
        }
      });

      imap.once('error', () => {
        if (!resolved) {
          resolved = true;
          clearTimeout(timeout);
          resolve(false);
        }
      });

      imap.connect();
    });
  }

  /**
   * 设置Express服务器
   * 配置中间件和路由
   */
  setupExpress() {
    // 中间件配置
    this.app.use(cors({
      origin: process.env.CORS_ORIGIN || '*',
      methods: ['GET', 'POST', 'DELETE'],
      allowedHeaders: ['Content-Type', 'Authorization']
    }));
    this.app.use(express.json({ limit: '10mb' }));
    this.app.use(express.urlencoded({ extended: true, limit: '10mb' }));

    // 请求日志中间件
    this.app.use((req, res, next) => {
      this.stats.totalRequests++;
      console.log(`📡 ${new Date().toISOString()} - ${req.method} ${req.path} - IP: ${req.ip}`);
      next();
    });

    // 错误处理中间件
    this.app.use((err, req, res, next) => {
      console.error('❌ Express错误:', err);
      res.status(500).json({
        success: false,
        error: 'Internal Server Error',
        timestamp: new Date().toISOString()
      });
    });

    this.setupRoutes();
  }

  /**
   * 设置API路由
   */
  setupRoutes() {
    // 健康检查端点
    this.app.get('/health', (req, res) => {
      res.json({
        status: this.healthStatus.status,
        timestamp: new Date().toISOString(),
        service: 'RTBS IMAP Server',
        version: '2.0.0',
        uptime: Math.floor((Date.now() - this.stats.startTime.getTime()) / 1000),
        stats: this.stats,
        imap: {
          connection: this.healthStatus.imapConnection,
          lastCheck: this.healthStatus.lastCheck
        }
      });
    });

    // 统计信息端点
    this.app.get('/stats', (req, res) => {
      res.json({
        ...this.stats,
        uptime: Math.floor((Date.now() - this.stats.startTime.getTime()) / 1000),
        cacheSize: {
          emails: this.emailCache.size,
          verificationCodes: this.verificationCodes.size
        }
      });
    });

    // 获取验证码端点（增强版，支持时间戳过滤）
    this.app.get('/verification-code/:email', async (req, res) => {
      const email = req.params.email;
      const since = req.query.since ? parseInt(req.query.since) : null;
      const timestamp = req.query.timestamp ? parseInt(req.query.timestamp) : Date.now();

      console.log(`收到验证码请求: ${email}`);
      if (since) {
        console.log(`⏰ 时间过滤: 只获取 ${new Date(since).toISOString()} 之后的验证码`);
      }

      try {
        const result = await this.getVerificationCodeWithFilter(email, since);
        if (result && result.code) {
          // 验证验证码是否属于请求的账号
          const isValidForAccount = this.validateCodeForAccount(result, email);

          if (isValidForAccount) {
            res.json({
              success: true,
              email: email,
              code: result.code,
              timestamp: result.emailTimestamp || new Date().toISOString(),
              relevanceScore: result.relevanceScore || 0
            });
          } else {
            console.log(`❌ 验证码不属于请求的账号: ${email}`);
            res.json({
              success: false,
              message: '验证码不属于请求的账号',
              email: email,
              timestamp: new Date().toISOString()
            });
          }
        } else {
          res.json({
            success: false,
            message: '未找到验证码',
            email: email,
            timestamp: new Date().toISOString()
          });
        }
      } catch (error) {
        console.error('获取验证码失败:', error);
        res.status(500).json({
          success: false,
          error: error.message,
          email: email,
          timestamp: new Date().toISOString()
        });
      }
    });

    // 清理旧验证码端点
    this.app.post('/clear-old-codes', async (req, res) => {
      const { email, timestamp } = req.body;
      console.log(`🧹 收到清理旧验证码请求: ${email}`);

      try {
        const clearedCount = this.clearOldVerificationCodes(email, timestamp);
        res.json({
          success: true,
          message: '旧验证码已清理',
          email: email,
          cleared_count: clearedCount,
          timestamp: new Date().toISOString()
        });
      } catch (error) {
        console.error('清理旧验证码失败:', error);
        res.status(500).json({
          success: false,
          error: error.message,
          email: email,
          timestamp: new Date().toISOString()
        });
      }
    });

    // 手动触发邮件检查端点
    this.app.post('/check-email/:email', async (req, res) => {
      const email = req.params.email;
      console.log(`手动检查邮件: ${email}`);
      
      try {
        await this.checkEmailForUser(email);
        res.json({
          success: true,
          message: '邮件检查已触发',
          email: email,
          timestamp: new Date().toISOString()
        });
      } catch (error) {
        console.error('邮件检查失败:', error);
        res.status(500).json({
          success: false,
          error: error.message,
          email: email,
          timestamp: new Date().toISOString()
        });
      }
    });

    // 获取邮件列表端点
    this.app.get('/emails/:email', (req, res) => {
      const email = req.params.email;
      const emails = this.emailCache.get(email) || [];
      
      res.json({
        success: true,
        email: email,
        count: emails.length,
        emails: emails,
        timestamp: new Date().toISOString()
      });
    });

    // 删除验证码邮件端点
    this.app.delete('/verification-email/:email', async (req, res) => {
      const email = req.params.email;
      console.log(`收到删除验证码邮件请求: ${email}`);

      try {
        const result = await this.deleteVerificationEmail(email);
        res.json({
          success: true,
          email: email,
          deleted: result.deleted,
          message: result.message,
          timestamp: new Date().toISOString()
        });
      } catch (error) {
        console.error('删除验证码邮件失败:', error);
        res.status(500).json({
          success: false,
          error: error.message,
          email: email,
          timestamp: new Date().toISOString()
        });
      }
    });

    // 配置信息端点
    this.app.get('/config', (req, res) => {
      res.json({
        imapHost: this.imapConfig.host,
        imapPort: this.imapConfig.port,
        imapUser: this.imapConfig.user,
        serverPort: this.port,
        timestamp: new Date().toISOString()
      });
    });
  }

  // 连接IMAP服务器并检查邮件（增强版本）
  async checkEmailForUser(targetEmail) {
    return new Promise((resolve, reject) => {
      const imap = new Imap(this.imapConfig);
      let isResolved = false;

      // 设置连接超时
      const connectionTimeout = setTimeout(() => {
        if (!isResolved) {
          isResolved = true;
          console.error('⏰ IMAP连接超时');
          imap.destroy();
          reject(new Error('IMAP连接超时'));
        }
      }, 30000); // 30秒超时

      imap.once('ready', () => {
        console.log('🔗 IMAP连接成功');
        clearTimeout(connectionTimeout);

        imap.openBox('INBOX', false, (err, box) => {
          if (err) {
            if (!isResolved) {
              isResolved = true;
              console.error('❌ 打开收件箱失败:', err);
              imap.end();
              reject(err);
            }
            return;
          }

          console.log(`📬 收件箱包含 ${box.messages.total} 封邮件`);

          // 改进的搜索条件：按时间和邮箱地址搜索
          const searchCriteria = [
            ['SINCE', new Date(Date.now() - 30 * 60 * 1000)],
            // 尝试多种搜索条件来匹配目标邮箱
            'OR',
            ['TO', targetEmail],
            ['CC', targetEmail],
            ['BCC', targetEmail],
            // 也搜索邮件内容包含目标邮箱的情况（用于转发邮件）
            ['BODY', targetEmail]
          ];

          console.log(`🔍 搜索条件: 最近30分钟内与邮箱 ${targetEmail} 相关的邮件`);

          // 设置搜索超时
          const searchTimeout = setTimeout(() => {
            if (!isResolved) {
              isResolved = true;
              console.error('⏰ 邮件搜索超时');
              imap.end();
              reject(new Error('邮件搜索超时'));
            }
          }, 15000); // 15秒搜索超时

          imap.search(searchCriteria, (err, results) => {
            clearTimeout(searchTimeout);

            if (err) {
              console.warn('⚠️ 复杂搜索失败，尝试简单搜索:', err.message);
              // 如果复杂搜索失败，回退到简单的时间搜索
              const fallbackCriteria = [
                ['SINCE', new Date(Date.now() - 30 * 60 * 1000)]
              ];

              imap.search(fallbackCriteria, (fallbackErr, fallbackResults) => {
                if (fallbackErr) {
                  if (!isResolved) {
                    isResolved = true;
                    console.error('❌ 搜索邮件失败:', fallbackErr);
                    imap.end();
                    reject(fallbackErr);
                  }
                  return;
                }

                console.log(`📧 回退搜索找到 ${fallbackResults?.length || 0} 封邮件`);
                this.processSearchResults(fallbackResults, targetEmail, imap, isResolved, resolve, reject);
              });
              return;
            }

            console.log(`📧 找到 ${results?.length || 0} 封相关邮件`);
            this.processSearchResults(results, targetEmail, imap, isResolved, resolve, reject);
          });
        });
      });
    });
  }

  // 带时间过滤的搜索结果处理方法
  processSearchResultsWithFilter(results, targetEmail, imap, isResolved, resolve, reject, sinceTimestamp = null) {
    if (!results || results.length === 0) {
      console.log('📭 没有找到符合条件的邮件');
      if (!isResolved) {
        isResolved = true;
        imap.end();
        resolve([]);
      }
      return;
    }

    const fetch = imap.fetch(results, {
      bodies: '',
      struct: true,
      markSeen: false
    });

    const emails = [];
    const targetEmailsWithCodes = []; // 专门存储目标邮箱的验证码邮件
    let processedCount = 0;
    const totalEmails = results.length;

    // 设置邮件处理超时
    const fetchTimeout = setTimeout(() => {
      if (!isResolved) {
        isResolved = true;
        console.error('⏰ 邮件获取超时');
        imap.end();
        reject(new Error('邮件获取超时'));
      }
    }, 20000); // 20秒获取超时

    fetch.on('message', (msg, seqno) => {
      msg.on('body', (stream, info) => {
        simpleParser(stream, (err, parsed) => {
          if (err) {
            console.error('❌ 解析邮件失败:', err);
            processedCount++;
            return;
          }

          console.log(`\n=== 处理邮件 ${seqno} (${processedCount + 1}/${totalEmails}) ===`);
          console.log('📧 邮件主题:', parsed.subject);
          console.log('📧 发件人:', parsed.from?.text);
          console.log('📧 收件人:', parsed.to?.text);
          console.log('📧 邮件时间:', parsed.date);

          const emailData = {
            subject: parsed.subject,
            from: parsed.from?.text,
            to: parsed.to?.text,
            date: parsed.date,
            text: parsed.text,
            html: parsed.html
          };

          // 如果设置了时间过滤，检查邮件时间
          if (sinceTimestamp && emailData.date) {
            const emailTimestamp = new Date(emailData.date).getTime();
            if (emailTimestamp < sinceTimestamp) {
              console.log(`⏰ 邮件时间早于过滤时间，跳过: ${emailData.date}`);
              processedCount++;
              return;
            } else {
              console.log(`✅ 邮件时间符合过滤条件: ${emailData.date}`);
            }
          }

          emails.push(emailData);

          // 检查邮件是否与目标邮箱相关，并获取相关性评分
          const relevanceScore = this.getEmailRelevanceScore(emailData, targetEmail);
          console.log(`📧 邮件相关性评分: ${relevanceScore} (邮箱: ${targetEmail})`);

          if (relevanceScore === 0) {
            console.log('❌ 邮件与目标邮箱无关，跳过');
            processedCount++;
            return;
          }

          // 尝试从文本和HTML内容中提取验证码
          let verificationCode = null;

          // 首先尝试从纯文本内容提取
          if (parsed.text) {
            console.log('🔍 尝试从纯文本内容提取验证码...');
            verificationCode = this.extractVerificationCode(parsed.text);
          }

          // 如果纯文本没有找到，尝试从HTML内容提取
          if (!verificationCode && parsed.html) {
            console.log('🔍 尝试从HTML内容提取验证码...');
            // 移除HTML标签，只保留文本内容
            const htmlText = parsed.html.replace(/<[^>]*>/g, ' ').replace(/\s+/g, ' ').trim();
            verificationCode = this.extractVerificationCode(htmlText);
          }

          // 如果还没找到，尝试直接从HTML中提取
          if (!verificationCode && parsed.html) {
            console.log('🔍 尝试从原始HTML内容提取验证码...');
            verificationCode = this.extractVerificationCode(parsed.html);
          }

          if (verificationCode) {
            console.log(`✅ 找到验证码: ${verificationCode} (邮箱: ${targetEmail}, 相关性: ${relevanceScore})`);

            // 将验证码邮件添加到专门的数组中，按相关性和时间排序
            targetEmailsWithCodes.push({
              code: verificationCode,
              timestamp: emailData.date || new Date(),
              email: emailData,
              relevanceScore: relevanceScore
            });
          } else {
            console.log('❌ 未在此邮件中找到验证码');
          }

          processedCount++;
        });
      });
    });

    fetch.once('end', () => {
      clearTimeout(fetchTimeout);
      console.log(`📊 邮件获取完成，处理了 ${processedCount}/${totalEmails} 封邮件`);

      // 如果找到了验证码邮件，选择最相关的一个
      if (targetEmailsWithCodes.length > 0) {
        // 按相关性评分降序排序，相关性相同时按时间降序排序
        targetEmailsWithCodes.sort((a, b) => {
          if (a.relevanceScore !== b.relevanceScore) {
            return b.relevanceScore - a.relevanceScore; // 相关性高的优先
          }
          return new Date(b.timestamp) - new Date(a.timestamp); // 时间新的优先
        });

        const bestMatch = targetEmailsWithCodes[0];
        console.log(`🎯 选择最佳匹配验证码: ${bestMatch.code} (相关性: ${bestMatch.relevanceScore}, 时间: ${bestMatch.timestamp})`);

        // 存储最佳匹配的验证码
        this.verificationCodes.set(targetEmail, {
          code: bestMatch.code,
          timestamp: new Date(),
          email: bestMatch.email,
          relevanceScore: bestMatch.relevanceScore
        });

        console.log(`📋 已缓存验证码: ${bestMatch.code} (邮箱: ${targetEmail})`);
      }

      // 缓存邮件
      const existingEmails = this.emailCache.get(targetEmail) || [];
      this.emailCache.set(targetEmail, [...existingEmails, ...emails]);

      if (!isResolved) {
        isResolved = true;
        imap.end();
        resolve(emails);
      }
    });

    fetch.once('error', (err) => {
      clearTimeout(fetchTimeout);
      console.error('❌ 获取邮件失败:', err);
      if (!isResolved) {
        isResolved = true;
        imap.end();
        reject(err);
      }
    });
  }

  // 处理搜索结果的独立方法（原版本，保持向后兼容）
  processSearchResults(results, targetEmail, imap, isResolved, resolve, reject) {
    if (!results || results.length === 0) {
      console.log('📭 没有找到符合条件的邮件');
      if (!isResolved) {
        isResolved = true;
        imap.end();
        resolve([]);
      }
      return;
    }

    const fetch = imap.fetch(results, {
      bodies: '',
      struct: true,
      markSeen: false
    });

    const emails = [];
    const targetEmailsWithCodes = []; // 专门存储目标邮箱的验证码邮件
    let processedCount = 0;
    const totalEmails = results.length;

    // 设置邮件处理超时
    const fetchTimeout = setTimeout(() => {
      if (!isResolved) {
        isResolved = true;
        console.error('⏰ 邮件获取超时');
        imap.end();
        reject(new Error('邮件获取超时'));
      }
    }, 20000); // 20秒获取超时

    fetch.on('message', (msg, seqno) => {
      msg.on('body', (stream, info) => {
        simpleParser(stream, (err, parsed) => {
          if (err) {
            console.error('❌ 解析邮件失败:', err);
            processedCount++;
            return;
          }

          console.log(`\n=== 处理邮件 ${seqno} (${processedCount + 1}/${totalEmails}) ===`);
          console.log('📧 邮件主题:', parsed.subject);
          console.log('📧 发件人:', parsed.from?.text);
          console.log('📧 收件人:', parsed.to?.text);

          const emailData = {
            subject: parsed.subject,
            from: parsed.from?.text,
            to: parsed.to?.text,
            date: parsed.date,
            text: parsed.text,
            html: parsed.html
          };

          emails.push(emailData);

          // 检查邮件是否与目标邮箱相关，并获取相关性评分
          const relevanceScore = this.getEmailRelevanceScore(emailData, targetEmail);
          console.log(`📧 邮件相关性评分: ${relevanceScore} (邮箱: ${targetEmail})`);

          if (relevanceScore === 0) {
            console.log('❌ 邮件与目标邮箱无关，跳过');
            processedCount++;
            return;
          }

          // 尝试从文本和HTML内容中提取验证码
          let verificationCode = null;

          // 首先尝试从纯文本内容提取
          if (parsed.text) {
            console.log('🔍 尝试从纯文本内容提取验证码...');
            verificationCode = this.extractVerificationCode(parsed.text);
          }

          // 如果纯文本没有找到，尝试从HTML内容提取
          if (!verificationCode && parsed.html) {
            console.log('🔍 尝试从HTML内容提取验证码...');
            // 移除HTML标签，只保留文本内容
            const htmlText = parsed.html.replace(/<[^>]*>/g, ' ').replace(/\s+/g, ' ').trim();
            verificationCode = this.extractVerificationCode(htmlText);
          }

          // 如果还没找到，尝试直接从HTML中提取
          if (!verificationCode && parsed.html) {
            console.log('🔍 尝试从原始HTML内容提取验证码...');
            verificationCode = this.extractVerificationCode(parsed.html);
          }

          if (verificationCode) {
            console.log(`✅ 找到验证码: ${verificationCode} (邮箱: ${targetEmail}, 相关性: ${relevanceScore})`);

            // 将验证码邮件添加到专门的数组中，按相关性和时间排序
            targetEmailsWithCodes.push({
              code: verificationCode,
              timestamp: emailData.date || new Date(),
              email: emailData,
              relevanceScore: relevanceScore
            });
          } else {
            console.log('❌ 未在此邮件中找到验证码');
          }

          processedCount++;
        });
      });
    });

    fetch.once('end', () => {
      clearTimeout(fetchTimeout);
      console.log(`📊 邮件获取完成，处理了 ${processedCount}/${totalEmails} 封邮件`);

      // 如果找到了验证码邮件，选择最相关的一个
      if (targetEmailsWithCodes.length > 0) {
        // 按相关性评分降序排序，相关性相同时按时间降序排序
        targetEmailsWithCodes.sort((a, b) => {
          if (a.relevanceScore !== b.relevanceScore) {
            return b.relevanceScore - a.relevanceScore; // 相关性高的优先
          }
          return new Date(b.timestamp) - new Date(a.timestamp); // 时间新的优先
        });

        const bestMatch = targetEmailsWithCodes[0];
        console.log(`🎯 选择最佳匹配验证码: ${bestMatch.code} (相关性: ${bestMatch.relevanceScore}, 时间: ${bestMatch.timestamp})`);

        // 存储最佳匹配的验证码
        this.verificationCodes.set(targetEmail, {
          code: bestMatch.code,
          timestamp: new Date(),
          email: bestMatch.email,
          relevanceScore: bestMatch.relevanceScore
        });

        console.log(`📋 已缓存验证码: ${bestMatch.code} (邮箱: ${targetEmail})`);
      }

      // 缓存邮件
      const existingEmails = this.emailCache.get(targetEmail) || [];
      this.emailCache.set(targetEmail, [...existingEmails, ...emails]);

      if (!isResolved) {
        isResolved = true;
        imap.end();
        resolve(emails);
      }
    });

    fetch.once('error', (err) => {
      clearTimeout(fetchTimeout);
      console.error('❌ 获取邮件失败:', err);
      if (!isResolved) {
        isResolved = true;
        imap.end();
        reject(err);
      }
    });
  }

  // 带时间过滤的邮件检查方法
  async checkEmailForUserWithFilter(targetEmail, sinceTimestamp = null) {
    console.log(`🔍 开始带时间过滤的邮件检查 - 邮箱: ${targetEmail}`);
    if (sinceTimestamp) {
      console.log(`⏰ 时间过滤: 只检查 ${new Date(sinceTimestamp).toISOString()} 之后的邮件`);
    }

    return new Promise((resolve, reject) => {
      const imap = new Imap(this.imapConfig);
      let isResolved = false;

      // 设置连接超时
      const connectionTimeout = setTimeout(() => {
        if (!isResolved) {
          isResolved = true;
          console.error('⏰ IMAP连接超时');
          imap.destroy();
          reject(new Error('IMAP连接超时'));
        }
      }, 30000); // 30秒超时

      imap.once('ready', () => {
        console.log('🔗 IMAP连接成功');
        clearTimeout(connectionTimeout);

        imap.openBox('INBOX', false, (err, box) => {
          if (err) {
            if (!isResolved) {
              isResolved = true;
              console.error('❌ 打开收件箱失败:', err);
              imap.end();
              reject(err);
            }
            return;
          }

          console.log(`📬 收件箱包含 ${box.messages.total} 封邮件`);

          // 设置搜索时间范围
          let searchSince;
          if (sinceTimestamp) {
            searchSince = new Date(sinceTimestamp);
            console.log(`🔍 搜索 ${searchSince.toISOString()} 之后的邮件`);
          } else {
            searchSince = new Date(Date.now() - 30 * 60 * 1000); // 默认30分钟
            console.log(`🔍 搜索最近30分钟的邮件`);
          }

          // 改进的搜索条件：按时间和邮箱地址搜索
          const searchCriteria = [
            ['SINCE', searchSince],
            // 尝试多种搜索条件来匹配目标邮箱
            'OR',
            ['TO', targetEmail],
            ['CC', targetEmail],
            ['BCC', targetEmail],
            // 也搜索邮件内容包含目标邮箱的情况（用于转发邮件）
            ['BODY', targetEmail]
          ];

          console.log(`🔍 搜索条件: ${searchSince.toISOString()} 之后与邮箱 ${targetEmail} 相关的邮件`);

          // 设置搜索超时
          const searchTimeout = setTimeout(() => {
            if (!isResolved) {
              isResolved = true;
              console.error('⏰ 邮件搜索超时');
              imap.end();
              reject(new Error('邮件搜索超时'));
            }
          }, 15000); // 15秒搜索超时

          imap.search(searchCriteria, (err, results) => {
            clearTimeout(searchTimeout);

            if (err) {
              console.warn('⚠️ 复杂搜索失败，尝试简单搜索:', err.message);
              // 如果复杂搜索失败，回退到简单的时间搜索
              const fallbackCriteria = [['SINCE', searchSince]];

              imap.search(fallbackCriteria, (fallbackErr, fallbackResults) => {
                if (fallbackErr) {
                  if (!isResolved) {
                    isResolved = true;
                    console.error('❌ 搜索邮件失败:', fallbackErr);
                    imap.end();
                    reject(fallbackErr);
                  }
                  return;
                }

                console.log(`📧 回退搜索找到 ${fallbackResults?.length || 0} 封邮件`);
                this.processSearchResultsWithFilter(fallbackResults, targetEmail, imap, isResolved, resolve, reject, sinceTimestamp);
              });
              return;
            }

            console.log(`📧 找到 ${results?.length || 0} 封相关邮件`);
            this.processSearchResultsWithFilter(results, targetEmail, imap, isResolved, resolve, reject, sinceTimestamp);
          });
        });
      });

      imap.once('error', (err) => {
        clearTimeout(connectionTimeout);
        console.error('❌ IMAP连接失败:', err);
        if (!isResolved) {
          isResolved = true;
          reject(err);
        }
      });

      imap.once('end', () => {
        console.log('🔗 IMAP连接已关闭');
      });

      // 添加连接前的日志
      console.log(`🔗 正在连接IMAP服务器: ${this.imapConfig.host}:${this.imapConfig.port}`);
      imap.connect();
    });
  }

  // 检查邮件是否与目标邮箱相关（保留向后兼容性）
  isEmailRelevantToTarget(emailData, targetEmail) {
    return this.getEmailRelevanceScore(emailData, targetEmail) > 0;
  }



  // 计算邮件与目标邮箱的相关性评分（修复版本）
  getEmailRelevanceScore(emailData, targetEmail) {
    let score = 0;
    const targetEmailLower = targetEmail.toLowerCase();
    const targetUsername = targetEmail.split('@')[0].toLowerCase();
    const targetDomain = targetEmail.split('@')[1].toLowerCase();

    console.log(`🔍 计算邮件相关性评分 - 目标邮箱: ${targetEmail}`);

    // 检查收件人字段 (最高权重) - 必须精确匹配邮箱地址
    if (emailData.to) {
      const toLower = emailData.to.toLowerCase();

      // 精确匹配完整邮箱地址
      if (toLower.includes(targetEmailLower)) {
        score += 50;
        console.log(`✅ 收件人匹配完整邮箱 (+50): ${emailData.to}`);
      }
      // 用户名匹配需要更严格的条件：必须是独立的词，且同域名
      else if (toLower.includes(targetUsername) && toLower.includes(targetDomain)) {
        // 检查用户名是否作为独立词出现（前后有分隔符）
        const usernameRegex = new RegExp(`\\b${targetUsername.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}\\b`);
        if (usernameRegex.test(toLower)) {
          score += 25; // 降低分数，因为不是完整匹配
          console.log(`✅ 收件人匹配用户名且同域 (+25): ${emailData.to}`);
        }
      }
    }

    // 检查抄送字段 - 同样严格匹配
    if (emailData.cc) {
      const ccLower = emailData.cc.toLowerCase();
      if (ccLower.includes(targetEmailLower)) {
        score += 40;
        console.log(`✅ 抄送匹配完整邮箱 (+40): ${emailData.cc}`);
      } else if (ccLower.includes(targetUsername) && ccLower.includes(targetDomain)) {
        const usernameRegex = new RegExp(`\\b${targetUsername.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}\\b`);
        if (usernameRegex.test(ccLower)) {
          score += 20; // 降低分数
          console.log(`✅ 抄送匹配用户名且同域 (+20): ${emailData.cc}`);
        }
      }
    }

    // 检查邮件内容 (中等权重) - 完整邮箱地址优先
    const allContent = ((emailData.text || '') + ' ' + (emailData.html || '')).toLowerCase();
    if (allContent.includes(targetEmailLower)) {
      score += 30; // 提高完整邮箱匹配的分数
      console.log(`✅ 邮件内容匹配完整邮箱 (+30)`);
    } else if (allContent.includes(targetUsername)) {
      // 用户名匹配分数大幅降低，避免误匹配
      score += 5;
      console.log(`✅ 邮件内容匹配用户名 (+5)`);
    }

    // 检查邮件主题 (中等权重)
    if (emailData.subject) {
      const subjectLower = emailData.subject.toLowerCase();
      if (subjectLower.includes(targetEmailLower)) {
        score += 15;
        console.log(`✅ 主题匹配完整邮箱 (+15): ${emailData.subject}`);
      } else if (subjectLower.includes(targetUsername)) {
        score += 3; // 大幅降低用户名匹配分数
        console.log(`✅ 主题匹配用户名 (+3): ${emailData.subject}`);
      }
    }

    // 检查发件人域名 (低权重，用于Microsoft等官方邮件)
    if (emailData.from) {
      const fromLower = emailData.from.toLowerCase();
      const officialDomains = ['microsoft.com', 'outlook.com', 'live.com', 'hotmail.com'];

      for (const domain of officialDomains) {
        if (fromLower.includes(domain)) {
          score += 5;
          console.log(`✅ 发件人为官方域名 (+5): ${domain}`);
          break;
        }
      }
    }

    // 检查是否是验证码相关的邮件 (额外加分)
    const verificationKeywords = ['verification', 'verify', '验证', '验证码', 'code', 'confirm', '确认', 'security code', '安全代码'];
    const subjectAndContent = ((emailData.subject || '') + ' ' + allContent).toLowerCase();

    for (const keyword of verificationKeywords) {
      if (subjectAndContent.includes(keyword)) {
        score += 3;
        console.log(`✅ 包含验证相关关键词 (+3): ${keyword}`);
        break; // 只加一次分
      }
    }

    console.log(`📊 最终相关性评分: ${score}`);
    return score;
  }

  // 从邮件内容中提取验证码
  extractVerificationCode(content) {
    if (!content) return null;

    console.log('开始提取验证码...');
    console.log('邮件内容长度:', content.length);
    console.log('邮件内容预览:', content.substring(0, 300));

    // 多种验证码格式的正则表达式
    const patterns = [
      /安全代码[:\s]*(\d{6})/i,
      /验证码[:\s]*(\d{6})/i,
      /verification code[:\s]*(\d{6})/i,
      /security code[:\s]*(\d{6})/i,
      /your code is[:\s]*(\d{6})/i,
      /code[:\s]*(\d{6})/i,
      // Microsoft特定格式
      /使用此安全代码[:\s]*(\d{6})/i,
      /请使用此安全代码[:\s]*(\d{6})/i,
      // HTML中的验证码格式
      /<span[^>]*>(\d{6})<\/span>/i,
      // 通用6位数字（作为最后的备选）
      /(\d{6})/g
    ];

    for (let i = 0; i < patterns.length; i++) {
      const pattern = patterns[i];
      console.log(`测试正则表达式 ${i + 1}: ${pattern}`);

      const match = content.match(pattern);
      if (match) {
        console.log('匹配结果:', match);
        if (match[1]) {
          const code = match[1];
          console.log('提取的代码:', code);
          // 验证是否为有效的6位数字
          if (/^\d{6}$/.test(code)) {
            // 对于通用6位数字正则，需要额外验证
            if (i === patterns.length - 1) {
              // 获取所有6位数字匹配
              const allMatches = [...content.matchAll(/(\d{6})/g)];
              console.log('找到所有6位数字:', allMatches.map(m => m[1]));

              // 检查每个6位数字的上下文
              for (const singleMatch of allMatches) {
                const singleCode = singleMatch[1];
                const matchIndex = singleMatch.index;

                console.log(`检查6位数字: ${singleCode} (位置: ${matchIndex})`);

                const contextBefore = content.substring(Math.max(0, matchIndex - 100), matchIndex);
                const contextAfter = content.substring(matchIndex + singleCode.length, Math.min(content.length, matchIndex + singleCode.length + 50));
                console.log('上下文前:', contextBefore.substring(contextBefore.length - 50));
                console.log('上下文后:', contextAfter.substring(0, 50));

                // 如果上下文中包含验证码相关词汇，则认为是有效的
                // 包括编码有问题的中文字符
                const codeKeywords = [
                  '安全代码', '验证码', 'code', '代码', 'verification', 'security',
                  // 编码有问题的中文字符
                  '瀹夊叏浠ｇ爜', '楠岃瘉鐮�', '浠ｇ爜',
                  // Microsoft相关
                  'microsoft', 'account', '帐户', '甯愭埛',
                  // 其他可能的关键词
                  '使用', '请使用', '浣跨敤'
                ];
                const hasCodeContext = codeKeywords.some(keyword =>
                  contextBefore.toLowerCase().includes(keyword.toLowerCase()) ||
                  contextAfter.toLowerCase().includes(keyword.toLowerCase())
                );

                // 额外检查：如果是Microsoft邮件且包含冒号，很可能是验证码
                const isMicrosoftEmail = content.toLowerCase().includes('microsoft');
                const hasColonBefore = contextBefore.includes(':') || contextBefore.includes('：');

                // 优先级检查：冒号紧邻的数字优先级更高
                const hasImmediateColon = contextBefore.trim().endsWith(':') || contextBefore.trim().endsWith('：');

                if (hasCodeContext || (isMicrosoftEmail && hasColonBefore)) {
                  console.log(`✅ 在验证码上下文中找到6位数字: ${singleCode} (优先级: ${hasImmediateColon ? '高' : '普通'})`);

                  // 如果有紧邻冒号的，优先返回
                  if (hasImmediateColon) {
                    return singleCode;
                  }

                  // 否则记录为候选
                  if (!code || hasImmediateColon) {
                    code = singleCode;
                  }
                } else {
                  console.log(`❌ 6位数字不在验证码上下文中，跳过: ${singleCode}`);
                }
              }

              if (code) {
                console.log('✅ 最终选择的验证码:', code);
                return code;
              }
            } else {
              console.log('✅ 找到有效验证码:', code);
              return code;
            }
          } else {
            console.log('❌ 代码格式无效:', code);
          }
        }
      } else {
        console.log('无匹配');
      }
    }

    console.log('❌ 未找到验证码');
    return null;
  }

  // 清理旧验证码缓存
  clearOldVerificationCodes(email, timestamp) {
    console.log(`🧹 清理邮箱 ${email} 的旧验证码缓存`);

    let clearedCount = 0;

    // 清理指定邮箱的验证码缓存
    if (this.verificationCodes.has(email)) {
      this.verificationCodes.delete(email);
      clearedCount++;
      console.log(`✅ 已清理邮箱 ${email} 的验证码缓存`);
    }

    // 清理所有过期的验证码缓存
    const now = Date.now();
    for (const [cachedEmail, cacheData] of this.verificationCodes.entries()) {
      const age = now - cacheData.timestamp.getTime();
      if (age > CONSTANTS.VERIFICATION_CODE_TTL) {
        this.verificationCodes.delete(cachedEmail);
        clearedCount++;
        console.log(`✅ 已清理过期验证码缓存: ${cachedEmail}`);
      }
    }

    console.log(`🧹 清理完成，共清理 ${clearedCount} 个验证码缓存`);
    return clearedCount;
  }

  // 验证验证码是否属于请求的账号（修复版本）
  validateCodeForAccount(codeResult, requestedEmail) {
    if (!codeResult || !codeResult.email) {
      console.log('⚠️ 验证码结果缺少邮件信息');
      return false;
    }

    // 检查邮件的相关性评分
    const relevanceScore = codeResult.relevanceScore || 0;
    console.log(`🔍 验证码账号匹配检查: 请求邮箱=${requestedEmail}, 相关性评分=${relevanceScore}`);

    // 提高验证阈值：相关性评分大于等于40分才认为是匹配的
    // 这确保至少需要完整邮箱地址匹配或高质量的部分匹配
    if (relevanceScore >= 40) {
      console.log('✅ 验证码账号匹配验证通过');
      return true;
    } else {
      console.log(`❌ 验证码账号匹配验证失败，相关性评分过低 (${relevanceScore} < 40)`);

      // 详细记录邮件信息用于调试
      if (codeResult.email) {
        console.log(`📧 邮件详情:`);
        console.log(`   收件人: ${codeResult.email.to || 'N/A'}`);
        console.log(`   抄送: ${codeResult.email.cc || 'N/A'}`);
        console.log(`   主题: ${codeResult.email.subject || 'N/A'}`);
        console.log(`   发件人: ${codeResult.email.from || 'N/A'}`);
      }

      return false;
    }
  }

  // 获取验证码（带时间过滤的增强版本）
  async getVerificationCodeWithFilter(email, sinceTimestamp = null) {
    console.log(`🔍 获取验证码请求 - 邮箱: ${email}`);
    if (sinceTimestamp) {
      console.log(`⏰ 时间过滤: 只获取 ${new Date(sinceTimestamp).toISOString()} 之后的验证码`);
    }

    // 首先检查缓存
    const cached = this.verificationCodes.get(email);
    if (cached) {
      // 检查验证码是否还有效（5分钟内）
      const age = Date.now() - cached.timestamp.getTime();
      console.log(`📋 缓存中的验证码年龄: ${Math.floor(age / 1000)}秒`);

      // 如果设置了时间过滤，检查验证码是否在指定时间之后
      if (sinceTimestamp && cached.email && cached.email.date) {
        const emailTimestamp = new Date(cached.email.date).getTime();
        if (emailTimestamp < sinceTimestamp) {
          console.log(`⏰ 缓存的验证码时间早于过滤时间，忽略缓存`);
        } else if (age < CONSTANTS.VERIFICATION_CODE_TTL) {
          console.log(`🎉 ===== 从缓存返回验证码（时间过滤通过）=====`);
          console.log(`📋 验证码: ${cached.code}`);
          console.log(`⏰ 邮件时间戳: ${cached.email.date}`);
          console.log(`🎉 =======================================`);
          return {
            code: cached.code,
            emailTimestamp: cached.email.date,
            email: cached.email,
            relevanceScore: cached.relevanceScore
          };
        }
      } else if (!sinceTimestamp && age < CONSTANTS.VERIFICATION_CODE_TTL) {
        console.log(`🎉 ===== 从缓存返回验证码 =====`);
        console.log(`📋 验证码: ${cached.code}`);
        console.log(`⏰ 时间戳: ${cached.timestamp}`);
        console.log(`🎉 ===========================`);
        return {
          code: cached.code,
          emailTimestamp: cached.email ? cached.email.date : cached.timestamp,
          email: cached.email,
          relevanceScore: cached.relevanceScore
        };
      } else {
        // 验证码过期，删除缓存
        console.log(`⏰ 验证码已过期，删除缓存`);
        this.verificationCodes.delete(email);
      }
    } else {
      console.log(`📋 缓存中没有找到验证码`);
    }

    // 检查新邮件（带重试机制和时间过滤）
    const maxRetries = 3;
    let retryCount = 0;

    while (retryCount < maxRetries) {
      try {
        console.log(`📧 开始检查新邮件... (尝试 ${retryCount + 1}/${maxRetries})`);
        await this.checkEmailForUserWithFilter(email, sinceTimestamp);

        // 再次检查缓存
        const newCached = this.verificationCodes.get(email);
        if (newCached) {
          console.log(`🎉 ===== 检查邮件后获得验证码 =====`);
          console.log(`📋 验证码: ${newCached.code}`);
          console.log(`⏰ 邮件时间戳: ${newCached.email ? newCached.email.date : newCached.timestamp}`);
          console.log(`🎉 ===============================`);
          return {
            code: newCached.code,
            emailTimestamp: newCached.email ? newCached.email.date : newCached.timestamp,
            email: newCached.email,
            relevanceScore: newCached.relevanceScore
          };
        } else {
          console.log(`❌ 检查邮件后仍未找到验证码 (尝试 ${retryCount + 1})`);
        }

        // 如果没找到验证码，等待一段时间再重试
        if (retryCount < maxRetries - 1) {
          const waitTime = (retryCount + 1) * 2000; // 递增等待时间：2s, 4s, 6s
          console.log(`⏳ 等待 ${waitTime}ms 后重试...`);
          await new Promise(resolve => setTimeout(resolve, waitTime));
        }

      } catch (error) {
        console.error(`❌ 检查邮件失败 (尝试 ${retryCount + 1}):`, error);

        // 如果是连接错误，等待更长时间再重试
        if (retryCount < maxRetries - 1) {
          const waitTime = (retryCount + 1) * 3000; // 递增等待时间：3s, 6s, 9s
          console.log(`⏳ 连接错误，等待 ${waitTime}ms 后重试...`);
          await new Promise(resolve => setTimeout(resolve, waitTime));
        }
      }

      retryCount++;
    }

    console.log(`❌ 最终未找到验证码 (已重试 ${maxRetries} 次)`);
    return null;
  }

  // 获取验证码（原版本，保持向后兼容）
  async getVerificationCode(email) {
    console.log(`🔍 获取验证码请求 - 邮箱: ${email}`);

    // 首先检查缓存
    const cached = this.verificationCodes.get(email);
    if (cached) {
      // 检查验证码是否还有效（5分钟内）
      const age = Date.now() - cached.timestamp.getTime();
      console.log(`📋 缓存中的验证码年龄: ${Math.floor(age / 1000)}秒`);

      if (age < 5 * 60 * 1000) {
        console.log(`🎉 ===== 从缓存返回验证码 =====`);
        console.log(`📋 验证码: ${cached.code}`);
        console.log(`⏰ 时间戳: ${cached.timestamp}`);
        console.log(`🎉 ===========================`);
        return cached.code;
      } else {
        // 验证码过期，删除缓存
        console.log(`⏰ 验证码已过期，删除缓存`);
        this.verificationCodes.delete(email);
      }
    } else {
      console.log(`📋 缓存中没有找到验证码`);
    }

    // 检查新邮件（带重试机制）
    const maxRetries = 3;
    let retryCount = 0;

    while (retryCount < maxRetries) {
      try {
        console.log(`📧 开始检查新邮件... (尝试 ${retryCount + 1}/${maxRetries})`);
        await this.checkEmailForUser(email);

        // 再次检查缓存
        const newCached = this.verificationCodes.get(email);
        if (newCached) {
          console.log(`🎉 ===== 检查邮件后获得验证码 =====`);
          console.log(`📋 验证码: ${newCached.code}`);
          console.log(`⏰ 时间戳: ${newCached.timestamp}`);
          console.log(`🎉 ===============================`);
          return newCached.code;
        } else {
          console.log(`❌ 检查邮件后仍未找到验证码 (尝试 ${retryCount + 1})`);
        }

        // 如果没找到验证码，等待一段时间再重试
        if (retryCount < maxRetries - 1) {
          const waitTime = (retryCount + 1) * 2000; // 递增等待时间：2s, 4s, 6s
          console.log(`⏳ 等待 ${waitTime}ms 后重试...`);
          await new Promise(resolve => setTimeout(resolve, waitTime));
        }

      } catch (error) {
        console.error(`❌ 检查邮件失败 (尝试 ${retryCount + 1}):`, error);

        // 如果是连接错误，等待更长时间再重试
        if (retryCount < maxRetries - 1) {
          const waitTime = (retryCount + 1) * 3000; // 递增等待时间：3s, 6s, 9s
          console.log(`⏳ 连接错误，等待 ${waitTime}ms 后重试...`);
          await new Promise(resolve => setTimeout(resolve, waitTime));
        }
      }

      retryCount++;
    }

    console.log(`❌ 最终未找到验证码 (已重试 ${maxRetries} 次)`);
    return null;
  }

  // 删除验证码邮件
  async deleteVerificationEmail(targetEmail) {
    console.log(`🗑️ 开始删除验证码邮件 - 邮箱: ${targetEmail}`);

    return new Promise((resolve, reject) => {
      const imap = new Imap(this.imapConfig);

      imap.once('ready', () => {
        console.log('🔗 IMAP连接成功，准备删除邮件');

        imap.openBox('INBOX', false, (err, box) => {
          if (err) {
            console.error('❌ 打开收件箱失败:', err);
            reject(err);
            return;
          }

          console.log(`📬 收件箱包含 ${box.messages.total} 封邮件`);

          // 搜索最近30分钟内的验证码邮件
          const searchCriteria = [
            ['SINCE', new Date(Date.now() - 30 * 60 * 1000)]
          ];

          console.log(`🔍 搜索最近30分钟内的邮件进行删除 (目标邮箱: ${targetEmail})`);

          imap.search(searchCriteria, (err, results) => {
            if (err) {
              console.error('❌ 搜索邮件失败:', err);
              imap.end();
              reject(err);
              return;
            }

            if (!results || results.length === 0) {
              console.log('📭 没有找到符合条件的邮件');
              imap.end();
              resolve({ deleted: 0, message: '没有找到符合条件的邮件' });
              return;
            }

            console.log(`📧 找到 ${results.length} 封邮件，开始检查验证码邮件`);

            const fetch = imap.fetch(results, {
              bodies: '',
              struct: true,
              markSeen: false
            });

            const emailsToDelete = [];
            let processedCount = 0;

            fetch.on('message', (msg, seqno) => {
              msg.on('body', (stream, info) => {
                simpleParser(stream, (err, parsed) => {
                  if (err) {
                    console.error('❌ 解析邮件失败:', err);
                    return;
                  }

                  console.log(`\n=== 检查邮件 ${seqno} 是否为验证码邮件 ===`);
                  console.log('📧 邮件主题:', parsed.subject);
                  console.log('📧 发件人:', parsed.from?.text);
                  console.log('📧 收件人:', parsed.to?.text);

                  const emailData = {
                    subject: parsed.subject,
                    from: parsed.from?.text,
                    to: parsed.to?.text,
                    date: parsed.date,
                    text: parsed.text,
                    html: parsed.html
                  };

                  // 检查邮件是否与目标邮箱相关且包含验证码
                  const isRelevantEmail = this.isEmailRelevantToTarget(emailData, targetEmail);
                  console.log(`📧 邮件是否与目标邮箱相关: ${isRelevantEmail}`);

                  if (isRelevantEmail) {
                    // 检查是否包含验证码
                    const allContent = (emailData.text || '') + ' ' + (emailData.html || '');
                    const verificationCode = this.extractVerificationCode(allContent);

                    if (verificationCode) {
                      console.log(`✅ 找到验证码邮件，标记删除: ${verificationCode} (序号: ${seqno})`);
                      emailsToDelete.push(seqno);
                    } else {
                      console.log(`❌ 邮件不包含验证码，跳过删除`);
                    }
                  } else {
                    console.log(`❌ 邮件与目标邮箱无关，跳过删除`);
                  }

                  processedCount++;
                });
              });
            });

            fetch.once('end', () => {
              console.log(`📊 邮件检查完成，处理了 ${processedCount} 封邮件`);
              console.log(`🗑️ 需要删除的邮件数量: ${emailsToDelete.length}`);

              if (emailsToDelete.length === 0) {
                console.log('📭 没有找到需要删除的验证码邮件');
                imap.end();
                resolve({ deleted: 0, message: '没有找到需要删除的验证码邮件' });
                return;
              }

              // 标记邮件为删除
              console.log(`🏷️ 标记 ${emailsToDelete.length} 封邮件为删除状态`);
              imap.seq.addFlags(emailsToDelete, '\\Deleted', (err) => {
                if (err) {
                  console.error('❌ 标记邮件删除失败:', err);
                  imap.end();
                  reject(err);
                  return;
                }

                console.log('✅ 邮件已标记为删除状态');

                // 执行expunge操作永久删除邮件
                console.log('🗑️ 执行expunge操作永久删除邮件');
                imap.expunge((err) => {
                  if (err) {
                    console.error('❌ Expunge操作失败:', err);
                    imap.end();
                    reject(err);
                    return;
                  }

                  console.log(`✅ 成功删除 ${emailsToDelete.length} 封验证码邮件`);
                  imap.end();
                  resolve({
                    deleted: emailsToDelete.length,
                    message: `成功删除 ${emailsToDelete.length} 封验证码邮件`
                  });
                });
              });
            });

            fetch.once('error', (err) => {
              console.error('❌ 获取邮件失败:', err);
              imap.end();
              reject(err);
            });
          });
        });
      });

      imap.once('error', (err) => {
        console.error('❌ IMAP连接失败:', err);
        reject(err);
      });

      imap.once('end', () => {
        console.log('🔗 IMAP连接已关闭');
      });

      imap.connect();
    });
  }

  // 启动服务器
  start() {
    this.app.listen(this.port, () => {
      console.log(`🚀 RTBS IMAP服务器已启动`);
      console.log(`📧 服务地址: http://localhost:${this.port}`);
      console.log(`📋 健康检查: http://localhost:${this.port}/health`);
      console.log(`⚙️  配置信息: http://localhost:${this.port}/config`);
      console.log(`📨 IMAP服务器: ${this.imapConfig.host}:${this.imapConfig.port}`);
      console.log(`👤 IMAP用户: ${this.imapConfig.user}`);
      console.log('');
      console.log('API端点:');
      console.log('  GET  /verification-code/:email - 获取验证码');
      console.log('  POST /check-email/:email - 手动检查邮件');
      console.log('  GET  /emails/:email - 获取邮件列表');
      console.log('  GET  /health - 健康检查');
      console.log('  GET  /config - 配置信息');
    });
  }

  // 更新IMAP配置
  updateConfig(newConfig) {
    this.imapConfig = { ...this.imapConfig, ...newConfig };
    console.log('IMAP配置已更新:', this.imapConfig);
  }
}

// 创建并启动服务器
const server = new ImapEmailServer();

// 从配置文件加载配置（如果存在）
const configPath = path.join(__dirname, 'imap-config.json');
if (fs.existsSync(configPath)) {
  try {
    const config = JSON.parse(fs.readFileSync(configPath, 'utf8'));
    server.updateConfig(config);
    console.log('已加载配置文件:', configPath);
  } catch (error) {
    console.error('加载配置文件失败:', error);
  }
}

server.start();

// 导出服务器实例
module.exports = server;
