/**
 * 增强版IMAP服务
 * 基于阶段化处理的邮件服务架构
 * 
 * <AUTHOR> Team
 * @version 3.0.0
 * @description 重构的IMAP服务，支持连接池、资源管理和智能重试
 */

const Imap = require('imap');
const { simpleParser } = require('mailparser');

/**
 * IMAP连接池管理器
 * 管理多个IMAP连接，避免连接冲突
 */
class ImapConnectionPool {
  constructor(config, maxConnections = 3) {
    this.config = config;
    this.maxConnections = maxConnections;
    this.connections = new Map(); // email -> connection
    this.connectionQueue = [];
    this.activeConnections = 0;
  }

  /**
   * 获取或创建连接
   * @param {string} email - 邮箱地址
   * @returns {Promise<Imap>} IMAP连接
   */
  async getConnection(email) {
    // 检查是否已有该邮箱的连接
    if (this.connections.has(email)) {
      const connection = this.connections.get(email);
      if (connection.state === 'authenticated') {
        console.log(`🔗 复用现有连接: ${email}`);
        return connection;
      } else {
        // 连接已断开，清理并重新创建
        this.connections.delete(email);
        this.activeConnections--;
      }
    }

    // 检查连接数限制
    if (this.activeConnections >= this.maxConnections) {
      console.log('⏳ 等待可用连接...');
      await this.waitForAvailableConnection();
    }

    // 创建新连接
    return this.createConnection(email);
  }

  /**
   * 创建新的IMAP连接
   * @param {string} email - 邮箱地址
   * @returns {Promise<Imap>} IMAP连接
   */
  async createConnection(email) {
    return new Promise((resolve, reject) => {
      console.log(`🔗 创建新IMAP连接: ${email}`);
      
      const imap = new Imap(this.config);
      let isResolved = false;

      // 设置连接超时
      const timeout = setTimeout(() => {
        if (!isResolved) {
          isResolved = true;
          imap.destroy();
          reject(new Error('IMAP连接超时'));
        }
      }, 30000);

      imap.once('ready', () => {
        clearTimeout(timeout);
        if (!isResolved) {
          isResolved = true;
          console.log(`✅ IMAP连接成功: ${email}`);
          
          // 添加到连接池
          this.connections.set(email, imap);
          this.activeConnections++;
          
          // 设置连接事件监听
          this.setupConnectionEvents(imap, email);
          
          resolve(imap);
        }
      });

      imap.once('error', (err) => {
        clearTimeout(timeout);
        if (!isResolved) {
          isResolved = true;
          console.error(`❌ IMAP连接失败: ${email}`, err);
          reject(err);
        }
      });

      imap.connect();
    });
  }

  /**
   * 设置连接事件监听
   * @param {Imap} imap - IMAP连接
   * @param {string} email - 邮箱地址
   */
  setupConnectionEvents(imap, email) {
    imap.once('end', () => {
      console.log(`🔗 IMAP连接关闭: ${email}`);
      this.connections.delete(email);
      this.activeConnections--;
      this.processQueue();
    });

    imap.once('error', (err) => {
      console.error(`❌ IMAP连接错误: ${email}`, err);
      this.connections.delete(email);
      this.activeConnections--;
      this.processQueue();
    });
  }

  /**
   * 等待可用连接
   */
  async waitForAvailableConnection() {
    return new Promise((resolve) => {
      this.connectionQueue.push(resolve);
    });
  }

  /**
   * 处理等待队列
   */
  processQueue() {
    if (this.connectionQueue.length > 0 && this.activeConnections < this.maxConnections) {
      const resolve = this.connectionQueue.shift();
      resolve();
    }
  }

  /**
   * 关闭指定邮箱的连接
   * @param {string} email - 邮箱地址
   */
  closeConnection(email) {
    const connection = this.connections.get(email);
    if (connection) {
      console.log(`🔗 关闭连接: ${email}`);
      connection.end();
    }
  }

  /**
   * 关闭所有连接
   */
  closeAllConnections() {
    console.log('🔗 关闭所有IMAP连接...');
    for (const [email, connection] of this.connections) {
      connection.end();
    }
    this.connections.clear();
    this.activeConnections = 0;
    this.connectionQueue = [];
  }
}

/**
 * 邮件处理器
 * 负责邮件解析和验证码提取
 */
class EmailProcessor {
  constructor() {
    this.verificationCodePatterns = [
      /\b(\d{6})\b/g,                    // 6位数字
      /验证码[：:\s]*(\d{6})/g,          // 中文验证码
      /verification code[：:\s]*(\d{6})/gi, // 英文验证码
      /security code[：:\s]*(\d{6})/gi,  // 安全码
      /code[：:\s]*(\d{6})/gi            // 通用码
    ];
  }

  /**
   * 处理邮件并提取验证码
   * @param {Buffer} emailBuffer - 邮件原始数据
   * @param {string} targetEmail - 目标邮箱
   * @returns {Promise<Object>} 处理结果
   */
  async processEmail(emailBuffer, targetEmail) {
    try {
      const parsed = await simpleParser(emailBuffer);
      
      const emailData = {
        subject: parsed.subject || '',
        from: parsed.from?.text || '',
        to: parsed.to?.text || '',
        cc: parsed.cc?.text || '',
        bcc: parsed.bcc?.text || '',
        date: parsed.date || new Date(),
        text: parsed.text || '',
        html: parsed.html || ''
      };

      // 计算相关性评分
      const relevanceScore = this.calculateRelevanceScore(emailData, targetEmail);
      
      // 提取验证码
      const verificationCode = this.extractVerificationCode(emailData);
      
      return {
        success: true,
        email: emailData,
        verificationCode,
        relevanceScore,
        isRelevant: relevanceScore >= 30 // 相关性阈值
      };

    } catch (error) {
      console.error('❌ 邮件处理失败:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 计算邮件相关性评分
   * @param {Object} emailData - 邮件数据
   * @param {string} targetEmail - 目标邮箱
   * @returns {number} 相关性评分
   */
  calculateRelevanceScore(emailData, targetEmail) {
    let score = 0;
    const emailUser = targetEmail.split('@')[0];
    const emailDomain = targetEmail.split('@')[1];

    // 收件人匹配
    if (emailData.to.includes(targetEmail)) score += 50;
    else if (emailData.to.includes(emailUser)) score += 30;

    // 抄送匹配
    if (emailData.cc.includes(targetEmail)) score += 40;
    else if (emailData.cc.includes(emailUser)) score += 25;

    // 密送匹配
    if (emailData.bcc.includes(targetEmail)) score += 40;
    else if (emailData.bcc.includes(emailUser)) score += 25;

    // 邮件内容匹配
    const content = (emailData.text + emailData.html).toLowerCase();
    if (content.includes(targetEmail.toLowerCase())) score += 20;
    else if (content.includes(emailUser.toLowerCase())) score += 10;

    // 主题匹配
    const subject = emailData.subject.toLowerCase();
    if (subject.includes(targetEmail.toLowerCase())) score += 15;
    else if (subject.includes(emailUser.toLowerCase())) score += 8;

    // 发件人域名匹配
    const fromDomain = emailData.from.split('@')[1];
    if (fromDomain && (fromDomain.includes('microsoft') || fromDomain.includes('outlook'))) {
      score += 5;
    }

    // 验证相关关键词
    const verificationKeywords = ['verification', 'verify', '验证', 'security', 'code'];
    for (const keyword of verificationKeywords) {
      if (content.includes(keyword) || subject.includes(keyword)) {
        score += 3;
        break;
      }
    }

    return score;
  }

  /**
   * 提取验证码
   * @param {Object} emailData - 邮件数据
   * @returns {string|null} 验证码
   */
  extractVerificationCode(emailData) {
    const content = emailData.text + ' ' + emailData.html + ' ' + emailData.subject;
    
    for (const pattern of this.verificationCodePatterns) {
      const matches = content.match(pattern);
      if (matches) {
        // 提取6位数字
        for (const match of matches) {
          const code = match.replace(/\D/g, ''); // 移除非数字字符
          if (code.length === 6) {
            console.log(`✅ 提取到验证码: ${code}`);
            return code;
          }
        }
      }
    }

    console.log('❌ 未找到验证码');
    return null;
  }
}

/**
 * 增强版IMAP服务管理器
 * 统一管理IMAP操作和资源
 */
class EnhancedImapService {
  constructor(config) {
    this.config = config;
    this.connectionPool = new ImapConnectionPool(config);
    this.emailProcessor = new EmailProcessor();
    this.verificationCodes = new Map(); // email -> {code, timestamp, email}
    this.emailCache = new Map(); // email -> emails[]
    this.activeRequests = new Map(); // email -> request info
    this.cleanupInterval = null;
    
    this.startCleanupTimer();
  }

  /**
   * 获取验证码
   * @param {string} email - 邮箱地址
   * @param {number} sinceTimestamp - 时间过滤
   * @returns {Promise<Object>} 验证码结果
   */
  async getVerificationCode(email, sinceTimestamp = null) {
    console.log(`🔍 获取验证码: ${email}`);
    
    // 检查缓存
    const cached = this.verificationCodes.get(email);
    if (cached && this.isValidCachedCode(cached, sinceTimestamp)) {
      console.log(`✅ 使用缓存的验证码: ${cached.code}`);
      return {
        success: true,
        code: cached.code,
        source: 'cache',
        timestamp: cached.timestamp
      };
    }

    // 检查是否已有活跃请求
    if (this.activeRequests.has(email)) {
      console.log(`⏳ 已有活跃请求，等待结果: ${email}`);
      return this.waitForActiveRequest(email);
    }

    // 创建新请求
    return this.createNewRequest(email, sinceTimestamp);
  }

  /**
   * 检查缓存验证码是否有效
   * @param {Object} cached - 缓存的验证码
   * @param {number} sinceTimestamp - 时间过滤
   * @returns {boolean} 是否有效
   */
  isValidCachedCode(cached, sinceTimestamp) {
    const now = Date.now();
    const codeAge = now - cached.timestamp;
    const maxAge = 5 * 60 * 1000; // 5分钟有效期

    if (codeAge > maxAge) {
      console.log('⏰ 缓存验证码已过期');
      return false;
    }

    if (sinceTimestamp && cached.timestamp < sinceTimestamp) {
      console.log('⏰ 缓存验证码时间不符合要求');
      return false;
    }

    return true;
  }

  /**
   * 等待活跃请求完成
   * @param {string} email - 邮箱地址
   * @returns {Promise<Object>} 请求结果
   */
  async waitForActiveRequest(email) {
    const request = this.activeRequests.get(email);
    if (request && request.promise) {
      return request.promise;
    }
    
    // 如果没有promise，创建新请求
    return this.createNewRequest(email);
  }

  /**
   * 创建新的验证码请求
   * @param {string} email - 邮箱地址
   * @param {number} sinceTimestamp - 时间过滤
   * @returns {Promise<Object>} 请求结果
   */
  async createNewRequest(email, sinceTimestamp = null) {
    const requestInfo = {
      email,
      startTime: Date.now(),
      sinceTimestamp,
      attempts: 0,
      maxAttempts: 3
    };

    // 创建请求promise
    const promise = this.executeRequest(requestInfo);
    requestInfo.promise = promise;
    
    // 记录活跃请求
    this.activeRequests.set(email, requestInfo);

    try {
      const result = await promise;
      return result;
    } finally {
      // 清理活跃请求
      this.activeRequests.delete(email);
    }
  }

  /**
   * 执行验证码请求
   * @param {Object} requestInfo - 请求信息
   * @returns {Promise<Object>} 执行结果
   */
  async executeRequest(requestInfo) {
    const { email, sinceTimestamp, maxAttempts } = requestInfo;
    
    for (let attempt = 0; attempt < maxAttempts; attempt++) {
      requestInfo.attempts = attempt + 1;
      console.log(`🔍 尝试获取验证码 ${attempt + 1}/${maxAttempts}: ${email}`);

      try {
        const result = await this.checkEmailForCode(email, sinceTimestamp);
        if (result.success && result.code) {
          // 缓存验证码
          this.verificationCodes.set(email, {
            code: result.code,
            timestamp: Date.now(),
            email: result.email,
            relevanceScore: result.relevanceScore
          });

          console.log(`✅ 获取验证码成功: ${result.code}`);
          return result;
        }
      } catch (error) {
        console.error(`❌ 获取验证码失败 (尝试 ${attempt + 1}):`, error);
      }

      // 等待后重试
      if (attempt < maxAttempts - 1) {
        const waitTime = (attempt + 1) * 2000; // 递增等待时间
        console.log(`⏳ 等待 ${waitTime}ms 后重试...`);
        await this.wait(waitTime);
      }
    }

    console.log(`❌ 获取验证码失败，已达到最大尝试次数: ${email}`);
    return {
      success: false,
      error: '获取验证码失败',
      attempts: maxAttempts
    };
  }

  /**
   * 等待指定时间
   * @param {number} ms - 等待毫秒数
   */
  async wait(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 启动清理定时器
   */
  startCleanupTimer() {
    // 每5分钟清理一次过期数据
    this.cleanupInterval = setInterval(() => {
      this.cleanupExpiredData();
    }, 5 * 60 * 1000);
  }

  /**
   * 清理过期数据
   */
  cleanupExpiredData() {
    const now = Date.now();
    const maxAge = 10 * 60 * 1000; // 10分钟

    // 清理过期验证码
    for (const [email, data] of this.verificationCodes) {
      if (now - data.timestamp > maxAge) {
        this.verificationCodes.delete(email);
        console.log(`🧹 清理过期验证码: ${email}`);
      }
    }

    // 清理过期邮件缓存
    for (const [email, emails] of this.emailCache) {
      const validEmails = emails.filter(e => now - e.timestamp < maxAge);
      if (validEmails.length !== emails.length) {
        this.emailCache.set(email, validEmails);
        console.log(`🧹 清理过期邮件缓存: ${email}`);
      }
    }
  }

  /**
   * 检查邮件获取验证码
   * @param {string} email - 邮箱地址
   * @param {number} sinceTimestamp - 时间过滤
   * @returns {Promise<Object>} 检查结果
   */
  async checkEmailForCode(email, sinceTimestamp = null) {
    try {
      const connection = await this.connectionPool.getConnection(email);

      return new Promise((resolve, reject) => {
        let isResolved = false;

        // 设置超时
        const timeout = setTimeout(() => {
          if (!isResolved) {
            isResolved = true;
            reject(new Error('邮件检查超时'));
          }
        }, 30000);

        connection.openBox('INBOX', false, (err, box) => {
          if (err) {
            clearTimeout(timeout);
            if (!isResolved) {
              isResolved = true;
              reject(err);
            }
            return;
          }

          console.log(`📬 打开邮箱成功，邮件数量: ${box.messages.total}`);

          // 构建搜索条件
          const searchCriteria = this.buildSearchCriteria(email, sinceTimestamp);

          connection.search(searchCriteria, (err, results) => {
            if (err) {
              clearTimeout(timeout);
              if (!isResolved) {
                isResolved = true;
                reject(err);
              }
              return;
            }

            if (!results || results.length === 0) {
              clearTimeout(timeout);
              if (!isResolved) {
                isResolved = true;
                resolve({
                  success: false,
                  message: '未找到相关邮件'
                });
              }
              return;
            }

            console.log(`📧 找到 ${results.length} 封相关邮件`);
            this.processSearchResults(connection, results, email, (result) => {
              clearTimeout(timeout);
              if (!isResolved) {
                isResolved = true;
                resolve(result);
              }
            });
          });
        });
      });

    } catch (error) {
      console.error('❌ 邮件检查失败:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 构建搜索条件
   * @param {string} email - 邮箱地址
   * @param {number} sinceTimestamp - 时间过滤
   * @returns {Array} 搜索条件
   */
  buildSearchCriteria(email, sinceTimestamp = null) {
    const criteria = ['UNSEEN']; // 只搜索未读邮件

    // 添加时间过滤
    if (sinceTimestamp) {
      const sinceDate = new Date(sinceTimestamp);
      criteria.push(['SINCE', sinceDate]);
    }

    // 添加收件人过滤
    const emailUser = email.split('@')[0];
    criteria.push(['OR',
      ['TO', email],
      ['TO', emailUser]
    ]);

    console.log('🔍 搜索条件:', criteria);
    return criteria;
  }

  /**
   * 处理搜索结果
   * @param {Imap} connection - IMAP连接
   * @param {Array} results - 搜索结果
   * @param {string} targetEmail - 目标邮箱
   * @param {Function} callback - 回调函数
   */
  processSearchResults(connection, results, targetEmail, callback) {
    const fetch = connection.fetch(results, { bodies: '', markSeen: false });
    const emails = [];
    let processedCount = 0;

    fetch.on('message', (msg, seqno) => {
      let emailBuffer = Buffer.alloc(0);

      msg.on('body', (stream, info) => {
        stream.on('data', (chunk) => {
          emailBuffer = Buffer.concat([emailBuffer, chunk]);
        });

        stream.once('end', async () => {
          try {
            const processed = await this.emailProcessor.processEmail(emailBuffer, targetEmail);
            if (processed.success && processed.isRelevant) {
              emails.push({
                seqno,
                ...processed,
                timestamp: Date.now()
              });
            }
          } catch (error) {
            console.error(`❌ 处理邮件失败 (seqno: ${seqno}):`, error);
          }

          processedCount++;
          if (processedCount === results.length) {
            this.handleProcessedEmails(emails, targetEmail, callback);
          }
        });
      });

      msg.once('error', (err) => {
        console.error(`❌ 邮件获取失败 (seqno: ${seqno}):`, err);
        processedCount++;
        if (processedCount === results.length) {
          this.handleProcessedEmails(emails, targetEmail, callback);
        }
      });
    });

    fetch.once('error', (err) => {
      console.error('❌ 邮件获取失败:', err);
      callback({
        success: false,
        error: err.message
      });
    });

    fetch.once('end', () => {
      console.log('📧 邮件获取完成');
    });
  }

  /**
   * 处理已处理的邮件
   * @param {Array} emails - 邮件数组
   * @param {string} targetEmail - 目标邮箱
   * @param {Function} callback - 回调函数
   */
  handleProcessedEmails(emails, targetEmail, callback) {
    if (emails.length === 0) {
      callback({
        success: false,
        message: '未找到相关验证码邮件'
      });
      return;
    }

    // 按相关性评分排序
    emails.sort((a, b) => b.relevanceScore - a.relevanceScore);

    // 查找包含验证码的邮件
    for (const email of emails) {
      if (email.verificationCode) {
        console.log(`✅ 找到验证码邮件，相关性评分: ${email.relevanceScore}`);

        // 缓存邮件
        this.cacheEmail(targetEmail, email);

        callback({
          success: true,
          code: email.verificationCode,
          email: email.email,
          relevanceScore: email.relevanceScore,
          seqno: email.seqno
        });
        return;
      }
    }

    // 没有找到验证码
    callback({
      success: false,
      message: '邮件中未找到验证码',
      emailCount: emails.length
    });
  }

  /**
   * 缓存邮件
   * @param {string} email - 邮箱地址
   * @param {Object} emailData - 邮件数据
   */
  cacheEmail(email, emailData) {
    if (!this.emailCache.has(email)) {
      this.emailCache.set(email, []);
    }

    const cached = this.emailCache.get(email);
    cached.push(emailData);

    // 限制缓存大小
    if (cached.length > 10) {
      cached.shift();
    }
  }

  /**
   * 删除验证码邮件
   * @param {string} email - 邮箱地址
   * @returns {Promise<Object>} 删除结果
   */
  async deleteVerificationEmails(email) {
    try {
      const connection = await this.connectionPool.getConnection(email);

      return new Promise((resolve, reject) => {
        connection.openBox('INBOX', false, (err, box) => {
          if (err) {
            reject(err);
            return;
          }

          // 搜索验证码相关邮件
          const searchCriteria = [
            ['OR',
              ['SUBJECT', 'verification'],
              ['SUBJECT', '验证']
            ],
            ['TO', email]
          ];

          connection.search(searchCriteria, (err, results) => {
            if (err) {
              reject(err);
              return;
            }

            if (!results || results.length === 0) {
              resolve({
                success: true,
                deleted: 0,
                message: '没有找到需要删除的验证码邮件'
              });
              return;
            }

            // 标记为删除
            connection.addFlags(results, '\\Deleted', (err) => {
              if (err) {
                reject(err);
                return;
              }

              // 执行删除
              connection.expunge((err) => {
                if (err) {
                  reject(err);
                  return;
                }

                console.log(`🗑️ 删除了 ${results.length} 封验证码邮件`);
                resolve({
                  success: true,
                  deleted: results.length,
                  message: `成功删除 ${results.length} 封验证码邮件`
                });
              });
            });
          });
        });
      });

    } catch (error) {
      console.error('❌ 删除邮件失败:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 清除指定邮箱的验证码缓存
   * @param {string} email - 邮箱地址
   * @returns {Object} 清理结果
   */
  clearVerificationCodeCache(email) {
    const hadCache = this.verificationCodes.has(email);
    this.verificationCodes.delete(email);

    console.log(`🧹 清理验证码缓存: ${email} (${hadCache ? '有缓存' : '无缓存'})`);

    return {
      success: true,
      hadCache,
      message: hadCache ? '缓存已清理' : '无缓存需要清理'
    };
  }

  /**
   * 销毁服务
   */
  destroy() {
    console.log('🧹 销毁IMAP服务...');

    // 清理定时器
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
    }

    // 关闭所有连接
    this.connectionPool.closeAllConnections();

    // 清理缓存
    this.verificationCodes.clear();
    this.emailCache.clear();
    this.activeRequests.clear();
  }
}

// 导出服务
module.exports = {
  ImapConnectionPool,
  EmailProcessor,
  EnhancedImapService
};
